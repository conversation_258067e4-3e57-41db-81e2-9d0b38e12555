name: release_docker

on:
  push:
    tags:
      - 'v*'
    branches:
      - main
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  REGISTRY: 'xhofe/alist'
  REGISTRY_USERNAME: 'xhofe'
  REGISTRY_PASSWORD: ${{ secrets.DOCKERHUB_TOKEN }}
  ARTIFACT_NAME: 'binaries_docker_release'
  RELEASE_PLATFORMS: 'linux/amd64,linux/arm64,linux/arm/v7,linux/386,linux/arm/v6,linux/s390x,linux/ppc64le,linux/riscv64'
  IMAGE_PUSH: ${{ github.event_name == 'push' }}
  IMAGE_IS_PROD: ${{ github.ref_type == 'tag' }}
  IMAGE_TAGS_BETA: |
    type=schedule
    type=ref,event=branch
    type=ref,event=tag
    type=ref,event=pr
    type=raw,value=beta,enable={{is_default_branch}}

jobs:
  build_binary:
    name: Build Binaries for Docker Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: 'stable'

      - name: Cache Musl
        id: cache-musl
        uses: actions/cache@v4
        with:
          path: build/musl-libs
          key: docker-musl-libs-v2

      - name: Download Musl Library
        if: steps.cache-musl.outputs.cache-hit != 'true'
        run: bash build.sh prepare docker-multiplatform

      - name: Build go binary (beta)
        if: env.IMAGE_IS_PROD != 'true'
        run: bash build.sh beta docker-multiplatform

      - name: Build go binary (release)
        if: env.IMAGE_IS_PROD == 'true'
        run: bash build.sh release docker-multiplatform

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.ARTIFACT_NAME }}
          overwrite: true
          path: |
            build/
            !build/*.tgz
            !build/musl-libs/**

  release_docker:
    needs: build_binary
    name: Release Docker image
    runs-on: ubuntu-latest
    strategy:
      matrix:
        image: ["latest", "ffmpeg", "aria2", "aio"]
        include:
          - image: "latest"
            build_arg: ""
            tag_favor: ""
          - image: "ffmpeg"
            build_arg: INSTALL_FFMPEG=true
            tag_favor: "suffix=-ffmpeg,onlatest=true"
          - image: "aria2"
            build_arg: INSTALL_ARIA2=true
            tag_favor: "suffix=-aria2,onlatest=true"
          - image: "aio"
            build_arg: |
              INSTALL_FFMPEG=true
              INSTALL_ARIA2=true
            tag_favor: "suffix=-aio,onlatest=true"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: 'build/'

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        if: env.IMAGE_PUSH == 'true'
        uses: docker/login-action@v3
        with:
          logout: true
          username: ${{ env.REGISTRY_USERNAME }}
          password: ${{ env.REGISTRY_PASSWORD }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}
          tags: ${{ env.IMAGE_IS_PROD == 'true' && '' || env.IMAGE_TAGS_BETA }}
          flavor: |
            ${{ env.IMAGE_IS_PROD == 'true' && 'latest=true' || '' }}
            ${{ matrix.tag_favor }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile.ci
          push: ${{ env.IMAGE_PUSH == 'true' }}
          build-args: ${{ matrix.build_arg }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          platforms: ${{ env.RELEASE_PLATFORMS }}