name: auto_lang

on:
  push:
    branches:
      - 'main'
    paths:
      - 'drivers/**'
      - 'internal/bootstrap/data/setting.go'
      - 'internal/conf/const.go'
      - 'cmd/lang.go'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  auto_lang:
    strategy:
      matrix:
        platform: [ ubuntu-latest ]
        go-version: [ '1.21' ]
    name: auto generate lang.json
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Setup go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go-version }}

      - name: Checkout alist
        uses: actions/checkout@v4
        with:
          path: alist

      - name: Checkout alist-web
        uses: actions/checkout@v4
        with:
          repository: 'alist-org/alist-web'
          ref: main
          persist-credentials: false
          fetch-depth: 0
          path: alist-web

      - name: Generate lang
        run: |
          cd alist
          go run ./main.go lang
          cd ..

      - name: Copy lang file
        run: |
          cp -f ./alist/lang/*.json ./alist-web/src/lang/en/ 2>/dev/null || :

      - name: Commit git
        run: |
          cd alist-web
          git add .
          git config --local user.email "<EMAIL>"
          git config --local user.name "IlaBot"
          git commit -m "chore: auto update i18n file" -a 2>/dev/null || :
          cd ..

      - name: Push lang files
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.MY_TOKEN }}
          branch: main
          directory: alist-web
          repository: alist-org/alist-web
