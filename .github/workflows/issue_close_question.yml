name: Close need info

on:
  schedule:
    - cron: "0 0 */1 * *"
  workflow_dispatch:

jobs:
  close-need-info:
    runs-on: ubuntu-latest
    steps:
      - name: close-issues
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'close-issues'
          token: ${{ secrets.GITHUB_TOKEN }}
          labels: 'question'
          inactive-day: 3
          close-reason: 'not_planned'
          body: |
            Hello @${{ github.event.issue.user.login }}, this issue was closed due to no activities in 3 days.
            你好 @${{ github.event.issue.user.login }}，此issue因超过3天未回复被关闭。