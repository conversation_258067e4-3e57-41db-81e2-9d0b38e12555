name: auto changelog

on:
  push:
    tags:
      - 'v*'

jobs:
  changelog:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Delete beta tag
        run: git tag -d beta
        continue-on-error: true

      - run: npx changelogithub # or changelogithub@0.12 if ensure the stable result
        env:
          GITHUB_TOKEN: ${{secrets.MY_TOKEN}}
