name: beta release

on:
  push:
    branches: [ 'main' ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: write

jobs:
  changelog:
    strategy:
      matrix:
        platform: [ ubuntu-latest ]
        go-version: [ '1.21' ]
    name: Beta Release Changelog
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Create or update ref
        id: create-or-update-ref
        uses: ovsds/create-or-update-ref-action@v1
        with:
          ref: tags/beta
          sha: ${{ github.sha }}

      - name: Delete beta tag
        run: git tag -d beta
        continue-on-error: true

      - name: changelog # or changelogithub@0.12 if ensure the stable result
        id: changelog
        run: |
          git tag -l
          npx changelogithub --output CHANGELOG.md
#          npx changelogen@latest --output CHANGELOG.md

      - name: Upload assets
        uses: softprops/action-gh-release@v2
        with:
          body_path: CHANGELOG.md
          files: CHANGELOG.md
          prerelease: true
          tag_name: beta

  release:
    needs:
      - changelog
    strategy:
      matrix:
        include:
          - target: '!(*musl*|*windows-arm64*|*android*|*freebsd*)' # xgo
            hash: "md5"
          - target: 'linux-!(arm*)-musl*' #musl-not-arm
            hash: "md5-linux-musl"
          - target: 'linux-arm*-musl*' #musl-arm
            hash: "md5-linux-musl-arm"
          - target: 'windows-arm64' #win-arm64
            hash: "md5-windows-arm64"
          - target: 'android-*' #android
            hash: "md5-android"
          - target: 'freebsd-*' #freebsd
            hash: "md5-freebsd"

    name: Beta Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      - name: Setup web
        run: bash build.sh dev web

      - name: Build
        uses: go-cross/cgo-actions@v1
        with:
          targets: ${{ matrix.target }}
          musl-target-format: $os-$musl-$arch
          out-dir: build
          x-flags: |
            github.com/alist-org/alist/v3/internal/conf.BuiltAt=$built_at
            github.com/alist-org/alist/v3/internal/conf.GitAuthor=Xhofe
            github.com/alist-org/alist/v3/internal/conf.GitCommit=$git_commit
            github.com/alist-org/alist/v3/internal/conf.Version=$tag
            github.com/alist-org/alist/v3/internal/conf.WebVersion=dev

      - name: Compress
        run: |
          bash build.sh zip ${{ matrix.hash }}
          
      - name: Upload assets
        uses: softprops/action-gh-release@v2
        with:
          files: build/compress/*
          prerelease: true
          tag_name: beta
          
  desktop:
    needs:
      - release
    name: Beta Release Desktop
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          repository: alist-org/desktop-release
          ref: main
          persist-credentials: false
          fetch-depth: 0

      - name: Commit
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "IlaBot"
          git commit --allow-empty -m "Trigger build for ${{ github.sha }}"

      - name: Push commit
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.MY_TOKEN }}
          branch: main
          repository: alist-org/desktop-release