name: build

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ 'main' ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest]
        target: 
          - darwin-amd64
          - darwin-arm64
          - windows-amd64
          - linux-arm64-musl
          - linux-amd64-musl
          - windows-arm64
          - android-arm64
    name: Build
    runs-on: ${{ matrix.platform }}
    steps:

      - name: Checkout
        uses: actions/checkout@v4

      - uses: benjlevesque/short-sha@v3.0
        id: short-sha

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      - name: Setup web
        run: bash build.sh dev web

      - name: Build
        uses: go-cross/cgo-actions@v1
        with:
          targets: ${{ matrix.target }}
          musl-target-format: $os-$musl-$arch
          out-dir: build
          x-flags: |
            github.com/alist-org/alist/v3/internal/conf.BuiltAt=$built_at
            github.com/alist-org/alist/v3/internal/conf.GitAuthor=Xhofe
            github.com/alist-org/alist/v3/internal/conf.GitCommit=$git_commit
            github.com/alist-org/alist/v3/internal/conf.Version=$tag
            github.com/alist-org/alist/v3/internal/conf.WebVersion=dev

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: alist_${{ env.SHA }}_${{ matrix.target }}
          path: build/*