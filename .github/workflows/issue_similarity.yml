name: Issues Similarity Analysis

on:
  issues:
    types: [opened, edited]

jobs:
  similarity-analysis:
    runs-on: ubuntu-latest
    steps:
      - name: analysis
        uses: actions-cool/issues-similarity-analysis@v1
        with:
          filter-threshold: 0.5
          comment-title: '### See'
          comment-body: '${index}. ${similarity} #${number}'
          show-footer: false
          show-mentioned: true
          since-days: 730