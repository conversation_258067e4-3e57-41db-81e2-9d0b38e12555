name: "Bug report"
description: Bug report
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report, please **confirm that your issue is not a duplicate issue and not because of your operation or version issues**
        感谢您花时间填写此错误报告，请**务必确认您的issue不是重复的且不是因为您的操作或版本问题**

  - type: checkboxes
    attributes:
      label: Please make sure of the following things
      description: |
        You must check all the following, otherwise your issue may be closed directly. Or you can go to the [discussions](https://github.com/alist-org/alist/discussions)
        您必须勾选以下所有内容，否则您的issue可能会被直接关闭。或者您可以去[讨论区](https://github.com/alist-org/alist/discussions)
      options:
        - label: |
            I have read the [documentation](https://alist.nn.ci).
            我已经阅读了[文档](https://alist.nn.ci)。
        - label: |
            I'm sure there are no duplicate issues or discussions.
            我确定没有重复的issue或讨论。
        - label: |
            I'm sure it's due to `AList` and not something else(such as [Network](https://alist.nn.ci/faq/howto.html#tls-handshake-timeout-read-connection-reset-by-peer-dns-lookup-failed-connect-connection-refused-client-timeout-exceeded-while-awaiting-headers-no-such-host) ,`Dependencies` or `Operational`).
            我确定是`AList`的问题，而不是其他原因（例如[网络](https://alist.nn.ci/zh/faq/howto.html#tls-handshake-timeout-read-connection-reset-by-peer-dns-lookup-failed-connect-connection-refused-client-timeout-exceeded-while-awaiting-headers-no-such-host)，`依赖`或`操作`）。
        - label: |
            I'm sure this issue is not fixed in the latest version.
            我确定这个问题在最新版本中没有被修复。

  - type: input
    id: version
    attributes:
      label: AList Version / AList 版本
      description: |
        What version of our software are you running? Do not use `latest` or `master` as an answer.
        您使用的是哪个版本的软件？请不要使用`latest`或`master`作为答案。
      placeholder: v3.xx.xx
    validations:
      required: true
  - type: input
    id: driver
    attributes:
      label: Driver used / 使用的存储驱动
      description: |
        What storage driver are you using?
        您使用的是哪个存储驱动？
      placeholder: "for example: Onedrive"
    validations:
      required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug / 问题描述
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Reproduction / 复现链接
      description: |
        Please provide a link to a repo that can reproduce the problem you ran into. Please be aware that your issue may be closed directly if you don't provide it.
        请提供能复现此问题的链接，请知悉如果不提供它你的issue可能会被直接关闭。
    validations:
      required: true
  - type: textarea
    id: config
    attributes:
      label: Config / 配置
      description: |
        Please provide the configuration file of your `AList` application and take a screenshot of the relevant storage configuration. (hide privacy field)
        请提供您的`AList`应用的配置文件，并截图相关存储配置。(隐藏隐私字段)
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Logs / 日志
      description: |
        Please copy and paste any relevant log output.
        请复制粘贴错误日志，或者截图
