# 文件夹直接下载功能实施方案

## 🎯 需求分析

用户希望在下载文件夹时：
- ✅ 保持原始目录结构
- ✅ 不进行ZIP打包
- ✅ 直接下载到本地指定文件夹
- ✅ 支持大文件夹和深层嵌套

## 🚀 技术方案

### 方案一：File System Access API（推荐）

#### 技术特点
- **现代浏览器API**：Chrome 86+、Edge 86+支持
- **原生目录操作**：可直接创建文件夹和写入文件
- **用户体验好**：用户可选择下载位置
- **无需外部工具**：纯前端实现

#### 实现步骤

1. **用户选择下载位置**
```javascript
// 让用户选择下载文件夹
const directoryHandle = await window.showDirectoryPicker({
  mode: 'readwrite'
});
```

2. **递归创建目录结构**
```javascript
async function createDirectoryStructure(dirHandle, path) {
  const parts = path.split('/').filter(Boolean);
  let currentHandle = dirHandle;
  
  for (const part of parts) {
    try {
      currentHandle = await currentHandle.getDirectoryHandle(part, {
        create: true
      });
    } catch (error) {
      console.error(`Failed to create directory: ${part}`, error);
      throw error;
    }
  }
  
  return currentHandle;
}
```

3. **下载并保存文件**
```javascript
async function downloadAndSaveFile(fileHandle, downloadUrl, fileName) {
  try {
    // 下载文件
    const response = await fetch(downloadUrl);
    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    
    // 创建文件
    const writable = await fileHandle.createWritable();
    
    // 流式写入
    await response.body.pipeTo(writable);
    
    return true;
  } catch (error) {
    console.error(`Failed to download file: ${fileName}`, error);
    return false;
  }
}
```

#### 完整实现代码

```typescript
// DirectFolderDownload.tsx
import { createSignal, For, Show } from "solid-js";
import { Button, Heading, ModalBody, ModalFooter, Text, VStack, Progress } from "@hope-ui/solid";
import { fsList, pathJoin } from "~/utils";
import { password, selectedObjs as _selectedObjs } from "~/store";
import { getLinkByDirAndObj, useRouter, useT } from "~/hooks";
import { Obj } from "~/types";

interface DownloadFile {
  path: string;
  url: string;
  name: string;
  size: number;
}

interface DownloadProgress {
  total: number;
  completed: number;
  current: string;
  failed: string[];
}

const DirectFolderDownload = (props: { onClose: () => void }) => {
  const t = useT();
  const { pathname } = useRouter();
  const selectedObjs = _selectedObjs();
  
  const [progress, setProgress] = createSignal<DownloadProgress>({
    total: 0,
    completed: 0,
    current: '',
    failed: []
  });
  const [status, setStatus] = createSignal<'init' | 'selecting' | 'downloading' | 'completed' | 'error'>('init');
  const [errorMessage, setErrorMessage] = createSignal('');

  // 检查浏览器支持
  const isSupported = () => {
    return 'showDirectoryPicker' in window;
  };

  // 递归获取文件夹结构
  const fetchFolderStructure = async (
    pre: string,
    obj: Obj
  ): Promise<DownloadFile[] | string> => {
    if (!obj.is_dir) {
      return [{
        path: pathJoin(pre, obj.name),
        url: getLinkByDirAndObj(
          pathJoin(pathname(), pre),
          obj,
          "direct",
          true
        ),
        name: obj.name,
        size: obj.size
      }];
    } else {
      const resp = await fsList(pathJoin(pathname(), pre, obj.name), password());
      if (resp.code !== 200) {
        return resp.message;
      }
      
      const res: DownloadFile[] = [];
      for (const _obj of resp.data.content ?? []) {
        const _res = await fetchFolderStructure(pathJoin(pre, obj.name), _obj);
        if (typeof _res === "string") {
          return _res;
        } else {
          res.push(..._res);
        }
      }
      return res;
    }
  };

  // 创建目录结构
  const createDirectoryStructure = async (
    dirHandle: FileSystemDirectoryHandle,
    path: string
  ): Promise<FileSystemDirectoryHandle> => {
    const parts = path.split('/').filter(Boolean);
    let currentHandle = dirHandle;
    
    for (const part of parts) {
      try {
        currentHandle = await currentHandle.getDirectoryHandle(part, {
          create: true
        });
      } catch (error) {
        console.error(`Failed to create directory: ${part}`, error);
        throw error;
      }
    }
    
    return currentHandle;
  };

  // 下载并保存文件
  const downloadAndSaveFile = async (
    dirHandle: FileSystemDirectoryHandle,
    file: DownloadFile
  ): Promise<boolean> => {
    try {
      // 创建目录结构
      const filePath = file.path;
      const dirPath = filePath.substring(0, filePath.lastIndexOf('/'));
      const fileName = file.name;
      
      let targetDirHandle = dirHandle;
      if (dirPath) {
        targetDirHandle = await createDirectoryStructure(dirHandle, dirPath);
      }
      
      // 下载文件
      const response = await fetch(file.url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      // 创建文件
      const fileHandle = await targetDirHandle.getFileHandle(fileName, {
        create: true
      });
      const writable = await fileHandle.createWritable();
      
      // 流式写入
      await response.body?.pipeTo(writable);
      
      return true;
    } catch (error) {
      console.error(`Failed to download file: ${file.name}`, error);
      return false;
    }
  };

  // 开始下载
  const startDownload = async () => {
    if (!isSupported()) {
      setStatus('error');
      setErrorMessage('您的浏览器不支持 File System Access API，请使用 Chrome 86+ 或 Edge 86+');
      return;
    }

    try {
      // 让用户选择下载文件夹
      setStatus('selecting');
      const directoryHandle = await (window as any).showDirectoryPicker({
        mode: 'readwrite'
      });

      setStatus('downloading');
      
      // 获取所有文件
      const allFiles: DownloadFile[] = [];
      for (const obj of selectedObjs) {
        const files = await fetchFolderStructure('', obj);
        if (typeof files === 'string') {
          throw new Error(files);
        }
        allFiles.push(...files);
      }

      setProgress(prev => ({ ...prev, total: allFiles.length }));

      // 下载所有文件
      const failed: string[] = [];
      for (let i = 0; i < allFiles.length; i++) {
        const file = allFiles[i];
        setProgress(prev => ({ ...prev, current: file.name }));
        
        const success = await downloadAndSaveFile(directoryHandle, file);
        if (success) {
          setProgress(prev => ({ ...prev, completed: prev.completed + 1 }));
        } else {
          failed.push(file.name);
        }
      }

      setProgress(prev => ({ ...prev, failed }));
      setStatus('completed');
      
    } catch (error: any) {
      setStatus('error');
      setErrorMessage(error.message || '下载过程中发生错误');
    }
  };

  return (
    <>
      <ModalBody>
        <VStack w="$full" alignItems="start" spacing="$4">
          <Heading size="lg">直接下载文件夹</Heading>
          
          <Show when={!isSupported()}>
            <Text color="$danger9">
              您的浏览器不支持此功能，请使用 Chrome 86+ 或 Edge 86+
            </Text>
          </Show>

          <Show when={status() === 'init' && isSupported()}>
            <VStack spacing="$2" alignItems="start">
              <Text>将要下载 {selectedObjs.length} 个项目，保持原始目录结构</Text>
              <Text fontSize="$sm" color="$neutral11">
                点击开始后，请选择下载到的文件夹位置
              </Text>
            </VStack>
          </Show>

          <Show when={status() === 'selecting'}>
            <Text>请选择下载文件夹...</Text>
          </Show>

          <Show when={status() === 'downloading'}>
            <VStack w="$full" spacing="$2">
              <Text>正在下载: {progress().current}</Text>
              <Progress 
                value={(progress().completed / progress().total) * 100}
                size="lg"
              />
              <Text fontSize="$sm">
                {progress().completed} / {progress().total} 个文件已完成
              </Text>
            </VStack>
          </Show>

          <Show when={status() === 'completed'}>
            <VStack spacing="$2" alignItems="start">
              <Text color="$success9">✅ 下载完成！</Text>
              <Text>成功下载 {progress().completed} 个文件</Text>
              <Show when={progress().failed.length > 0}>
                <Text color="$warning9">
                  失败 {progress().failed.length} 个文件
                </Text>
              </Show>
            </VStack>
          </Show>

          <Show when={status() === 'error'}>
            <Text color="$danger9">❌ {errorMessage()}</Text>
          </Show>
        </VStack>
      </ModalBody>
      
      <ModalFooter>
        <Show when={status() === 'init' && isSupported()}>
          <Button colorScheme="primary" onClick={startDownload}>
            开始下载
          </Button>
        </Show>
        <Button 
          variant={status() === 'downloading' ? 'ghost' : 'outline'} 
          onClick={props.onClose}
          disabled={status() === 'downloading'}
        >
          {status() === 'downloading' ? '下载中...' : '关闭'}
        </Button>
      </ModalFooter>
    </>
  );
};

export default DirectFolderDownload;
```

### 方案二：增强Aria2下载（现有功能优化）

#### 优化点
1. **自动创建目录结构**：在Aria2配置中自动设置正确的目录路径
2. **批量任务管理**：优化任务添加逻辑，减少API调用
3. **进度统一显示**：在AList界面显示Aria2下载进度

### 方案三：浏览器扩展方案

开发Chrome/Edge扩展，利用扩展的文件系统权限：
- 可以绕过浏览器安全限制
- 支持更多浏览器版本
- 需要用户安装扩展

## 📋 实施建议

### 第一阶段：File System Access API实现
1. 在下载菜单中添加"直接下载"选项
2. 实现基本的文件夹下载功能
3. 添加进度显示和错误处理

### 第二阶段：功能完善
1. 支持断点续传
2. 添加下载速度限制
3. 支持选择性下载（用户可选择跳过某些文件）

### 第三阶段：兼容性扩展
1. 为不支持的浏览器提供降级方案
2. 开发浏览器扩展版本
3. 集成到移动端应用

## 🔧 技术要求

- **浏览器支持**：Chrome 86+、Edge 86+
- **权限要求**：用户需要授权文件系统访问
- **网络要求**：支持CORS的下载链接
- **性能考虑**：大文件夹需要流式处理

## 📊 预期效果

- ✅ 完全保持目录结构
- ✅ 用户可选择下载位置  
- ✅ 支持大文件夹下载
- ✅ 实时进度显示
- ✅ 错误处理和重试机制
