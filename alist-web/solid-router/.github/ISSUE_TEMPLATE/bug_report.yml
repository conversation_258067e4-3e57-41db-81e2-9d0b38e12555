name: '🐛 Bug report'
description: Create a report to help us improve
body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting an issue :pray:.

        This issue tracker is for reporting bugs found in `solid-router` (https://github.com/solidjs/solid-router).
        If you have a question about how to achieve something and are struggling, please post a question
        inside of `solid-router` Discussions tab: https://github.com/solidjs/solid-router/discussions

        Before submitting a new bug/issue, please check the links below to see if there is a solution or question posted there already:
         - `solid-router` Issues tab: https://github.com/solidjs/solid-router/issues?q=is%3Aissue+is%3Aopen+sort%3Aupdated-desc
         - `solid-router` closed issues tab: https://github.com/solidjs/solid-router/issues?q=is%3Aissue+sort%3Aupdated-desc+is%3Aclosed
         - `solid-router` Discussions tab: https://github.com/solidjs/solid-router/discussions

        The more information you fill in, the better the community can help you.
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: Provide a clear and concise description of the challenge you are running into.
    validations:
      required: true
  - type: input
    id: link
    attributes:
      label: Your Example Website or App
      description: |
        Which website or app were you using when the bug happened?
        Note:
        - Your bug will may get fixed much faster if we can run your code and it doesn't have dependencies other than the solid-js  npm package.
        - To create a shareable code example you can use Stackblitz (https://stackblitz.com/). Please no localhost URLs.
        - Please read these tips for providing a minimal example: https://stackoverflow.com/help/mcve.
      placeholder: |
        e.g. https://stackblitz.com/edit/...... OR Github Repo
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce the Bug or Issue
      description: Describe the steps we have to take to reproduce the behavior.
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: Provide a clear and concise description of what you expected to happen.
      placeholder: |
        As a user, I expected ___ behavior but i am seeing ___
    validations:
      required: true
  - type: textarea
    id: screenshots_or_videos
    attributes:
      label: Screenshots or Videos
      description: |
        If applicable, add screenshots or a video to help explain your problem.
        For more information on the supported file image/file types and the file size limits, please refer
        to the following link: https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/attaching-files
      placeholder: |
        You can drag your video or image files inside of this editor ↓
  - type: textarea
    id: platform
    attributes:
      label: Platform
      value: |
        - OS: [e.g. macOS, Windows, Linux]
        - Browser: [e.g. Chrome, Safari, Firefox]
        - Version: [e.g. 91.1]
    validations:
      required: true
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
