lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@egjs/view360':
        specifier: 4.0.0-beta.7
        version: 4.0.0-beta.7
      '@github/webauthn-json':
        specifier: ^2.1.1
        version: 2.1.1
      '@hope-ui/solid':
        specifier: 0.6.7
        version: 0.6.7(@stitches/core@1.2.8)(solid-js@1.4.8)(solid-transition-group@0.0.12(solid-js@1.4.8))
      '@monaco-editor/loader':
        specifier: ^1.4.0
        version: 1.4.0(monaco-editor@0.50.0)
      '@motionone/solid':
        specifier: ^10.14.1
        version: 10.14.1(solid-js@1.4.8)
      '@solid-primitives/i18n':
        specifier: ^1.1.0
        version: 1.1.0(solid-js@1.4.8)
      '@solid-primitives/keyboard':
        specifier: ^1.2.5
        version: 1.2.5(solid-js@1.4.8)
      '@solid-primitives/storage':
        specifier: ^1.3.1
        version: 1.3.1(solid-js@1.4.8)
      '@stitches/core':
        specifier: ^1.2.8
        version: 1.2.8
      '@viselect/vanilla':
        specifier: ^3.5.0
        version: 3.5.0
      aplayer:
        specifier: ^1.10.1
        version: 1.10.1
      artplayer:
        specifier: ^5.2.2
        version: 5.2.2
      artplayer-plugin-danmuku:
        specifier: ^5.1.5
        version: 5.1.5
      asciinema-player:
        specifier: ^3.6.3
        version: 3.6.3
      axios:
        specifier: ^1.0.0
        version: 1.1.3
      bencode:
        specifier: ^4.0.0
        version: 4.0.0
      chardet:
        specifier: ^2.0.0
        version: 2.0.0
      clsx:
        specifier: ^2.0.0
        version: 2.0.0
      copy-to-clipboard:
        specifier: ^3.3.2
        version: 3.3.2
      crypto-js:
        specifier: ^4.2.0
        version: 4.2.0
      hash-wasm:
        specifier: ^4.12.0
        version: 4.12.0
      hls.js:
        specifier: ^1.6.1
        version: 1.6.1
      just-once:
        specifier: ^2.2.0
        version: 2.2.0
      libass-wasm:
        specifier: ^4.1.0
        version: 4.1.0
      lightgallery:
        specifier: ^2.5.0
        version: 2.5.0
      mark.js:
        specifier: ^8.11.1
        version: 8.11.1
      mitt:
        specifier: ^3.0.0
        version: 3.0.0
      mpegts.js:
        specifier: ^1.8.0
        version: 1.8.0
      qrcode:
        specifier: ^1.5.4
        version: 1.5.4
      rehype-katex:
        specifier: ^6.0.3
        version: 6.0.3
      rehype-raw:
        specifier: ^6.1.1
        version: 6.1.1
      remark-gfm:
        specifier: ^3.0.1
        version: 3.0.1
      remark-math:
        specifier: ^5.1.1
        version: 5.1.1
      seemly:
        specifier: ^0.3.6
        version: 0.3.6
      sha256:
        specifier: ^0.2.0
        version: 0.2.0
      solid-contextmenu:
        specifier: 0.0.2
        version: 0.0.2(solid-js@1.4.8)(solid-transition-group@0.0.12(solid-js@1.4.8))
      solid-icons:
        specifier: ^1.0.1
        version: 1.0.1(solid-js@1.4.8)
      solid-js:
        specifier: ^1.4.8
        version: 1.4.8
      solid-markdown:
        specifier: ^1.2.0
        version: 1.2.0(solid-js@1.4.8)
      solid-transition-group:
        specifier: ^0.0.12
        version: 0.0.12(solid-js@1.4.8)
      streamsaver:
        specifier: ^2.0.6
        version: 2.0.6
      typescript-natural-sort:
        specifier: ^0.7.2
        version: 0.7.2
    devDependencies:
      '@crowdin/cli':
        specifier: ^3.7.10
        version: 3.7.10
      '@hrgui/libass-wasm-ts':
        specifier: ^1.0.3
        version: 1.0.3
      '@types/bencode':
        specifier: ^2.0.4
        version: 2.0.4
      '@types/crypto-js':
        specifier: ^4.2.2
        version: 4.2.2
      '@types/mark.js':
        specifier: ^8.11.8
        version: 8.11.8
      '@types/node':
        specifier: ^20.0.0
        version: 20.0.0
      '@types/qrcode':
        specifier: ^1.5.5
        version: 1.5.5
      '@types/sha256':
        specifier: ^0.2.0
        version: 0.2.0
      '@types/streamsaver':
        specifier: ^2.0.1
        version: 2.0.1
      '@vitejs/plugin-legacy':
        specifier: ^2.0.1
        version: 2.0.1(terser@5.14.2)(vite@4.5.13(@types/node@20.0.0)(terser@5.14.2))
      husky:
        specifier: ^8.0.2
        version: 8.0.2
      lint-staged:
        specifier: ^13.0.4
        version: 13.0.4
      prettier:
        specifier: 3.1.1
        version: 3.1.1
      rollup-plugin-copy:
        specifier: ^3.5.0
        version: 3.5.0
      terser:
        specifier: ^5.14.2
        version: 5.14.2
      typescript:
        specifier: ^4.7.4
        version: 4.7.4
      vite:
        specifier: ^4.5.13
        version: 4.5.13(@types/node@20.0.0)(terser@5.14.2)
      vite-plugin-dynamic-base:
        specifier: ^0.4.4
        version: 0.4.4
      vite-plugin-solid:
        specifier: ^2.3.0
        version: 2.3.0(solid-js@1.4.8)(vite@4.5.13(@types/node@20.0.0)(terser@5.14.2))

packages:

  '@ampproject/remapping@2.2.0':
    resolution: {integrity: sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.18.6':
    resolution: {integrity: sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.18.8':
    resolution: {integrity: sha512-HSmX4WZPPK3FUxYp7g2T6EyO8j96HlZJlxmKPSh6KAcqwyDrfx7hKjXpAW/0FhFfTJsR0Yt4lAjLI2coMptIHQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.18.10':
    resolution: {integrity: sha512-JQM6k6ENcBFKVtWvLavlvi/mPcpYZ3+R+2EySDEMSMbp7Mn4FexlbbJVrx2R7Ijhr01T8gyqrOaABWIOgxeUyw==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.18.12':
    resolution: {integrity: sha512-dfQ8ebCN98SvyL7IxNMCUtZQSq5R7kxgN+r8qYTGDmmSion1hX2C0zq2yo1bsCDhXixokv1SAWTZUMYbO/V5zg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.18.6':
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.18.9':
    resolution: {integrity: sha512-tzLCyVmqUiFlcFoAPLA/gL9TeYrF61VLNtb+hvkuVaB5SUjW7jcfrglBIX1vUIoT7CLP3bBlIMeyEsIl2eFQNg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-class-features-plugin@7.18.9':
    resolution: {integrity: sha512-WvypNAYaVh23QcjpMR24CwZY2Nz6hqdOcFdPbNpV56hL5H6KiFheO7Xm1aPdlLQ7d5emYZX7VZwPp9x3z+2opw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-environment-visitor@7.18.9':
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.18.9':
    resolution: {integrity: sha512-fJgWlZt7nxGksJS9a0XdSaI4XvpExnNIgRP+rVefWh5U7BL8pPuir6SJUmFKRfjWQ51OtWSzwOxhaH/EBWWc0A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.18.6':
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.18.9':
    resolution: {integrity: sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.16.0':
    resolution: {integrity: sha512-kkH7sWzKPq0xt3H1n+ghb4xEMP8k0U7XV3kkB+ZGy69kDk2ySFW1qPi06sjKzFY3t1j6XbJSqr4mF9L7CYVyhg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.18.6':
    resolution: {integrity: sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.18.9':
    resolution: {integrity: sha512-KYNqY0ICwfv19b31XzvmI/mfcylOzbLtowkw+mfvGPAQ3kfCnMLYbED3YecL5tPd8nAYFQFAd6JHp2LxZk/J1g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-optimise-call-expression@7.18.6':
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.18.9':
    resolution: {integrity: sha512-aBXPT3bmtLryXaoJLyYPXPlSD4p1ld9aYeR+sJNOZjJJGiOpb+fKfh3NkcCu7J54nUJwCERPBExCCpyCOHnu/w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-replace-supers@7.18.9':
    resolution: {integrity: sha512-dNsWibVI4lNT6HiuOIBr1oyxo40HvIVmbwPUm3XZ7wMh4k2WxrxTqZwSqw/eEmXDS9np0ey5M2bz9tBmO9c+YQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-simple-access@7.18.6':
    resolution: {integrity: sha512-iNpIgTgyAvDQpDj76POqg+YEt8fPxx3yaNBg3S30dxNKm2SWfYhD0TGrK/Eu9wHpUW63VQU894TsTg+GLbUa1g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.18.6':
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.18.10':
    resolution: {integrity: sha512-XtIfWmeNY3i4t7t4D2t02q50HvqHybPqW2ki1kosnvWCwuCMeo81Jf0gwr85jy/neUdg5XDdeFE/80DXiO+njw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.18.6':
    resolution: {integrity: sha512-MmetCkz9ej86nJQV+sFCxoGGrUbU3q02kgLciwkrt9QqEB7cP39oKEY0PakknEO0Gu20SskMRi+AYZ3b1TpN9g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.18.6':
    resolution: {integrity: sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.18.9':
    resolution: {integrity: sha512-Jf5a+rbrLoR4eNdUmnFu8cN5eNJT6qdTdOg5IHIzq87WwyRw9PwguLFOWYgktN/60IP4fgDUawJvs7PjQIzELQ==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.18.6':
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.18.11':
    resolution: {integrity: sha512-9JKn5vN+hDt0Hdqn1PiJ2guflwP+B6Ga8qbDuoF0PzzVhrzsKIJo8yGqVk6CmMHiMei9w1C1Bp9IMJSIK+HPIQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-jsx@7.18.6':
    resolution: {integrity: sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.18.6':
    resolution: {integrity: sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.18.12':
    resolution: {integrity: sha512-2vjjam0cum0miPkenUbQswKowuxs/NjMwIKEq0zwegRxXk12C9YOF9STXnaUptITOtOJHKHpzvvWYOjbm6tc0w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.18.6':
    resolution: {integrity: sha512-s9ik86kXBAnD760aybBucdpnLsAt0jK1xqJn2juOn9lkOvSHV60os5hxoVJsPzMQxvnUJFAlkont2DvvaYEBtQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.23.6':
    resolution: {integrity: sha512-zHd0eUrf5GZoOWVCXp6koAKQTfZV07eit6bGPmJgnZdnSAvvZee6zniW2XMF7Cmc4ISOOnPy3QaSiIJGJkVEDQ==}
    engines: {node: '>=6.9.0'}

  '@babel/standalone@7.18.12':
    resolution: {integrity: sha512-wDh3K5IUJiSMAY0MLYBFoCaj2RCZwvDz5BHn2uHat9KOsGWEVDFgFQFIOO+81Js2phFKNppLC45iOCsZVfJniw==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.18.10':
    resolution: {integrity: sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.18.11':
    resolution: {integrity: sha512-TG9PiM2R/cWCAy6BPJKeHzNbu4lPzOSZpeMfeNErskGpTJx6trEvFaVCbDvpcxwy49BKWmEPwiW8mrysNiDvIQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.18.10':
    resolution: {integrity: sha512-MJvnbEiiNkpjo+LknnmRrqbY1GPUUggjv+wQVjetM/AONoupqRALB7I6jGqNUAZsKcRIEu2J6FRFvsczljjsaQ==}
    engines: {node: '>=6.9.0'}

  '@cfcs/core@0.0.24':
    resolution: {integrity: sha512-feB38qu+eDk0Pggh/yR7gjaNmvUYA2uCxHP3Pz2MLE4LZ/9jPdtu8bzCSI47yTEhWyZCF5Pk698hdz8IN2mTjA==}

  '@crowdin/cli@3.7.10':
    resolution: {integrity: sha512-L0sjeEv4bn7LHNYsKxl2aTrah16u1ThufN0xvMMH7o53lD29llvVfAD9jVOttSl/kyQ+mMDY8GLzjPRNFdLJqQ==}
    hasBin: true

  '@egjs/component@3.0.5':
    resolution: {integrity: sha512-cLcGizTrrUNA2EYE3MBmEDt2tQv1joVP1Q3oDisZ5nw0MZDx2kcgEXM+/kZpfa/PAkFvYVhRUZwytIQWoN3V/w==}

  '@egjs/imready@1.4.1':
    resolution: {integrity: sha512-JIOBs4lB7FYdsKi5uvz2j3SObX8eShtZjtqlOH41tm185aJOQZwiKBK8+V4MxzG4X6DqVhpdN8UcuVwBbElfsg==}

  '@egjs/view360@4.0.0-beta.7':
    resolution: {integrity: sha512-prVTTxuQ1/k59NM7G0tm58k2vPHGoaExoFr5E7MoJaSGF56Otj4okQHAxxosXH87aQLN0feZMtBlsKz0b/7zEw==}

  '@esbuild/android-arm64@0.18.20':
    resolution: {integrity: sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.18.20':
    resolution: {integrity: sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.18.20':
    resolution: {integrity: sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.18.20':
    resolution: {integrity: sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.18.20':
    resolution: {integrity: sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.18.20':
    resolution: {integrity: sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.18.20':
    resolution: {integrity: sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.18.20':
    resolution: {integrity: sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.18.20':
    resolution: {integrity: sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.18.20':
    resolution: {integrity: sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.18.20':
    resolution: {integrity: sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.18.20':
    resolution: {integrity: sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.18.20':
    resolution: {integrity: sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.18.20':
    resolution: {integrity: sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.18.20':
    resolution: {integrity: sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.18.20':
    resolution: {integrity: sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.18.20':
    resolution: {integrity: sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.18.20':
    resolution: {integrity: sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.18.20':
    resolution: {integrity: sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.18.20':
    resolution: {integrity: sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.18.20':
    resolution: {integrity: sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.18.20':
    resolution: {integrity: sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@floating-ui/core@0.6.2':
    resolution: {integrity: sha512-jktYRmZwmau63adUG3GKOAVCofBXkk55S/zQ94XOorAHhwqFIOFAy1rSp2N0Wp6/tGbe9V3u/ExlGZypyY17rg==}

  '@floating-ui/dom@0.4.4':
    resolution: {integrity: sha512-0Ulu3B/dqQplUUSqnTx0foSrlYuMN+GTtlJWvNJwt6Fr7/PqmlR/Y08o6/+bxDWr6p3roBJRaQ51MDZsNmEhhw==}

  '@github/webauthn-json@2.1.1':
    resolution: {integrity: sha512-XrftRn4z75SnaJOmZQbt7Mk+IIjqVHw+glDGOxuHwXkZBZh/MBoRS7MHjSZMDaLhT4RjN2VqiEU7EOYleuJWSQ==}
    hasBin: true

  '@hope-ui/solid@0.6.7':
    resolution: {integrity: sha512-7zGGy4QbGUC7QhwRnNH8HO0MZFg4jFISlC2cnAMBfFBy272uqQN3PYdTjiIbnpR/4JilUfxCWpFQY+4qslqcIw==}
    peerDependencies:
      '@stitches/core': ^1.2.8
      solid-js: ^1.4.0
      solid-transition-group: ^0.0.10

  '@hrgui/libass-wasm-ts@1.0.3':
    resolution: {integrity: sha512-n8RbJLrhirfgDun88jVSs0/SeLC5PZz9iost9DXZ9dAXztDzpmjlEfu+k/viM37+EbaC9gWnRdUwcnptDkjtNw==}

  '@jridgewell/gen-mapping@0.1.1':
    resolution: {integrity: sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/gen-mapping@0.3.2':
    resolution: {integrity: sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.0':
    resolution: {integrity: sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.1.2':
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.2':
    resolution: {integrity: sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==}

  '@jridgewell/sourcemap-codec@1.4.14':
    resolution: {integrity: sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==}

  '@jridgewell/trace-mapping@0.3.15':
    resolution: {integrity: sha512-oWZNOULl+UbhsgB51uuZzglikfIKSUBO/M9W2OfEjn7cmqoAiCgmv9lyACTUacZwBz0ITnJ2NqjU8Tx0DHL88g==}

  '@monaco-editor/loader@1.4.0':
    resolution: {integrity: sha512-00ioBig0x642hytVspPl7DbQyaSWRaolYie/UFNjoTdvoKPzo6xrXLhTk9ixgIKcLH5b5vDOjVNiGyY+uDCUlg==}
    peerDependencies:
      monaco-editor: '>= 0.21.0 < 1'

  '@motionone/animation@10.14.0':
    resolution: {integrity: sha512-h+1sdyBP8vbxEBW5gPFDnj+m2DCqdlAuf2g6Iafb1lcMnqjsRXWlPw1AXgvUMXmreyhqmPbJqoNfIKdytampRQ==}

  '@motionone/dom@10.14.1':
    resolution: {integrity: sha512-KdlWDm267Befu90Q4WqFrowTT6XdnIU2Mum/xCF3I03X6jROMwR+gZj9axYoGQfx5lndUyHbzqDglLaYXWzAfQ==}

  '@motionone/easing@10.14.0':
    resolution: {integrity: sha512-2vUBdH9uWTlRbuErhcsMmt1jvMTTqvGmn9fHq8FleFDXBlHFs5jZzHJT9iw+4kR1h6a4SZQuCf72b9ji92qNYA==}

  '@motionone/generators@10.14.0':
    resolution: {integrity: sha512-6kRHezoFfIjFN7pPpaxmkdZXD36tQNcyJe3nwVqwJ+ZfC0e3rFmszR8kp9DEVFs9QL/akWjuGPSLBI1tvz+Vjg==}

  '@motionone/solid@10.14.1':
    resolution: {integrity: sha512-HemoM7NuRiRlgqbkEgN1NO31XahlgtjkhR3iihZ4RF3sP1h78p2d5ODE7OPtXEUlMKPEe5wobfh5+9+23kaHZA==}
    peerDependencies:
      solid-js: ^1.4.1

  '@motionone/types@10.14.0':
    resolution: {integrity: sha512-***********************************/IZqsHtA9DnRM1SYgN01CTcJ8Iw8pCXF5Ocp34tyAjY7WRpOJJQ==}

  '@motionone/utils@10.14.0':
    resolution: {integrity: sha512-sLWBLPzRqkxmOTRzSaD3LFQXCPHvDzyHJ1a3VP9PRzBxyVd2pv51/gMOsdAcxQ9n+MIeGJnxzXBYplUHKj4jkw==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@solid-primitives/event-listener@2.3.0':
    resolution: {integrity: sha512-0DS7DQZvCExWSpurVZC9/wjI8RmkhuOtWOy6Pp1Woq9ElMT9/bfjNpkwXsOwisLpcTqh9eUs17kp7jtpWcC20w==}
    peerDependencies:
      solid-js: ^1.6.12

  '@solid-primitives/i18n@1.1.0':
    resolution: {integrity: sha512-aqJttEb8rmsQTU5VqfBfaeHFMIqKu1eXHVEahtW4DGKYBz/CDis5V25psQEyspuVTmPs7RpX99Whe9jV7ib4pg==}
    peerDependencies:
      solid-js: ^1.3.1

  '@solid-primitives/keyboard@1.2.5':
    resolution: {integrity: sha512-1axfWM1T4ASzZp4D91vLtxARevlBuOQ6yFHr1/IkuM/7OhMLo/BrO2CcDu3vSwCPVOSiZ2P875bTiqVWQV6e5g==}
    peerDependencies:
      solid-js: ^1.6.12

  '@solid-primitives/props@2.2.2':
    resolution: {integrity: sha512-vjRRoi/z3S2ylIJKCs+mN07oxDmt2S9gPCbTqkEx8jYHnvzocpt34UQdglLoSklTE6jI37JhW3g/Cs8Qr/peHg==}
    peerDependencies:
      solid-js: ^1.3.0

  '@solid-primitives/rootless@1.4.2':
    resolution: {integrity: sha512-ynI/2aEOPyc14IKCX6yDBqnsAYCoLbaP9V/jejEWMVKOT2ZdV2ZxdftaLimOpWPpvjyti5DUJIGTOfLaNb7jlg==}
    peerDependencies:
      solid-js: ^1.6.12

  '@solid-primitives/storage@1.3.1':
    resolution: {integrity: sha512-3By9cxqCGGkXrw3gYj7h2c5SCWxiDIYc2C3O6GLVyzN4JCIKzcMlm3HLK0gKMUbR/MzAs265XyAc3rqsMq1ixw==}
    peerDependencies:
      solid-js: ^1.3.1

  '@solid-primitives/utils@3.0.2':
    resolution: {integrity: sha512-LCU3tVrJmyRqJ0ocG5uCEuUNqmGkcAC+cWpDEE49AuvtehkdQfv4CfqvdNJgs3eoQRQhLOrVcgd1bHFJY4lsrQ==}
    peerDependencies:
      solid-js: ^1.4.1

  '@solid-primitives/utils@6.2.1':
    resolution: {integrity: sha512-TsecNzxiO5bLfzqb4OOuzfUmdOROcssuGqgh5rXMMaasoFZ3GoveUgdY1wcf17frMJM7kCNGNuK34EjErneZkg==}
    peerDependencies:
      solid-js: ^1.6.12

  '@stitches/core@1.2.8':
    resolution: {integrity: sha512-Gfkvwk9o9kE9r9XNBmJRfV8zONvXThnm1tcuojL04Uy5uRyqg93DC83lDebl0rocZCfKSjUv+fWYtMQmEDJldg==}

  '@types/bencode@2.0.4':
    resolution: {integrity: sha512-sirDu3HUSG7jZMlhTDvCzSFiPR4lkUYBQA75CoMi6DEf2alFZWJWtHgfjBbb9PachPZhPMB1IlH09deyMNBipQ==}

  '@types/crypto-js@4.2.2':
    resolution: {integrity: sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==}

  '@types/debug@4.1.7':
    resolution: {integrity: sha512-9AonUzyTjXXhEOa0DnqpzZi6VHlqKMswga9EXjpXnnqxwLtdvPPtlO8evrI5D9S6asFRCQ6v+wpiUKbw+vKqyg==}

  '@types/fs-extra@8.1.4':
    resolution: {integrity: sha512-OMcQKnlrkrOI0TaZ/MgVDA8LYFl7CykzFsjMj9l5x3un2nFxCY20ZFlnqrM0lcqlbs0Yro2HbnZlmopyRaoJ5w==}

  '@types/glob@7.2.0':
    resolution: {integrity: sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==}

  '@types/hast@2.3.4':
    resolution: {integrity: sha512-wLEm0QvaoawEDoTRwzTXp4b4jpwiJDvR5KMnFnVodm3scufTlBOWRD6N1OBf9TZMhjlNsSfcO5V+7AF4+Vy+9g==}

  '@types/jquery@3.5.16':
    resolution: {integrity: sha512-bsI7y4ZgeMkmpG9OM710RRzDFp+w4P1RGiIt30C1mSBT+ExCleeh4HObwgArnDFELmRrOpXgSYN9VF1hj+f1lw==}

  '@types/katex@0.14.0':
    resolution: {integrity: sha512-+2FW2CcT0K3P+JMR8YG846bmDwplKUTsWgT2ENwdQ1UdVfRk3GQrh6Mi4sTopy30gI8Uau5CEqHTDZ6YvWIUPA==}

  '@types/katex@0.16.0':
    resolution: {integrity: sha512-hz+S3nV6Mym5xPbT9fnO8dDhBFQguMYpY0Ipxv06JMi1ORgnEM4M1ymWDUhUNer3ElLmT583opRo4RzxKmh9jw==}

  '@types/mark.js@8.11.8':
    resolution: {integrity: sha512-BoWCd9ydi1hZxDfu/lF0v1hHMsNUjuxZEDJsdHlmm6GlKk4qxlLya7D3FS81QmabwFbYPpoDOh9603JESUkHbA==}

  '@types/mdast@3.0.10':
    resolution: {integrity: sha512-W864tg/Osz1+9f4lrGTZpCSO5/z4608eUp19tbozkq2HJK6i3z1kT0H9tlADXuYIb1YYOBByU4Jsqkk75q48qA==}

  '@types/mdurl@1.0.2':
    resolution: {integrity: sha512-eC4U9MlIcu2q0KQmXszyn5Akca/0jrQmwDRgpAMJai7qBWq4amIQhZyNau4VYGtCeALvW1/NtjzJJ567aZxfKA==}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==}

  '@types/ms@0.7.31':
    resolution: {integrity: sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==}

  '@types/node@20.0.0':
    resolution: {integrity: sha512-cD2uPTDnQQCVpmRefonO98/PPijuOnnEy5oytWJFPY1N9aJCz2wJ5kSGWO+zJoed2cY2JxQh6yBuUq4vIn61hw==}

  '@types/parse5@6.0.3':
    resolution: {integrity: sha512-SuT16Q1K51EAVPz1K29DJ/sXjhSQ0zjvsypYJ6tlwVsRV9jwW5Adq2ch8Dq8kDBCkYnELS7N7VNCSB5nC56t/g==}

  '@types/qrcode@1.5.5':
    resolution: {integrity: sha512-CdfBi/e3Qk+3Z/fXYShipBT13OJ2fDO2Q2w5CIP5anLTLIndQG9z6P1cnm+8zCWSpm5dnxMFd/uREtb0EXuQzg==}

  '@types/sha256@0.2.0':
    resolution: {integrity: sha512-QYMr6HuxTQunFWRLZpGopbkgQFoFWOmKTBGgNSYiWMqU/CWnQSTo3edyHvgsRXsOWtOSOG/cmDptPzgCeOsQGw==}

  '@types/sizzle@2.3.3':
    resolution: {integrity: sha512-JYM8x9EGF163bEyhdJBpR2QX1R5naCJHC8ucJylJ3w9/CVBaskdQ8WqBf8MmQrd1kRvp/a4TS8HJ+bxzR7ZJYQ==}

  '@types/streamsaver@2.0.1':
    resolution: {integrity: sha512-I49NtT8w6syBI3Zg3ixCyygTHoTVMY0z2TMRcTgccdIsVd2MwlKk7ITLHLsJtgchUHcOd7QEARG9h0ifcA6l2Q==}

  '@types/unist@2.0.6':
    resolution: {integrity: sha512-PBjIUxZHOuj0R15/xuwJYjFi+KZdNFrehocChv4g5hu6aFroHue8m0lBP0POdK2nKzbw0cgV1mws8+V/JAcEkQ==}

  '@types/webxr@0.5.19':
    resolution: {integrity: sha512-4hxA+NwohSgImdTSlPXEqDqqFktNgmTXQ05ff1uWam05tNGroCMp4G+4XVl6qWm1p7GQ/9oD41kAYsSssF6Mzw==}

  '@viselect/vanilla@3.5.0':
    resolution: {integrity: sha512-drPkEQ/0CZl/c7D0gx5lWkY6iCdbyJm6KWFJpY2oQEKEnCrW9LUdQOsbNP/RG2L8BNUaiDBmwyTTW2RYIroZhA==}

  '@vitejs/plugin-legacy@2.0.1':
    resolution: {integrity: sha512-kKC56rfsXwebApzyuZqdQlGmqTyH1ugy0l0rY58gx5OXzq4/t5/DCqGUoz53Db51OqfjRqsHfqmvq9or6w9k/Q==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      terser: ^5.4.0
      vite: ^3.0.0

  acorn@8.8.0:
    resolution: {integrity: sha512-QOxyigPVrpZ2GXT+PFyZTl6TtOFc5egxHIP9IlQ+RbupQuX4RkT/Bee4/kQuC02Xkzg84JcT7oLYtDIQxp+v7w==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  aplayer@1.10.1:
    resolution: {integrity: sha512-HAfyxgCUTLAqtYlxzzK9Fyqg6y+kZ9CqT1WfeWE8FSzwspT6oBqWOZHANPHF3RGTtC33IsyEgrfthPDzU5r9kQ==}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  artplayer-plugin-danmuku@5.1.5:
    resolution: {integrity: sha512-iFjdXVbkw5FbkB98SOwMPQEFJp7k6Io4nHfehpSdCc7Eo1gkmjNShOT6dAtAH1kLMHjh38vRnf/oYwCvTKkVOw==}

  artplayer@5.2.2:
    resolution: {integrity: sha512-JsPRA9e+7KyWTh5KBeKLTcCigzGhpZulRFXvOeiFMbxnpPz9kZ/1STp1LZONqQXHkxX9wHTO6JhGOvzrduH2Lw==}

  asciinema-player@3.6.3:
    resolution: {integrity: sha512-62aDgLpbuduhmpFfNgPOzf6fOluACLsftVnjpWJjUXX6dqhqTckFqWoJ+ayA0XjSlZ9l9wXTcJqRqvAAIpMblg==}

  astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.1.3:
    resolution: {integrity: sha512-00tXVRwKx/FZr/IDVFt4C+f9FYairX517WoGCL6dpOntqLkZofjhu43F/Xl44UOpqa+9sLFDrG/XAnFsUYgkDA==}

  babel-plugin-jsx-dom-expressions@0.33.14:
    resolution: {integrity: sha512-91T8uEz6Wb42bUm5vxRBawY05fBHiwUxah/xWBimuWpH3nf7E0KJ0Wm/s8R7lxRIZzwGCILv1IBlUCqA50WOVw==}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-solid@1.4.8:
    resolution: {integrity: sha512-Qv1yoE7yIux68egUsUUEV26t7B0KLNyXKz1MTk89GJDc6mt+2s7+lDVr4tXa29PTZ/hXDTu2uLbEN/1OtmFFBg==}
    peerDependencies:
      '@babel/core': ^7.0.0

  bail@2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  balloon-css@0.5.2:
    resolution: {integrity: sha512-zheJpzwyNrG4t39vusA67v3BYg1HTVXOF8cErPEHzWK88PEOFwgo6Ea9VHOgOWNMgeuOtFVtB73NE2NWl9uDyQ==}

  base64-arraybuffer@1.0.2:
    resolution: {integrity: sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==}
    engines: {node: '>= 0.6.0'}

  bencode@4.0.0:
    resolution: {integrity: sha512-AERXw18df0pF3ziGOCyUjqKZBVNH8HV3lBxnx5w0qtgMIk4a1wb9BkcCQbkp9Zstfrn/dzRwl7MmUHHocX3sRQ==}
    engines: {node: '>=12.20.0'}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}

  browserslist@4.21.3:
    resolution: {integrity: sha512-898rgRXLAyRkM1GryrrBHGkqA5hlpkV5MhtZwg9QXeiyLUYs2k00Un05aX5l2/yJIOObYKOpS2JNo8nJDE7fWQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-crc32@0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001377:
    resolution: {integrity: sha512-I5XeHI1x/mRSGl96LFOaSk528LA/yZG3m3iQgImGujjO8gotd/DL8QaI1R1h1dg5ATeI2jqPblMpKq4Tr5iKfQ==}

  ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  character-entities@2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}

  chardet@2.0.0:
    resolution: {integrity: sha512-xVgPpulCooDjY6zH4m9YW3jbkaBe3FKIAvF5sj5t7aBNsVl2ljIE+xwJ4iNgiDZHFQvNIpjdKdVOQvvk5ZfxbQ==}

  chownr@1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}

  clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}

  cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}

  cli-truncate@2.1.0:
    resolution: {integrity: sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==}
    engines: {node: '>=8'}

  cli-truncate@3.1.0:
    resolution: {integrity: sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  cliui@6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}

  clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}

  clsx@2.0.0:
    resolution: {integrity: sha512-rQ1+kcj+ttHG0MKVGBUXwayCCF1oh39BF5COIpRzuCEv8Mwjv0XucrI2ExNTOn9IlLifGClWQcU9BrZORvtw6Q==}
    engines: {node: '>=6'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  colorette@1.4.0:
    resolution: {integrity: sha512-Y2oEozpomLn7Q3HFP7dpww7AtMJplbM9lGZP6RDfHqmbeRjiwRg4n6VM6j4KLmRke85uWEI7JqF17f3pqdRA0g==}

  colorette@2.0.19:
    resolution: {integrity: sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  comma-separated-tokens@2.0.2:
    resolution: {integrity: sha512-G5yTt3KQN4Yn7Yk4ed73hlZ1evrFKXeUW3086p3PRFNp7m2vIjI6Pg+Kgb+oyzhd9F2qdcoj67+y3SdxL5XWsg==}

  command-exists-promise@2.0.2:
    resolution: {integrity: sha512-T6PB6vdFrwnHXg/I0kivM3DqaCGZLjjYSOe0a5WgFKcz1sOnmOeIjnhQPXVXX3QjVbLyTJ85lJkX6lUpukTzaA==}
    engines: {node: '>=6'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}

  commander@9.4.1:
    resolution: {integrity: sha512-5EEkTNyHNGFPD2H+c/dXXfQZYa/scCKasxWcXJaWnNJ99pnQN9Vnmqow+p+PlFPE63Q6mThaZws1T+HxfpgtPw==}
    engines: {node: ^12.20.0 || >=14}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  convert-hex@0.1.0:
    resolution: {integrity: sha512-w20BOb1PiR/sEJdS6wNrUjF5CSfscZFUp7R9NSlXH8h2wynzXVEPFPJECAnkNylZ+cvf3p7TyRUHggDmrwXT9A==}

  convert-source-map@1.8.0:
    resolution: {integrity: sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA==}

  convert-string@0.1.0:
    resolution: {integrity: sha512-1KX9ESmtl8xpT2LN2tFnKSbV4NiarbVi8DVb39ZriijvtTklyrT+4dT1wsGMHKD3CJUjXgvJzstm9qL9ICojGA==}

  copy-to-clipboard@3.3.2:
    resolution: {integrity: sha512-Vme1Z6RUDzrb6xAI7EZlVZ5uvOk2F//GaxKUxajDqm9LhOVM1inxNAD2vy+UZDYsd0uyA9s7b3/FVZPSxqrCfg==}

  core-js@3.24.1:
    resolution: {integrity: sha512-0QTBSYSUZ6Gq21utGzkfITDylE8jWC9Ne1D2MrhvlsZBI1x39OdDIVbzSqtgMndIy6BlHxBXpMGqzZmnztg2rg==}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}

  css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  csstype@3.0.11:
    resolution: {integrity: sha512-sa6P2wJ+CAbgyy4KFssIb/JNMLxFvKF1pCYCSXS8ZMuqZnMsrxqI2E5sPyoTpxoPU/gVZMzr2zjOfg8GIZOMsw==}

  debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  decode-named-character-reference@1.0.2:
    resolution: {integrity: sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  diff@5.1.0:
    resolution: {integrity: sha512-D+mk+qE8VC/PAUrlAU34N+VfXev0ghe5ywmpqrawphmVZc1bEfn56uo9qpyGp1p4xpzOHkSW4ztBd6L7Xx4ACw==}
    engines: {node: '>=0.3.1'}

  dijkstrajs@1.0.3:
    resolution: {integrity: sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}

  domutils@2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  electron-to-chromium@1.4.221:
    resolution: {integrity: sha512-aWg2mYhpxZ6Q6Xvyk7B2ziBca4YqrCDlXzmcD7wuRs65pVEVkMT1u2ifdjpAQais2O2o0rW964ZWWWYRlAL/kw==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  es6-promise@4.2.8:
    resolution: {integrity: sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==}

  esbuild@0.18.20:
    resolution: {integrity: sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}

  execa@6.1.0:
    resolution: {integrity: sha512-QVWlX2e50heYJcCPG0iWtf8r0xjEYfz/OYLGDYH+IyjWezzPNxz63qNFOu0l4YftGWuizFVZHHs8PrLU5p2IDA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  fast-glob@3.3.1:
    resolution: {integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==}
    engines: {node: '>=8.6.0'}

  fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}

  fd-slicer@1.1.0:
    resolution: {integrity: sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==}

  fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  focus-trap@6.7.3:
    resolution: {integrity: sha512-8xCEKndV4KrseGhFKKKmczVA14yx1/hnmFICPOjcFjToxCJYj/NHH43tPc3YE/PLnLRNZoFug0EcWkGQde/miQ==}

  follow-redirects@1.15.1:
    resolution: {integrity: sha512-yLAMQs+k0b2m7cVxpS1VKJVvoz7SS9Td1zss3XRwXj+ZDH00RJgnuLx7E44wx02kQLrdM3aOOy+FpzS7+8OizA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  fs-extra@8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}

  fs-minipass@1.2.7:
    resolution: {integrity: sha512-GWSSJGFy4e9GUeCcbIkED+bgAoFyj7XF1mV8rma3QW4NIqX9Kyx79N/PF61H5udOV3aY1IaMLs6pGbH71nlCTA==}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  gl-matrix@3.4.3:
    resolution: {integrity: sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globby@10.0.1:
    resolution: {integrity: sha512-sSs4inE1FB2YQiymcmTv6NWENryABjUNPeWhOvmn4SjtKybglsyPZxFB3U1/+L1bYi0rNZDqCLlHyLYDl1Pq5A==}
    engines: {node: '>=8'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has@1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}

  hash-wasm@4.12.0:
    resolution: {integrity: sha512-+/2B2rYLb48I/evdOIhP+K/DD2ca2fgBjp6O+GBEnCDk2e4rpeXIK8GvIyRPjTezgmWn9gmKwkQjjx6BtqDHVQ==}

  hast-to-hyperscript@10.0.1:
    resolution: {integrity: sha512-dhIVGoKCQVewFi+vz3Vt567E4ejMppS1haBRL6TEmeLeJVB1i/FJIIg/e6s1Bwn0g5qtYojHEKvyGA+OZuyifw==}

  hast-util-from-dom@4.2.0:
    resolution: {integrity: sha512-t1RJW/OpJbCAJQeKi3Qrj1cAOLA0+av/iPFori112+0X7R3wng+jxLA+kXec8K4szqPRGI8vPxbbpEYvvpwaeQ==}

  hast-util-from-html-isomorphic@1.0.0:
    resolution: {integrity: sha512-Yu480AKeOEN/+l5LA674a+7BmIvtDj24GvOt7MtQWuhzUwlaaRWdEPXAh3Qm5vhuthpAipFb2vTetKXWOjmTvw==}

  hast-util-from-html@1.0.2:
    resolution: {integrity: sha512-LhrTA2gfCbLOGJq2u/asp4kwuG0y6NhWTXiPKP+n0qNukKy7hc10whqqCFfyvIA1Q5U5d0sp9HhNim9gglEH4A==}

  hast-util-from-parse5@7.1.0:
    resolution: {integrity: sha512-m8yhANIAccpU4K6+121KpPP55sSl9/samzQSQGpb0mTExcNh2WlvjtMwSWFhg6uqD4Rr6Nfa8N6TMypQM51rzQ==}

  hast-util-is-element@2.1.3:
    resolution: {integrity: sha512-O1bKah6mhgEq2WtVMk+Ta5K7pPMqsBBlmzysLdcwKVrqzZQ0CHqUPiIVspNhAG1rvxpvJjtGee17XfauZYKqVA==}

  hast-util-parse-selector@3.1.0:
    resolution: {integrity: sha512-AyjlI2pTAZEOeu7GeBPZhROx0RHBnydkQIXlhnFzDi0qfXTmGUWoCYZtomHbrdrheV4VFUlPcfJ6LMF5T6sQzg==}

  hast-util-raw@7.2.2:
    resolution: {integrity: sha512-0x3BhhdlBcqRIKyc095lBSDvmQNMY3Eulj2PLsT5XCyKYrxssI5yr3P4Kv/PBo1s/DMkZy2voGkMXECnFCZRLQ==}

  hast-util-to-parse5@7.0.0:
    resolution: {integrity: sha512-YHiS6aTaZ3N0Q3nxaY/Tj98D6kM8QX5Q8xqgg8G45zR7PvWnPGPP0vcKCgb/moIydEJ/QWczVrX0JODCVeoV7A==}

  hast-util-to-text@3.1.2:
    resolution: {integrity: sha512-tcllLfp23dJJ+ju5wCCZHVpzsQQ43+moJbqVX3jNWPB7z/KFC4FyZD6R7y94cHL6MQ33YtMZL8Z0aIXXI4XFTw==}

  hastscript@7.0.2:
    resolution: {integrity: sha512-uA8ooUY4ipaBvKcMuPehTAB/YfFLSSzCwFSwT6ltJbocFUKH/GDHLN+tflq7lSRf9H86uOuxOFkh1KgIy3Gg2g==}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  hey-listen@1.0.8:
    resolution: {integrity: sha512-COpmrF2NOg4TBWUJ5UVyaCU2A88wEMkUPK4hNqyCkqHbxT92BbvfjoSozkAIIm6XhicGlJHhFdullInrdhwU8Q==}

  hls.js@1.6.1:
    resolution: {integrity: sha512-7GOkcqn0Y9EqU2OJZlzkwxj9Uynuln7URvr7dRjgqNJNZ5UbbjL/v1BjAvQogy57Psdd/ek1u2s6IDEFYlabrA==}

  html-entities@2.3.2:
    resolution: {integrity: sha512-c3Ab/url5ksaT0WyleslpBEthOzWhrjQbg75y7XUsfSzi3Dgzt0l8w5e7DylRn15MTlMMD58dTfzddNS2kcAjQ==}

  html-void-elements@2.0.1:
    resolution: {integrity: sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A==}

  human-signals@3.0.1:
    resolution: {integrity: sha512-rQLskxnM/5OCldHo+wNXbpVgDn5A17CUoKX+7Sokwaknlq7CdSnphy0W39GU8dw59XiCXmFXDg4fRuckQRKewQ==}
    engines: {node: '>=12.20.0'}

  husky@8.0.2:
    resolution: {integrity: sha512-Tkv80jtvbnkK3mYWxPZePGFpQ/tT3HNSs/sasF9P2YfkMezDl3ON37YN6jUUI4eTg5LcyVynlb6r4eyvOmspvg==}
    engines: {node: '>=14'}
    hasBin: true

  ignore@5.2.4:
    resolution: {integrity: sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==}
    engines: {node: '>= 4'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  inline-style-parser@0.1.1:
    resolution: {integrity: sha512-7NXolsK4CAS5+xvdj5OMMbI962hU/wvwoxk+LWR9Ek9bVtyuuYScDN6eS0rUm6TxApFpw7CX1o4uJzcd4AyD3Q==}

  interpret@1.4.0:
    resolution: {integrity: sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==}
    engines: {node: '>= 0.10'}

  is-buffer@2.0.5:
    resolution: {integrity: sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==}
    engines: {node: '>=4'}

  is-core-module@2.10.0:
    resolution: {integrity: sha512-Erxj2n/LDAZ7H8WNJXd9tw38GYM3dv8rk8Zcs+jJuxYTW7sozH+SS8NtrSjVL1/vpLvWi1hxy96IzjJ3EHTJJg==}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    engines: {node: '>=12'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}

  is-plain-object@3.0.1:
    resolution: {integrity: sha512-Xnpx182SBMrr/aBik8y+GuR4U1L9FqMSojwDQwPMmxyC6bvEqly9UBCxhauBF5vNh2gwWJNX6oDV7O+OM4z34g==}
    engines: {node: '>=0.10.0'}

  is-stream@3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-what@4.1.7:
    resolution: {integrity: sha512-DBVOQNiPKnGMxRMLIYSwERAS5MVY1B7xYiGnpgctsOFvVDz9f9PFXXxMcTOHuoqYp4NK9qFYQaIC1NRRxLMpBQ==}
    engines: {node: '>=12.13'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json5@2.2.1:
    resolution: {integrity: sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}

  just-once@2.2.0:
    resolution: {integrity: sha512-Wo547FgUOUZ98jbrZ1KX8nRezdEdtgIlC2NK1u1RvR1oZ/WoU++FjprP8J8hRbaox776MHyeMZZED4DvhhHVjg==}

  katex@0.16.8:
    resolution: {integrity: sha512-ftuDnJbcbOckGY11OO+zg3OofESlbR5DRl2cmN8HeWeeFIV7wTXvAOx8kEjZjobhA+9wh2fbKeO6cdcA9Mnovg==}
    hasBin: true

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  kleur@4.1.5:
    resolution: {integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==}
    engines: {node: '>=6'}

  libass-wasm@4.1.0:
    resolution: {integrity: sha512-+RbYT/uuI6VHExCmGyUuMg3A2gQOaCRTzSn8GGDSf3q4cEoUNiINd9u4RGfZXA1UKafW+Hv8bmcKIX4FKbSh0Q==}

  lightgallery@2.5.0:
    resolution: {integrity: sha512-pzg5gwflLGlKaK1VqDKpb7yqQ/WSV/TxFaHRR7ld7G5iOI9gkJZ2qKKo2lyqvTvu8yAqNYXoU0w9O/Dll6POkw==}
    engines: {node: '>=6.0.0'}

  lilconfig@2.0.6:
    resolution: {integrity: sha512-9JROoBW7pobfsx+Sq2JsASvCo6Pfo6WWoUW79HuB1BCoBXD4PLWJPqDF6fNj67pqBYTbAHkE57M1kS/+L1neOg==}
    engines: {node: '>=10'}

  lint-staged@13.0.4:
    resolution: {integrity: sha512-HxlHCXoYRsq9QCby5wFozmZW00hMs/9e3l+/dz6Qr8Kle4UH0kJTdABAbqhzG+3pcG6QjL9kz7NgGBfph+a5dw==}
    engines: {node: ^14.13.1 || >=16.0.0}
    hasBin: true

  listr2@5.0.6:
    resolution: {integrity: sha512-u60KxKBy1BR2uLJNTWNptzWQ1ob/gjMzIJPZffAENzpZqbMZ/5PrXXOomDcevIS/+IB7s1mmCEtSlT2qHWMqag==}
    engines: {node: ^14.13.1 || >=16.0.0}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  log-update@4.0.0:
    resolution: {integrity: sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==}
    engines: {node: '>=10'}

  longest-streak@3.0.1:
    resolution: {integrity: sha512-cHlYSUpL2s7Fb3394mYxwTYj8niTaNHUCLr0qdiCXQfSjfuA7CKofpX2uSwEfFDQ0EB7JcnMnm+GjbqqoinYYg==}

  magic-string@0.26.2:
    resolution: {integrity: sha512-NzzlXpclt5zAbmo6h6jNc8zl2gNRGHvmsZW4IvZhTC4W7k4OlLP+S5YLussa/r3ixNT66KOQfNORlXHSOy/X4A==}
    engines: {node: '>=12'}

  mark.js@8.11.1:
    resolution: {integrity: sha512-1I+1qpDt4idfgLQG+BNWmrqku+7/2bi5nLf4YwF8y8zXvmfiTBY3PV3ZibfrjBueCByROpuBjLLFCajqkgYoLQ==}

  markdown-table@3.0.2:
    resolution: {integrity: sha512-y8j3a5/DkJCmS5x4dMCQL+OR0+2EAq3DOtio1COSHsmW2BGXnNCK3v12hJt1LrUz5iZH5g0LmuYOjDdI+czghA==}

  mdast-util-definitions@5.1.1:
    resolution: {integrity: sha512-rQ+Gv7mHttxHOBx2dkF4HWTg+EE+UR78ptQWDylzPKaQuVGdG4HIoY3SrS/pCp80nZ04greFvXbVFHT+uf0JVQ==}

  mdast-util-find-and-replace@2.2.1:
    resolution: {integrity: sha512-SobxkQXFAdd4b5WmEakmkVoh18icjQRxGy5OWTCzgsLRm1Fu/KCtwD1HIQSsmq5ZRjVH0Ehwg6/Fn3xIUk+nKw==}

  mdast-util-from-markdown@1.2.0:
    resolution: {integrity: sha512-iZJyyvKD1+K7QX1b5jXdE7Sc5dtoTry1vzV28UZZe8Z1xVnB/czKntJ7ZAkG0tANqRnBF6p3p7GpU1y19DTf2Q==}

  mdast-util-gfm-autolink-literal@1.0.2:
    resolution: {integrity: sha512-FzopkOd4xTTBeGXhXSBU0OCDDh5lUj2rd+HQqG92Ld+jL4lpUfgX2AT2OHAVP9aEeDKp7G92fuooSZcYJA3cRg==}

  mdast-util-gfm-footnote@1.0.1:
    resolution: {integrity: sha512-p+PrYlkw9DeCRkTVw1duWqPRHX6Ywh2BNKJQcZbCwAuP/59B0Lk9kakuAd7KbQprVO4GzdW8eS5++A9PUSqIyw==}

  mdast-util-gfm-strikethrough@1.0.1:
    resolution: {integrity: sha512-zKJbEPe+JP6EUv0mZ0tQUyLQOC+FADt0bARldONot/nefuISkaZFlmVK4tU6JgfyZGrky02m/I6PmehgAgZgqg==}

  mdast-util-gfm-table@1.0.4:
    resolution: {integrity: sha512-aEuoPwZyP4iIMkf2cLWXxx3EQ6Bmh2yKy9MVCg4i6Sd3cX80dcLEfXO/V4ul3pGH9czBK4kp+FAl+ZHmSUt9/w==}

  mdast-util-gfm-task-list-item@1.0.1:
    resolution: {integrity: sha512-KZ4KLmPdABXOsfnM6JHUIjxEvcx2ulk656Z/4Balw071/5qgnhz+H1uGtf2zIGnrnvDC8xR4Fj9uKbjAFGNIeA==}

  mdast-util-gfm@2.0.1:
    resolution: {integrity: sha512-42yHBbfWIFisaAfV1eixlabbsa6q7vHeSPY+cg+BBjX51M8xhgMacqH9g6TftB/9+YkcI0ooV4ncfrJslzm/RQ==}

  mdast-util-math@2.0.2:
    resolution: {integrity: sha512-8gmkKVp9v6+Tgjtq6SYx9kGPpTf6FVYRa53/DLh479aldR9AyP48qeVOgNZ5X7QUK7nOy4yw7vg6mbiGcs9jWQ==}

  mdast-util-to-hast@12.2.0:
    resolution: {integrity: sha512-YDwT5KhGzLgPpSnQhAlK1+WpCW4gsPmNNAxUNMkMTDhxQyPp2eX86WOelnKnLKEvSpfxqJbPbInHFkefXZBhEA==}

  mdast-util-to-markdown@1.3.0:
    resolution: {integrity: sha512-6tUSs4r+KK4JGTTiQ7FfHmVOaDrLQJPmpjD6wPMlHGUVXoG9Vjc3jIeP+uyBWRf8clwB2blM+W7+KrlMYQnftA==}

  mdast-util-to-string@3.1.0:
    resolution: {integrity: sha512-n4Vypz/DZgwo0iMHLQL49dJzlp7YtAJP+N07MZHpjPf/5XJuHUWstviF4Mn2jEiR/GNmtnRRqnwsXExk3igfFA==}

  mdurl@1.0.1:
    resolution: {integrity: sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g==}

  merge-anything@5.0.2:
    resolution: {integrity: sha512-POPQBWkBC0vxdgzRJ2Mkj4+2NTKbvkHo93ih+jGDhNMLzIw+rYKjO7949hOQM2X7DxMHH1uoUkwWFLIzImw7gA==}
    engines: {node: '>=12.13'}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromark-core-commonmark@1.0.6:
    resolution: {integrity: sha512-K+PkJTxqjFfSNkfAhp4GB+cZPfQd6dxtTXnf+RjZOV7T4EEXnvgzOcnp+eSTmpGk9d1S9sL6/lqrgSNn/s0HZA==}

  micromark-extension-gfm-autolink-literal@1.0.3:
    resolution: {integrity: sha512-i3dmvU0htawfWED8aHMMAzAVp/F0Z+0bPh3YrbTPPL1v4YAlCZpy5rBO5p0LPYiZo0zFVkoYh7vDU7yQSiCMjg==}

  micromark-extension-gfm-footnote@1.0.4:
    resolution: {integrity: sha512-E/fmPmDqLiMUP8mLJ8NbJWJ4bTw6tS+FEQS8CcuDtZpILuOb2kjLqPEeAePF1djXROHXChM/wPJw0iS4kHCcIg==}

  micromark-extension-gfm-strikethrough@1.0.4:
    resolution: {integrity: sha512-/vjHU/lalmjZCT5xt7CcHVJGq8sYRm80z24qAKXzaHzem/xsDYb2yLL+NNVbYvmpLx3O7SYPuGL5pzusL9CLIQ==}

  micromark-extension-gfm-table@1.0.5:
    resolution: {integrity: sha512-xAZ8J1X9W9K3JTJTUL7G6wSKhp2ZYHrFk5qJgY/4B33scJzE2kpfRL6oiw/veJTbt7jiM/1rngLlOKPWr1G+vg==}

  micromark-extension-gfm-tagfilter@1.0.1:
    resolution: {integrity: sha512-Ty6psLAcAjboRa/UKUbbUcwjVAv5plxmpUTy2XC/3nJFL37eHej8jrHrRzkqcpipJliuBH30DTs7+3wqNcQUVA==}

  micromark-extension-gfm-task-list-item@1.0.3:
    resolution: {integrity: sha512-PpysK2S1Q/5VXi72IIapbi/jliaiOFzv7THH4amwXeYXLq3l1uo8/2Be0Ac1rEwK20MQEsGH2ltAZLNY2KI/0Q==}

  micromark-extension-gfm@2.0.1:
    resolution: {integrity: sha512-p2sGjajLa0iYiGQdT0oelahRYtMWvLjy8J9LOCxzIQsllMCGLbsLW+Nc+N4vi02jcRJvedVJ68cjelKIO6bpDA==}

  micromark-extension-math@2.1.2:
    resolution: {integrity: sha512-es0CcOV89VNS9wFmyn+wyFTKweXGW4CEvdaAca6SWRWPyYCbBisnjaHLjWO4Nszuiud84jCpkHsqAJoa768Pvg==}

  micromark-factory-destination@1.0.0:
    resolution: {integrity: sha512-eUBA7Rs1/xtTVun9TmV3gjfPz2wEwgK5R5xcbIM5ZYAtvGF6JkyaDsj0agx8urXnO31tEO6Ug83iVH3tdedLnw==}

  micromark-factory-label@1.0.2:
    resolution: {integrity: sha512-CTIwxlOnU7dEshXDQ+dsr2n+yxpP0+fn271pu0bwDIS8uqfFcumXpj5mLn3hSC8iw2MUr6Gx8EcKng1dD7i6hg==}

  micromark-factory-space@1.0.0:
    resolution: {integrity: sha512-qUmqs4kj9a5yBnk3JMLyjtWYN6Mzfcx8uJfi5XAveBniDevmZasdGBba5b4QsvRcAkmvGo5ACmSUmyGiKTLZew==}

  micromark-factory-title@1.0.2:
    resolution: {integrity: sha512-zily+Nr4yFqgMGRKLpTVsNl5L4PMu485fGFDOQJQBl2NFpjGte1e86zC0da93wf97jrc4+2G2GQudFMHn3IX+A==}

  micromark-factory-whitespace@1.0.0:
    resolution: {integrity: sha512-Qx7uEyahU1lt1RnsECBiuEbfr9INjQTGa6Err+gF3g0Tx4YEviPbqqGKNv/NrBaE7dVHdn1bVZKM/n5I/Bak7A==}

  micromark-util-character@1.1.0:
    resolution: {integrity: sha512-agJ5B3unGNJ9rJvADMJ5ZiYjBRyDpzKAOk01Kpi1TKhlT1APx3XZk6eN7RtSz1erbWHC2L8T3xLZ81wdtGRZzg==}

  micromark-util-chunked@1.0.0:
    resolution: {integrity: sha512-5e8xTis5tEZKgesfbQMKRCyzvffRRUX+lK/y+DvsMFdabAicPkkZV6gO+FEWi9RfuKKoxxPwNL+dFF0SMImc1g==}

  micromark-util-classify-character@1.0.0:
    resolution: {integrity: sha512-F8oW2KKrQRb3vS5ud5HIqBVkCqQi224Nm55o5wYLzY/9PwHGXC01tr3d7+TqHHz6zrKQ72Okwtvm/xQm6OVNZA==}

  micromark-util-combine-extensions@1.0.0:
    resolution: {integrity: sha512-J8H058vFBdo/6+AsjHp2NF7AJ02SZtWaVUjsayNFeAiydTxUwViQPxN0Hf8dp4FmCQi0UUFovFsEyRSUmFH3MA==}

  micromark-util-decode-numeric-character-reference@1.0.0:
    resolution: {integrity: sha512-OzO9AI5VUtrTD7KSdagf4MWgHMtET17Ua1fIpXTpuhclCqD8egFWo85GxSGvxgkGS74bEahvtM0WP0HjvV0e4w==}

  micromark-util-decode-string@1.0.2:
    resolution: {integrity: sha512-DLT5Ho02qr6QWVNYbRZ3RYOSSWWFuH3tJexd3dgN1odEuPNxCngTCXJum7+ViRAd9BbdxCvMToPOD/IvVhzG6Q==}

  micromark-util-encode@1.0.1:
    resolution: {integrity: sha512-U2s5YdnAYexjKDel31SVMPbfi+eF8y1U4pfiRW/Y8EFVCy/vgxk/2wWTxzcqE71LHtCuCzlBDRU2a5CQ5j+mQA==}

  micromark-util-html-tag-name@1.1.0:
    resolution: {integrity: sha512-BKlClMmYROy9UiV03SwNmckkjn8QHVaWkqoAqzivabvdGcwNGMMMH/5szAnywmsTBUzDsU57/mFi0sp4BQO6dA==}

  micromark-util-normalize-identifier@1.0.0:
    resolution: {integrity: sha512-yg+zrL14bBTFrQ7n35CmByWUTFsgst5JhA4gJYoty4Dqzj4Z4Fr/DHekSS5aLfH9bdlfnSvKAWsAgJhIbogyBg==}

  micromark-util-resolve-all@1.0.0:
    resolution: {integrity: sha512-CB/AGk98u50k42kvgaMM94wzBqozSzDDaonKU7P7jwQIuH2RU0TeBqGYJz2WY1UdihhjweivStrJ2JdkdEmcfw==}

  micromark-util-sanitize-uri@1.0.0:
    resolution: {integrity: sha512-cCxvBKlmac4rxCGx6ejlIviRaMKZc0fWm5HdCHEeDWRSkn44l6NdYVRyU+0nT1XC72EQJMZV8IPHF+jTr56lAg==}

  micromark-util-subtokenize@1.0.2:
    resolution: {integrity: sha512-d90uqCnXp/cy4G881Ub4psE57Sf8YD0pim9QdjCRNjfas2M1u6Lbt+XZK9gnHL2XFhnozZiEdCa9CNfXSfQ6xA==}

  micromark-util-symbol@1.0.1:
    resolution: {integrity: sha512-oKDEMK2u5qqAptasDAwWDXq0tG9AssVwAx3E9bBF3t/shRIGsWIRG+cGafs2p/SnDSOecnt6hZPCE2o6lHfFmQ==}

  micromark-util-types@1.0.2:
    resolution: {integrity: sha512-DCfg/T8fcrhrRKTPjRrw/5LLvdGV7BHySf/1LOZx7TzWZdYRjogNtyNq885z3nNallwr3QUKARjqvHqX1/7t+w==}

  micromark@3.0.10:
    resolution: {integrity: sha512-ryTDy6UUunOXy2HPjelppgJ2sNfcPz1pLlMdA6Rz9jPzhLikWXv/irpWV/I2jd68Uhmny7hHxAlAhk4+vWggpg==}

  micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimist@1.2.6:
    resolution: {integrity: sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==}

  minipass@2.9.0:
    resolution: {integrity: sha512-wxfUjg9WebH+CUDX/CdbRlh5SmfZiy/hpkxaRI16Y9W56Pa75sWgd/rvFilSgrauD9NyFymP/+JFV3KwzIsJeg==}

  minizlib@1.3.3:
    resolution: {integrity: sha512-6ZYMOEnmVsdCeTJVE0W9ZD+pVnE8h9Hma/iOwwRDsdQoePpoX56/8B6z3P9VNwppJuBKNRuFDRNRqRWexT9G9Q==}

  mitt@3.0.0:
    resolution: {integrity: sha512-7dX2/10ITVyqh4aOSVI9gdape+t9l2/8QxHrFmUXu4EEUpdlxl6RudZUPZoc+zuY2hk1j7XxVroIVIan/pD/SQ==}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  monaco-editor@0.50.0:
    resolution: {integrity: sha512-8CclLCmrRRh+sul7C08BmPBP3P8wVWfBHomsTcndxg5NRCEPfu/mc2AGU8k37ajjDVXcXFc12ORAMUkmk+lkFA==}

  mpegts.js@1.8.0:
    resolution: {integrity: sha512-ZtujqtmTjWgcDDkoOnLvrOKUTO/MKgLHM432zGDI8oPaJ0S+ebPxg1nEpDpLw6I7KmV/GZgUIrfbWi3qqEircg==}

  mri@1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  njre@0.2.0:
    resolution: {integrity: sha512-+Wq8R6VmjK+jI8a9NdzfU6Vh50r3tjsdvl5KJE1OyHeH8I/nx5Ptm12qpO3qNUbstXuZfBDgDL0qQZw9JyjhMw==}
    engines: {node: '>=8'}

  node-fetch@2.6.7:
    resolution: {integrity: sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-html-parser@5.4.1:
    resolution: {integrity: sha512-xy/O2wOEBJsIRLs4avwa1lVY7tIpXXOoHHUJLa0GvnoPPqMG1hgBVl1tNI3GHOwRktTVZy+Y6rjghk4B9/NLyg==}

  node-releases@2.0.6:
    resolution: {integrity: sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  npm-run-path@5.1.0:
    resolution: {integrity: sha512-sJOdmRGrY2sjNTRMbSvluQqg+8X7ZK61yvzBEIDhz4f8z1TZFYABsqjjCBd/0PUNE9M6QDgHJXQkGUEm7Q+l9Q==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  object-inspect@1.12.2:
    resolution: {integrity: sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  onetime@6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}

  option-validator@2.0.6:
    resolution: {integrity: sha512-tmZDan2LRIRQyhUGvkff68/O0R8UmF+Btmiiz0SmSw2ng3CfPZB9wJlIjHpe/MKUZqyIZkVIXCrwr1tIN+0Dzg==}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}

  parse5@7.1.2:
    resolution: {integrity: sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pend@1.2.0:
    resolution: {integrity: sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==}

  picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pidtree@0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    engines: {node: '>=0.10'}
    hasBin: true

  pngjs@5.0.0:
    resolution: {integrity: sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==}
    engines: {node: '>=10.13.0'}

  postcss@8.5.3:
    resolution: {integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==}
    engines: {node: ^10 || ^12 || >=14}

  prettier@3.1.1:
    resolution: {integrity: sha512-22UbSzg8luF4UuZtzgiUOfcGM8s4tjBv6dJRT7j275NXsy2jb4aJa4NNveul5x4eqlF1wuhuR2RElK71RvmVaw==}
    engines: {node: '>=14'}
    hasBin: true

  promise-polyfill@7.1.0:
    resolution: {integrity: sha512-P6NJ2wU/8fac44ENORsuqT8TiolKGB2u0fEClPtXezn7w5cmLIjM/7mhPlTebke2EPr6tmqZbXvnX0TxwykGrg==}

  property-information@6.1.1:
    resolution: {integrity: sha512-hrzC564QIl0r0vy4l6MvRLhafmUowhO/O3KgVSoXIbbA2Sz4j8HGpJc6T2cubRVwMwpdiG/vKGfhT4IixmKN9w==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  qrcode@1.5.4:
    resolution: {integrity: sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  rechoir@0.6.2:
    resolution: {integrity: sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==}
    engines: {node: '>= 0.10'}

  regenerator-runtime@0.13.9:
    resolution: {integrity: sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==}

  regenerator-runtime@0.14.0:
    resolution: {integrity: sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==}

  rehype-katex@6.0.3:
    resolution: {integrity: sha512-ByZlRwRUcWegNbF70CVRm2h/7xy7jQ3R9LaY4VVSvjnoVWwWVhNL60DiZsBpC5tSzYQOCvDbzncIpIjPZWodZA==}

  rehype-raw@6.1.1:
    resolution: {integrity: sha512-d6AKtisSRtDRX4aSPsJGTfnzrX2ZkHQLE5kiUuGOeEoLpbEulFF4hj0mLPbsa+7vmguDKOVVEQdHKDSwoaIDsQ==}

  remark-gfm@3.0.1:
    resolution: {integrity: sha512-lEFDoi2PICJyNrACFOfDD3JlLkuSbOa5Wd8EPt06HUdptv8Gn0bxYTdbU/XXQ3swAPkEaGxxPN9cbnMHvVu1Ig==}

  remark-math@5.1.1:
    resolution: {integrity: sha512-cE5T2R/xLVtfFI4cCePtiRn+e6jKMtFDR3P8V3qpv8wpKjwvHoBA4eJzvX+nVrnlNy0911bdGmuspCSwetfYHw==}

  remark-parse@10.0.1:
    resolution: {integrity: sha512-1fUyHr2jLsVOkhbvPRBJ5zTKZZyD6yZzYaWCS6BPBdQ8vEMBCH+9zNCDA6tET/zHCi/jLqjCWtlJZUPk+DbnFw==}

  remark-rehype@10.1.0:
    resolution: {integrity: sha512-EFmR5zppdBp0WQeDVZ/b66CWJipB2q2VLNFMabzDSGR66Z2fQii83G5gTBbgGEnEEA0QRussvrFHxk1HWGJskw==}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-main-filename@2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  resolve@1.22.1:
    resolution: {integrity: sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==}
    hasBin: true

  restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.3.0:
    resolution: {integrity: sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA==}

  rollup-plugin-copy@3.5.0:
    resolution: {integrity: sha512-wI8D5dvYovRMx/YYKtUNt3Yxaw4ORC9xo6Gt9t22kveWz1enG9QrhVlagzwrxSC455xD1dHMKhIJkbsQ7d48BA==}
    engines: {node: '>=8.3'}

  rollup@3.29.5:
    resolution: {integrity: sha512-GVsDdsbJzzy4S/v3dqWPJ7EfvZJfCHiDqe80IyrF59LYuP+e6U1LJoUqeuqRbwAWoMNoXivMNeNAOf5E22VA1w==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rxjs@7.5.7:
    resolution: {integrity: sha512-z9MzKh/UcOqB3i20H6rtrlaE/CgjLOvheWK/9ILrbhROGTweAi1BaFsTT9FbwZi5Trr1qNRs+MXkhmR06awzQA==}

  sade@1.8.1:
    resolution: {integrity: sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==}
    engines: {node: '>=6'}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  scroll-lock@2.1.5:
    resolution: {integrity: sha512-GN8Lp0AzXbkrPFUUNkMUruiiv019UvarNKE/SnXi+AxZRjMnDc2R22VB9RcUtL4P/uub04cKibmpHKIKTyWwYQ==}

  seemly@0.3.6:
    resolution: {integrity: sha512-lEV5VB8BUKTo/AfktXJcy+JeXns26ylbMkIUco8CYREsQijuz4mrXres2Q+vMLdwkuLxJdIPQ8IlCIxLYm71Yw==}

  semver@6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  sha256@0.2.0:
    resolution: {integrity: sha512-kTWMJUaez5iiT9CcMv8jSq6kMhw3ST0uRdcIWl3D77s6AsLXNXRp3heeqqfu5+Dyfu4hwpQnMzhqHh8iNQxw0w==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shelljs@0.8.5:
    resolution: {integrity: sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==}
    engines: {node: '>=4'}
    hasBin: true

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  slice-ansi@3.0.0:
    resolution: {integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==}
    engines: {node: '>=8'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}

  slice-ansi@5.0.0:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    engines: {node: '>=12'}

  smoothscroll@0.4.0:
    resolution: {integrity: sha512-sggQ3U2Un38b3+q/j1P4Y4fCboCtoUIaBYoge+Lb6Xg1H8RTIif/hugVr+ErMtIDpvBbhQfTjtiTeYAfbw1ZGQ==}

  solid-contextmenu@0.0.2:
    resolution: {integrity: sha512-TmOhIvVpZmQdoiydSP07d9rT0xu/wvRSH6TJqbGPd+2XZz0wOualbm5CvLiHbOGnOU5tBrbe4q0cU/EMvG7u+w==}
    peerDependencies:
      solid-js: ^1.4.8
      solid-transition-group: ^0.0.10

  solid-icons@1.0.1:
    resolution: {integrity: sha512-9rxPeJ1UDGzWGlksjuXuyK2CdL1vg89inDyOl43koL3zoMgXLzY6LVLuMeuhslHbjq7Fp2h6fSSImj9ygfvleA==}
    engines: {node: '>= 16'}
    peerDependencies:
      solid-js: '*'

  solid-js@1.4.8:
    resolution: {integrity: sha512-XErZdnnYYXF7OwGSUAPcua2y5/ELB/c53zFCpWiEGqxTNoH1iQghzI8EsHJXk06sNn+Z/TGhb8bPDNNGSgimag==}

  solid-markdown@1.2.0:
    resolution: {integrity: sha512-OWev1wiNq2f8GOJu8SA6M527UiH2IxcY4L1KAHIfZgRW3snb+W4YqqZ71iuFsPpmaDLr5Jdg8Kc9NYP+I86sjQ==}
    peerDependencies:
      solid-js: ^1.2.0

  solid-refresh@0.4.1:
    resolution: {integrity: sha512-v3tD/OXQcUyXLrWjPW1dXZyeWwP7/+GQNs8YTL09GBq+5FguA6IejJWUvJDrLIA4M0ho9/5zK2e9n+uy+4488g==}
    peerDependencies:
      solid-js: ^1.3

  solid-transition-group@0.0.12:
    resolution: {integrity: sha512-Eu4MfNZSSxM67C+4EXaoDiWr4BFbDLg3j30JQJgQEIH9UDOfgNPfZlsFDhT/PxwJqhe6sn85W+dbmYJzBmjpAA==}
    peerDependencies:
      solid-js: ^1.0.0

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==}

  space-separated-tokens@2.0.1:
    resolution: {integrity: sha512-ekwEbFp5aqSPKaqeY1PGrlGQxPNaq+Cnx4+bE2D8sciBQrHpbwoBbawqTN2+6jPs9IdWxxiUcN0K2pkczD3zmw==}

  state-local@1.0.7:
    resolution: {integrity: sha512-HTEHMNieakEnoe33shBYcZ7NX83ACUjCu8c40iOGEZsngj9zRnkqS9j1pqQPXwobB0ZcVTk27REb7COQ0UR59w==}

  streamsaver@2.0.6:
    resolution: {integrity: sha512-LK4e7TfCV8HzuM0PKXuVUfKyCB1FtT9L0EGxsFk5Up8njj0bXK8pJM9+Wq2Nya7/jslmCQwRK39LFm55h7NBTw==}

  string-argv@0.3.1:
    resolution: {integrity: sha512-a1uQGz7IyVy9YwhqjZIZu1c8JO8dNIe20xBmSS6qu9kv++k3JGzCVmprbNN5Kn+BgzD5E7YYwg1CcjuJMRNsvg==}
    engines: {node: '>=0.6.19'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.0.1:
    resolution: {integrity: sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw==}
    engines: {node: '>=12'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}

  style-to-object@0.3.0:
    resolution: {integrity: sha512-CzFnRRXhzWIdItT3OmF8SQfWyahHhjq3HwcMNCNLn+N7klOOqPjMeG/4JSu77D7ypZdGvSzvkrbyeTMizz2VrA==}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  systemjs@6.12.3:
    resolution: {integrity: sha512-TtYUN86Hs8V1QGAoj9ad1xmJmZS9Lurfi8Iu8QWOKaUDDuTH0Bpfdxz9qZIdxsmvAg3WMQnZ5/pkQvloh2sr/Q==}

  tabbable@5.3.3:
    resolution: {integrity: sha512-QD9qKY3StfbZqWOPLp0++pOrAVb/HbUi5xCc8cUo4XjP19808oaMiDzn0leBY5mCespIBM0CIZePzZjgzR83kA==}

  tar@4.4.19:
    resolution: {integrity: sha512-a20gEsvHnWe0ygBY8JbxoM4w3SJdhc7ZAuxkLqh+nvNQN2IOt0B5lLgM490X5Hl8FF0dl0tOf2ewFYAlIFgzVA==}
    engines: {node: '>=4.5'}

  terser@5.14.2:
    resolution: {integrity: sha512-oL0rGeM/WFQCUd0y2QrWxYnq7tfSuKBiqTjRPWrRgB46WD/kiwHwF8T23z78H6Q6kGCuuHcPB+KULHRdxvVGQA==}
    engines: {node: '>=10'}
    hasBin: true

  through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}

  trough@2.1.0:
    resolution: {integrity: sha512-AqTiAOLcj85xS7vQ8QkAV41hPDIJ71XJB4RCUrzo/1GM2CQwhkJGaf9Hgr7BOugMRpgGUrqRg/DrBDl4H40+8g==}

  ts-toolbelt@9.6.0:
    resolution: {integrity: sha512-nsZd8ZeNUzukXPlJmTBwUAuABDe/9qtVDelJeT/qW0ow3ZS3BsQJtNkan1802aM9Uf68/Y8ljw86Hu0h5IUW3w==}

  tslib@2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}

  type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  typescript-natural-sort@0.7.2:
    resolution: {integrity: sha512-C8gaSIZySgJ+PchvslIJ6j5wcUIKPsGMzUm1gbFSmKjk+YkXTYPLlFc8UJHp2mvUadF80pWPgsxsAOPIP3TZ4g==}

  typescript@4.7.4:
    resolution: {integrity: sha512-C0WQT0gezHuw6AdY1M2jxUO83Rjf0HP7Sk1DtXj6j1EwkQNZrHAg2XPWlq62oqEhYvONq5pkC2Y9oPljWToLmQ==}
    engines: {node: '>=4.2.0'}
    hasBin: true

  uint8-util@2.2.5:
    resolution: {integrity: sha512-/QxVQD7CttWpVUKVPz9znO+3Dd4BdTSnFQ7pv/4drVhC9m4BaL2LFHTkJn6EsYoxT79VDq/2Gg8L0H22PrzyMw==}

  unified@10.1.2:
    resolution: {integrity: sha512-pUSWAi/RAnVy1Pif2kAoeWNBa3JVrx0MId2LASj8G+7AiHWoKZNTomq6LG326T68U7/e263X6fTdcXIy7XnF7Q==}

  unist-builder@3.0.0:
    resolution: {integrity: sha512-GFxmfEAa0vi9i5sd0R2kcrI9ks0r82NasRq5QHh2ysGngrc6GiqD5CDf1FjPenY4vApmFASBIIlk/jj5J5YbmQ==}

  unist-util-find-after@4.0.1:
    resolution: {integrity: sha512-QO/PuPMm2ERxC6vFXEPtmAutOopy5PknD+Oq64gGwxKtk4xwo9Z97t9Av1obPmGU0IyTa6EKYUfTrK2QJS3Ozw==}

  unist-util-generated@2.0.0:
    resolution: {integrity: sha512-TiWE6DVtVe7Ye2QxOVW9kqybs6cZexNwTwSMVgkfjEReqy/xwGpAXb99OxktoWwmL+Z+Epb0Dn8/GNDYP1wnUw==}

  unist-util-is@5.1.1:
    resolution: {integrity: sha512-F5CZ68eYzuSvJjGhCLPL3cYx45IxkqXSetCcRgUXtbcm50X2L9oOWQlfUfDdAf+6Pd27YDblBfdtmsThXmwpbQ==}

  unist-util-position@4.0.3:
    resolution: {integrity: sha512-p/5EMGIa1qwbXjA+QgcBXaPWjSnZfQ2Sc3yBEEfgPwsEmJd8Qh+DSk3LGnmOM4S1bY2C0AjmMnB8RuEYxpPwXQ==}

  unist-util-remove-position@4.0.2:
    resolution: {integrity: sha512-TkBb0HABNmxzAcfLf4qsIbFbaPDvMO6wa3b3j4VcEzFVaw1LBKwnW4/sRJ/atSLSzoIg41JWEdnE7N6DIhGDGQ==}

  unist-util-stringify-position@3.0.2:
    resolution: {integrity: sha512-7A6eiDCs9UtjcwZOcCpM4aPII3bAAGv13E96IkawkOAW0OhH+yRxtY0lzo8KiHpzEMfH7Q+FizUmwp8Iqy5EWg==}

  unist-util-visit-parents@5.1.0:
    resolution: {integrity: sha512-y+QVLcY5eR/YVpqDsLf/xh9R3Q2Y4HxkZTp7ViLDU6WtJCEcPmRzW1gpdWDCDIqIlhuPDXOgttqPlykrHYDekg==}

  unist-util-visit@4.1.0:
    resolution: {integrity: sha512-n7lyhFKJfVZ9MnKtqbsqkQEk5P1KShj0+//V7mAcoI6bpbUjh3C/OG8HVD+pBihfh6Ovl01m8dkcv9HNqYajmQ==}

  universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}

  update-browserslist-db@1.0.5:
    resolution: {integrity: sha512-dteFFpCyvuDdr9S/ff1ISkKt/9YZxKjI9WlRR99c180GaztJtRa/fn18FdxGVKVsnPY7/a/FDN68mcvUmP4U7Q==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uvu@0.5.6:
    resolution: {integrity: sha512-+g8ENReyr8YsOc6fv/NVJs2vFdHBnBNdfE49rshrTzDWOlUx4Gq7KOS2GD8eqhy2j+Ejq29+SbKH8yjkAqXqoA==}
    engines: {node: '>=8'}
    hasBin: true

  vfile-location@4.0.1:
    resolution: {integrity: sha512-JDxPlTbZrZCQXogGheBHjbRWjESSPEak770XwWPfw5mTc1v1nWGLB/apzZxsx8a0SJVfF8HK8ql8RD308vXRUw==}

  vfile-message@3.1.2:
    resolution: {integrity: sha512-QjSNP6Yxzyycd4SVOtmKKyTsSvClqBPJcd00Z0zuPj3hOIjg0rUPG6DbFGPvUKRgYyaIWLPKpuEclcuvb3H8qA==}

  vfile@5.3.4:
    resolution: {integrity: sha512-KI+7cnst03KbEyN1+JE504zF5bJBZa+J+CrevLeyIMq0aPU681I2rQ5p4PlnQ6exFtWiUrg26QUdFMnAKR6PIw==}

  vite-plugin-dynamic-base@0.4.4:
    resolution: {integrity: sha512-++d35lUSWhIRcoKfTUlWSe3BFMTJPB0iQzAhNCNwiFILfouXAosGhB0DNjtxiIuHSORSrxwawzlwByAGbDuJgA==}

  vite-plugin-solid@2.3.0:
    resolution: {integrity: sha512-N2sa54C3UZC2nN5vpj5o6YP+XdIAZW6n6xv8OasxNAcAJPFeZT7EOVvumL0V4c8hBz1yuYniMWdESY8807fVSg==}
    peerDependencies:
      solid-js: ^1.3.17
      vite: ^3.0.0

  vite@4.5.13:
    resolution: {integrity: sha512-Hgp8IF/yZDzKsN1hQWOuQZbrKiaFsbQud+07jJ8h9m9PaHWkpvZ5u55Xw5yYjWRXwRQ4jwFlJvY7T7FUJG9MCA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  web-namespaces@2.0.1:
    resolution: {integrity: sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  webworkify-webpack@https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef:
    resolution: {tarball: https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef}
    version: 2.1.5

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which-module@2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yaml@2.1.3:
    resolution: {integrity: sha512-AacA8nRULjKMX2DvWvOAdBZMOfQlypSFkjcOcu9FalllIDJ1kvlREzcdIZmidQUqqeMv7jorHjq2HlLv/+c2lg==}
    engines: {node: '>= 14'}

  yargs-parser@18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}

  yargs@15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}

  yauzl@2.10.0:
    resolution: {integrity: sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==}

  zwitch@2.0.2:
    resolution: {integrity: sha512-JZxotl7SxAJH0j7dN4pxsTV6ZLXoLdGME+PsjkL/DaBrVryK9kTGq06GfKrwcSOqypP+fdXGoCHE36b99fWVoA==}

snapshots:

  '@ampproject/remapping@2.2.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.1.1
      '@jridgewell/trace-mapping': 0.3.15

  '@babel/code-frame@7.18.6':
    dependencies:
      '@babel/highlight': 7.18.6

  '@babel/compat-data@7.18.8': {}

  '@babel/core@7.18.10':
    dependencies:
      '@ampproject/remapping': 2.2.0
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.18.12
      '@babel/helper-compilation-targets': 7.18.9(@babel/core@7.18.10)
      '@babel/helper-module-transforms': 7.18.9
      '@babel/helpers': 7.18.9
      '@babel/parser': 7.18.11
      '@babel/template': 7.18.10
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
      convert-source-map: 1.8.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.18.12':
    dependencies:
      '@babel/types': 7.18.10
      '@jridgewell/gen-mapping': 0.3.2
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.18.6':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-compilation-targets@7.18.9(@babel/core@7.18.10)':
    dependencies:
      '@babel/compat-data': 7.18.8
      '@babel/core': 7.18.10
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.3
      semver: 6.3.0

  '@babel/helper-create-class-features-plugin@7.18.9(@babel/core@7.18.10)':
    dependencies:
      '@babel/core': 7.18.10
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.18.9
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.18.9': {}

  '@babel/helper-function-name@7.18.9':
    dependencies:
      '@babel/template': 7.18.10
      '@babel/types': 7.18.10

  '@babel/helper-hoist-variables@7.18.6':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-member-expression-to-functions@7.18.9':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-module-imports@7.16.0':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-module-imports@7.18.6':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-module-transforms@7.18.9':
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-simple-access': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.18.6
      '@babel/template': 7.18.10
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.18.6':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-plugin-utils@7.18.9': {}

  '@babel/helper-replace-supers@7.18.9':
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-simple-access@7.18.6':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-split-export-declaration@7.18.6':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/helper-string-parser@7.18.10': {}

  '@babel/helper-validator-identifier@7.18.6': {}

  '@babel/helper-validator-option@7.18.6': {}

  '@babel/helpers@7.18.9':
    dependencies:
      '@babel/template': 7.18.10
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.18.6':
    dependencies:
      '@babel/helper-validator-identifier': 7.18.6
      chalk: 2.4.2
      js-tokens: 4.0.0

  '@babel/parser@7.18.11':
    dependencies:
      '@babel/types': 7.18.10

  '@babel/plugin-syntax-jsx@7.18.6(@babel/core@7.18.10)':
    dependencies:
      '@babel/core': 7.18.10
      '@babel/helper-plugin-utils': 7.18.9

  '@babel/plugin-syntax-typescript@7.18.6(@babel/core@7.18.10)':
    dependencies:
      '@babel/core': 7.18.10
      '@babel/helper-plugin-utils': 7.18.9

  '@babel/plugin-transform-typescript@7.18.12(@babel/core@7.18.10)':
    dependencies:
      '@babel/core': 7.18.10
      '@babel/helper-create-class-features-plugin': 7.18.9(@babel/core@7.18.10)
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-typescript': 7.18.6(@babel/core@7.18.10)
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-typescript@7.18.6(@babel/core@7.18.10)':
    dependencies:
      '@babel/core': 7.18.10
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-validator-option': 7.18.6
      '@babel/plugin-transform-typescript': 7.18.12(@babel/core@7.18.10)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.23.6':
    dependencies:
      regenerator-runtime: 0.14.0

  '@babel/standalone@7.18.12': {}

  '@babel/template@7.18.10':
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.18.11
      '@babel/types': 7.18.10

  '@babel/traverse@7.18.11':
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.18.12
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.18.11
      '@babel/types': 7.18.10
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.18.10':
    dependencies:
      '@babel/helper-string-parser': 7.18.10
      '@babel/helper-validator-identifier': 7.18.6
      to-fast-properties: 2.0.0

  '@cfcs/core@0.0.24':
    dependencies:
      '@egjs/component': 3.0.5

  '@crowdin/cli@3.7.10':
    dependencies:
      njre: 0.2.0
      shelljs: 0.8.5
    transitivePeerDependencies:
      - encoding

  '@egjs/component@3.0.5': {}

  '@egjs/imready@1.4.1':
    dependencies:
      '@cfcs/core': 0.0.24
      '@egjs/component': 3.0.5

  '@egjs/view360@4.0.0-beta.7':
    dependencies:
      '@egjs/component': 3.0.5
      '@egjs/imready': 1.4.1
      '@types/webxr': 0.5.19
      gl-matrix: 3.4.3

  '@esbuild/android-arm64@0.18.20':
    optional: true

  '@esbuild/android-arm@0.18.20':
    optional: true

  '@esbuild/android-x64@0.18.20':
    optional: true

  '@esbuild/darwin-arm64@0.18.20':
    optional: true

  '@esbuild/darwin-x64@0.18.20':
    optional: true

  '@esbuild/freebsd-arm64@0.18.20':
    optional: true

  '@esbuild/freebsd-x64@0.18.20':
    optional: true

  '@esbuild/linux-arm64@0.18.20':
    optional: true

  '@esbuild/linux-arm@0.18.20':
    optional: true

  '@esbuild/linux-ia32@0.18.20':
    optional: true

  '@esbuild/linux-loong64@0.18.20':
    optional: true

  '@esbuild/linux-mips64el@0.18.20':
    optional: true

  '@esbuild/linux-ppc64@0.18.20':
    optional: true

  '@esbuild/linux-riscv64@0.18.20':
    optional: true

  '@esbuild/linux-s390x@0.18.20':
    optional: true

  '@esbuild/linux-x64@0.18.20':
    optional: true

  '@esbuild/netbsd-x64@0.18.20':
    optional: true

  '@esbuild/openbsd-x64@0.18.20':
    optional: true

  '@esbuild/sunos-x64@0.18.20':
    optional: true

  '@esbuild/win32-arm64@0.18.20':
    optional: true

  '@esbuild/win32-ia32@0.18.20':
    optional: true

  '@esbuild/win32-x64@0.18.20':
    optional: true

  '@floating-ui/core@0.6.2': {}

  '@floating-ui/dom@0.4.4':
    dependencies:
      '@floating-ui/core': 0.6.2

  '@github/webauthn-json@2.1.1': {}

  '@hope-ui/solid@0.6.7(@stitches/core@1.2.8)(solid-js@1.4.8)(solid-transition-group@0.0.12(solid-js@1.4.8))':
    dependencies:
      '@floating-ui/dom': 0.4.4
      '@stitches/core': 1.2.8
      csstype: 3.0.11
      focus-trap: 6.7.3
      lodash.merge: 4.6.2
      scroll-lock: 2.1.5
      solid-js: 1.4.8
      solid-transition-group: 0.0.12(solid-js@1.4.8)

  '@hrgui/libass-wasm-ts@1.0.3': {}

  '@jridgewell/gen-mapping@0.1.1':
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.14

  '@jridgewell/gen-mapping@0.3.2':
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.14
      '@jridgewell/trace-mapping': 0.3.15

  '@jridgewell/resolve-uri@3.1.0': {}

  '@jridgewell/set-array@1.1.2': {}

  '@jridgewell/source-map@0.3.2':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.2
      '@jridgewell/trace-mapping': 0.3.15

  '@jridgewell/sourcemap-codec@1.4.14': {}

  '@jridgewell/trace-mapping@0.3.15':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.0
      '@jridgewell/sourcemap-codec': 1.4.14

  '@monaco-editor/loader@1.4.0(monaco-editor@0.50.0)':
    dependencies:
      monaco-editor: 0.50.0
      state-local: 1.0.7

  '@motionone/animation@10.14.0':
    dependencies:
      '@motionone/easing': 10.14.0
      '@motionone/types': 10.14.0
      '@motionone/utils': 10.14.0
      tslib: 2.4.0

  '@motionone/dom@10.14.1':
    dependencies:
      '@motionone/animation': 10.14.0
      '@motionone/generators': 10.14.0
      '@motionone/types': 10.14.0
      '@motionone/utils': 10.14.0
      hey-listen: 1.0.8
      tslib: 2.4.0

  '@motionone/easing@10.14.0':
    dependencies:
      '@motionone/utils': 10.14.0
      tslib: 2.4.0

  '@motionone/generators@10.14.0':
    dependencies:
      '@motionone/types': 10.14.0
      '@motionone/utils': 10.14.0
      tslib: 2.4.0

  '@motionone/solid@10.14.1(solid-js@1.4.8)':
    dependencies:
      '@motionone/dom': 10.14.1
      '@motionone/utils': 10.14.0
      '@solid-primitives/props': 2.2.2(solid-js@1.4.8)
      solid-js: 1.4.8
      tslib: 2.4.0

  '@motionone/types@10.14.0': {}

  '@motionone/utils@10.14.0':
    dependencies:
      '@motionone/types': 10.14.0
      hey-listen: 1.0.8
      tslib: 2.4.0

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0

  '@solid-primitives/event-listener@2.3.0(solid-js@1.4.8)':
    dependencies:
      '@solid-primitives/utils': 6.2.1(solid-js@1.4.8)
      solid-js: 1.4.8

  '@solid-primitives/i18n@1.1.0(solid-js@1.4.8)':
    dependencies:
      solid-js: 1.4.8

  '@solid-primitives/keyboard@1.2.5(solid-js@1.4.8)':
    dependencies:
      '@solid-primitives/event-listener': 2.3.0(solid-js@1.4.8)
      '@solid-primitives/rootless': 1.4.2(solid-js@1.4.8)
      '@solid-primitives/utils': 6.2.1(solid-js@1.4.8)
      solid-js: 1.4.8

  '@solid-primitives/props@2.2.2(solid-js@1.4.8)':
    dependencies:
      '@solid-primitives/utils': 3.0.2(solid-js@1.4.8)
      solid-js: 1.4.8

  '@solid-primitives/rootless@1.4.2(solid-js@1.4.8)':
    dependencies:
      '@solid-primitives/utils': 6.2.1(solid-js@1.4.8)
      solid-js: 1.4.8

  '@solid-primitives/storage@1.3.1(solid-js@1.4.8)':
    dependencies:
      solid-js: 1.4.8

  '@solid-primitives/utils@3.0.2(solid-js@1.4.8)':
    dependencies:
      solid-js: 1.4.8

  '@solid-primitives/utils@6.2.1(solid-js@1.4.8)':
    dependencies:
      solid-js: 1.4.8

  '@stitches/core@1.2.8': {}

  '@types/bencode@2.0.4':
    dependencies:
      '@types/node': 20.0.0

  '@types/crypto-js@4.2.2': {}

  '@types/debug@4.1.7':
    dependencies:
      '@types/ms': 0.7.31

  '@types/fs-extra@8.1.4':
    dependencies:
      '@types/node': 20.0.0

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 20.0.0

  '@types/hast@2.3.4':
    dependencies:
      '@types/unist': 2.0.6

  '@types/jquery@3.5.16':
    dependencies:
      '@types/sizzle': 2.3.3

  '@types/katex@0.14.0': {}

  '@types/katex@0.16.0': {}

  '@types/mark.js@8.11.8':
    dependencies:
      '@types/jquery': 3.5.16

  '@types/mdast@3.0.10':
    dependencies:
      '@types/unist': 2.0.6

  '@types/mdurl@1.0.2': {}

  '@types/minimatch@5.1.2': {}

  '@types/ms@0.7.31': {}

  '@types/node@20.0.0': {}

  '@types/parse5@6.0.3': {}

  '@types/qrcode@1.5.5':
    dependencies:
      '@types/node': 20.0.0

  '@types/sha256@0.2.0':
    dependencies:
      '@types/node': 20.0.0

  '@types/sizzle@2.3.3': {}

  '@types/streamsaver@2.0.1': {}

  '@types/unist@2.0.6': {}

  '@types/webxr@0.5.19': {}

  '@viselect/vanilla@3.5.0': {}

  '@vitejs/plugin-legacy@2.0.1(terser@5.14.2)(vite@4.5.13(@types/node@20.0.0)(terser@5.14.2))':
    dependencies:
      '@babel/standalone': 7.18.12
      core-js: 3.24.1
      magic-string: 0.26.2
      regenerator-runtime: 0.13.9
      systemjs: 6.12.3
      terser: 5.14.2
      vite: 4.5.13(@types/node@20.0.0)(terser@5.14.2)

  acorn@8.8.0: {}

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  aplayer@1.10.1:
    dependencies:
      balloon-css: 0.5.2
      promise-polyfill: 7.1.0
      smoothscroll: 0.4.0

  array-union@2.1.0: {}

  artplayer-plugin-danmuku@5.1.5: {}

  artplayer@5.2.2:
    dependencies:
      option-validator: 2.0.6

  asciinema-player@3.6.3:
    dependencies:
      '@babel/runtime': 7.23.6
      solid-js: 1.4.8

  astral-regex@2.0.0: {}

  asynckit@0.4.0: {}

  axios@1.1.3:
    dependencies:
      follow-redirects: 1.15.1
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-plugin-jsx-dom-expressions@0.33.14(@babel/core@7.18.10):
    dependencies:
      '@babel/core': 7.18.10
      '@babel/helper-module-imports': 7.16.0
      '@babel/plugin-syntax-jsx': 7.18.6(@babel/core@7.18.10)
      '@babel/types': 7.18.10
      html-entities: 2.3.2

  babel-preset-solid@1.4.8(@babel/core@7.18.10):
    dependencies:
      '@babel/core': 7.18.10
      babel-plugin-jsx-dom-expressions: 0.33.14(@babel/core@7.18.10)

  bail@2.0.2: {}

  balanced-match@1.0.2: {}

  balloon-css@0.5.2: {}

  base64-arraybuffer@1.0.2: {}

  bencode@4.0.0:
    dependencies:
      uint8-util: 2.2.5

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browserslist@4.21.3:
    dependencies:
      caniuse-lite: 1.0.30001377
      electron-to-chromium: 1.4.221
      node-releases: 2.0.6
      update-browserslist-db: 1.0.5(browserslist@4.21.3)

  buffer-crc32@0.2.13: {}

  buffer-from@1.1.2: {}

  camelcase@5.3.1: {}

  caniuse-lite@1.0.30001377: {}

  ccount@2.0.1: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  character-entities@2.0.2: {}

  chardet@2.0.0: {}

  chownr@1.1.4: {}

  clean-stack@2.2.0: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-truncate@2.1.0:
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3

  cli-truncate@3.1.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 5.1.2

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  clsx@1.2.1: {}

  clsx@2.0.0: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@1.4.0: {}

  colorette@2.0.19: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  comma-separated-tokens@2.0.2: {}

  command-exists-promise@2.0.2: {}

  commander@2.20.3: {}

  commander@8.3.0: {}

  commander@9.4.1: {}

  concat-map@0.0.1: {}

  convert-hex@0.1.0: {}

  convert-source-map@1.8.0:
    dependencies:
      safe-buffer: 5.1.2

  convert-string@0.1.0: {}

  copy-to-clipboard@3.3.2:
    dependencies:
      toggle-selection: 1.0.6

  core-js@3.24.1: {}

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-js@4.2.0: {}

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-what@6.1.0: {}

  csstype@3.0.11: {}

  debug@4.3.4:
    dependencies:
      ms: 2.1.2

  decamelize@1.2.0: {}

  decode-named-character-reference@1.0.2:
    dependencies:
      character-entities: 2.0.2

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  diff@5.1.0: {}

  dijkstrajs@1.0.3: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  domelementtype@2.3.0: {}

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  eastasianwidth@0.2.0: {}

  electron-to-chromium@1.4.221: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  entities@2.2.0: {}

  entities@4.5.0: {}

  es6-promise@4.2.8: {}

  esbuild@0.18.20:
    optionalDependencies:
      '@esbuild/android-arm': 0.18.20
      '@esbuild/android-arm64': 0.18.20
      '@esbuild/android-x64': 0.18.20
      '@esbuild/darwin-arm64': 0.18.20
      '@esbuild/darwin-x64': 0.18.20
      '@esbuild/freebsd-arm64': 0.18.20
      '@esbuild/freebsd-x64': 0.18.20
      '@esbuild/linux-arm': 0.18.20
      '@esbuild/linux-arm64': 0.18.20
      '@esbuild/linux-ia32': 0.18.20
      '@esbuild/linux-loong64': 0.18.20
      '@esbuild/linux-mips64el': 0.18.20
      '@esbuild/linux-ppc64': 0.18.20
      '@esbuild/linux-riscv64': 0.18.20
      '@esbuild/linux-s390x': 0.18.20
      '@esbuild/linux-x64': 0.18.20
      '@esbuild/netbsd-x64': 0.18.20
      '@esbuild/openbsd-x64': 0.18.20
      '@esbuild/sunos-x64': 0.18.20
      '@esbuild/win32-arm64': 0.18.20
      '@esbuild/win32-ia32': 0.18.20
      '@esbuild/win32-x64': 0.18.20

  escalade@3.1.1: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@5.0.0: {}

  execa@6.1.0:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 3.0.1
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.1.0
      onetime: 6.0.0
      signal-exit: 3.0.7
      strip-final-newline: 3.0.0

  extend@3.0.2: {}

  fast-glob@3.3.1:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  fastq@1.15.0:
    dependencies:
      reusify: 1.0.4

  fd-slicer@1.1.0:
    dependencies:
      pend: 1.2.0

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  focus-trap@6.7.3:
    dependencies:
      tabbable: 5.3.3

  follow-redirects@1.15.1: {}

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  fs-extra@8.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-minipass@1.2.7:
    dependencies:
      minipass: 2.9.0

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.1: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-stream@6.0.1: {}

  gl-matrix@3.4.3: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globby@10.0.1:
    dependencies:
      '@types/glob': 7.2.0
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.1
      glob: 7.2.3
      ignore: 5.2.4
      merge2: 1.4.1
      slash: 3.0.0

  graceful-fs@4.2.11: {}

  has-flag@3.0.0: {}

  has@1.0.3:
    dependencies:
      function-bind: 1.1.1

  hash-wasm@4.12.0: {}

  hast-to-hyperscript@10.0.1:
    dependencies:
      '@types/unist': 2.0.6
      comma-separated-tokens: 2.0.2
      property-information: 6.1.1
      space-separated-tokens: 2.0.1
      style-to-object: 0.3.0
      unist-util-is: 5.1.1
      web-namespaces: 2.0.1

  hast-util-from-dom@4.2.0:
    dependencies:
      hastscript: 7.0.2
      web-namespaces: 2.0.1

  hast-util-from-html-isomorphic@1.0.0:
    dependencies:
      '@types/hast': 2.3.4
      hast-util-from-dom: 4.2.0
      hast-util-from-html: 1.0.2
      unist-util-remove-position: 4.0.2

  hast-util-from-html@1.0.2:
    dependencies:
      '@types/hast': 2.3.4
      hast-util-from-parse5: 7.1.0
      parse5: 7.1.2
      vfile: 5.3.4
      vfile-message: 3.1.2

  hast-util-from-parse5@7.1.0:
    dependencies:
      '@types/hast': 2.3.4
      '@types/parse5': 6.0.3
      '@types/unist': 2.0.6
      hastscript: 7.0.2
      property-information: 6.1.1
      vfile: 5.3.4
      vfile-location: 4.0.1
      web-namespaces: 2.0.1

  hast-util-is-element@2.1.3:
    dependencies:
      '@types/hast': 2.3.4
      '@types/unist': 2.0.6

  hast-util-parse-selector@3.1.0:
    dependencies:
      '@types/hast': 2.3.4

  hast-util-raw@7.2.2:
    dependencies:
      '@types/hast': 2.3.4
      '@types/parse5': 6.0.3
      hast-util-from-parse5: 7.1.0
      hast-util-to-parse5: 7.0.0
      html-void-elements: 2.0.1
      parse5: 6.0.1
      unist-util-position: 4.0.3
      unist-util-visit: 4.1.0
      vfile: 5.3.4
      web-namespaces: 2.0.1
      zwitch: 2.0.2

  hast-util-to-parse5@7.0.0:
    dependencies:
      '@types/hast': 2.3.4
      '@types/parse5': 6.0.3
      hast-to-hyperscript: 10.0.1
      property-information: 6.1.1
      web-namespaces: 2.0.1
      zwitch: 2.0.2

  hast-util-to-text@3.1.2:
    dependencies:
      '@types/hast': 2.3.4
      '@types/unist': 2.0.6
      hast-util-is-element: 2.1.3
      unist-util-find-after: 4.0.1

  hastscript@7.0.2:
    dependencies:
      '@types/hast': 2.3.4
      comma-separated-tokens: 2.0.2
      hast-util-parse-selector: 3.1.0
      property-information: 6.1.1
      space-separated-tokens: 2.0.1

  he@1.2.0: {}

  hey-listen@1.0.8: {}

  hls.js@1.6.1: {}

  html-entities@2.3.2: {}

  html-void-elements@2.0.1: {}

  human-signals@3.0.1: {}

  husky@8.0.2: {}

  ignore@5.2.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inline-style-parser@0.1.1: {}

  interpret@1.4.0: {}

  is-buffer@2.0.5: {}

  is-core-module@2.10.0:
    dependencies:
      has: 1.0.3

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-plain-obj@4.1.0: {}

  is-plain-object@3.0.1: {}

  is-stream@3.0.0: {}

  is-what@4.1.7: {}

  isexe@2.0.0: {}

  js-tokens@4.0.0: {}

  jsesc@2.5.2: {}

  json5@2.2.1: {}

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  just-once@2.2.0: {}

  katex@0.16.8:
    dependencies:
      commander: 8.3.0

  kind-of@6.0.3: {}

  kleur@4.1.5: {}

  libass-wasm@4.1.0: {}

  lightgallery@2.5.0: {}

  lilconfig@2.0.6: {}

  lint-staged@13.0.4:
    dependencies:
      cli-truncate: 3.1.0
      colorette: 2.0.19
      commander: 9.4.1
      debug: 4.3.4
      execa: 6.1.0
      lilconfig: 2.0.6
      listr2: 5.0.6
      micromatch: 4.0.5
      normalize-path: 3.0.0
      object-inspect: 1.12.2
      pidtree: 0.6.0
      string-argv: 0.3.1
      yaml: 2.1.3
    transitivePeerDependencies:
      - enquirer
      - supports-color

  listr2@5.0.6:
    dependencies:
      cli-truncate: 2.1.0
      colorette: 2.0.19
      log-update: 4.0.0
      p-map: 4.0.0
      rfdc: 1.3.0
      rxjs: 7.5.7
      through: 2.3.8
      wrap-ansi: 7.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash.merge@4.6.2: {}

  log-update@4.0.0:
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0

  longest-streak@3.0.1: {}

  magic-string@0.26.2:
    dependencies:
      sourcemap-codec: 1.4.8

  mark.js@8.11.1: {}

  markdown-table@3.0.2: {}

  mdast-util-definitions@5.1.1:
    dependencies:
      '@types/mdast': 3.0.10
      '@types/unist': 2.0.6
      unist-util-visit: 4.1.0

  mdast-util-find-and-replace@2.2.1:
    dependencies:
      escape-string-regexp: 5.0.0
      unist-util-is: 5.1.1
      unist-util-visit-parents: 5.1.0

  mdast-util-from-markdown@1.2.0:
    dependencies:
      '@types/mdast': 3.0.10
      '@types/unist': 2.0.6
      decode-named-character-reference: 1.0.2
      mdast-util-to-string: 3.1.0
      micromark: 3.0.10
      micromark-util-decode-numeric-character-reference: 1.0.0
      micromark-util-decode-string: 1.0.2
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      unist-util-stringify-position: 3.0.2
      uvu: 0.5.6
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-autolink-literal@1.0.2:
    dependencies:
      '@types/mdast': 3.0.10
      ccount: 2.0.1
      mdast-util-find-and-replace: 2.2.1
      micromark-util-character: 1.1.0

  mdast-util-gfm-footnote@1.0.1:
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-markdown: 1.3.0
      micromark-util-normalize-identifier: 1.0.0

  mdast-util-gfm-strikethrough@1.0.1:
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-markdown: 1.3.0

  mdast-util-gfm-table@1.0.4:
    dependencies:
      markdown-table: 3.0.2
      mdast-util-from-markdown: 1.2.0
      mdast-util-to-markdown: 1.3.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-task-list-item@1.0.1:
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-markdown: 1.3.0

  mdast-util-gfm@2.0.1:
    dependencies:
      mdast-util-from-markdown: 1.2.0
      mdast-util-gfm-autolink-literal: 1.0.2
      mdast-util-gfm-footnote: 1.0.1
      mdast-util-gfm-strikethrough: 1.0.1
      mdast-util-gfm-table: 1.0.4
      mdast-util-gfm-task-list-item: 1.0.1
      mdast-util-to-markdown: 1.3.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-math@2.0.2:
    dependencies:
      '@types/mdast': 3.0.10
      longest-streak: 3.0.1
      mdast-util-to-markdown: 1.3.0

  mdast-util-to-hast@12.2.0:
    dependencies:
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      '@types/mdurl': 1.0.2
      mdast-util-definitions: 5.1.1
      mdurl: 1.0.1
      micromark-util-sanitize-uri: 1.0.0
      trim-lines: 3.0.1
      unist-builder: 3.0.0
      unist-util-generated: 2.0.0
      unist-util-position: 4.0.3
      unist-util-visit: 4.1.0

  mdast-util-to-markdown@1.3.0:
    dependencies:
      '@types/mdast': 3.0.10
      '@types/unist': 2.0.6
      longest-streak: 3.0.1
      mdast-util-to-string: 3.1.0
      micromark-util-decode-string: 1.0.2
      unist-util-visit: 4.1.0
      zwitch: 2.0.2

  mdast-util-to-string@3.1.0: {}

  mdurl@1.0.1: {}

  merge-anything@5.0.2:
    dependencies:
      is-what: 4.1.7
      ts-toolbelt: 9.6.0

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromark-core-commonmark@1.0.6:
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-factory-destination: 1.0.0
      micromark-factory-label: 1.0.2
      micromark-factory-space: 1.0.0
      micromark-factory-title: 1.0.2
      micromark-factory-whitespace: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-chunked: 1.0.0
      micromark-util-classify-character: 1.0.0
      micromark-util-html-tag-name: 1.1.0
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-resolve-all: 1.0.0
      micromark-util-subtokenize: 1.0.2
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-extension-gfm-autolink-literal@1.0.3:
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-sanitize-uri: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-extension-gfm-footnote@1.0.4:
    dependencies:
      micromark-core-commonmark: 1.0.6
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-sanitize-uri: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-extension-gfm-strikethrough@1.0.4:
    dependencies:
      micromark-util-chunked: 1.0.0
      micromark-util-classify-character: 1.0.0
      micromark-util-resolve-all: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-extension-gfm-table@1.0.5:
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-extension-gfm-tagfilter@1.0.1:
    dependencies:
      micromark-util-types: 1.0.2

  micromark-extension-gfm-task-list-item@1.0.3:
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-extension-gfm@2.0.1:
    dependencies:
      micromark-extension-gfm-autolink-literal: 1.0.3
      micromark-extension-gfm-footnote: 1.0.4
      micromark-extension-gfm-strikethrough: 1.0.4
      micromark-extension-gfm-table: 1.0.5
      micromark-extension-gfm-tagfilter: 1.0.1
      micromark-extension-gfm-task-list-item: 1.0.3
      micromark-util-combine-extensions: 1.0.0
      micromark-util-types: 1.0.2

  micromark-extension-math@2.1.2:
    dependencies:
      '@types/katex': 0.16.0
      katex: 0.16.8
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-factory-destination@1.0.0:
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2

  micromark-factory-label@1.0.2:
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-factory-space@1.0.0:
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-types: 1.0.2

  micromark-factory-title@1.0.2:
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-factory-whitespace@1.0.0:
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2

  micromark-util-character@1.1.0:
    dependencies:
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2

  micromark-util-chunked@1.0.0:
    dependencies:
      micromark-util-symbol: 1.0.1

  micromark-util-classify-character@1.0.0:
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2

  micromark-util-combine-extensions@1.0.0:
    dependencies:
      micromark-util-chunked: 1.0.0
      micromark-util-types: 1.0.2

  micromark-util-decode-numeric-character-reference@1.0.0:
    dependencies:
      micromark-util-symbol: 1.0.1

  micromark-util-decode-string@1.0.2:
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-util-character: 1.1.0
      micromark-util-decode-numeric-character-reference: 1.0.0
      micromark-util-symbol: 1.0.1

  micromark-util-encode@1.0.1: {}

  micromark-util-html-tag-name@1.1.0: {}

  micromark-util-normalize-identifier@1.0.0:
    dependencies:
      micromark-util-symbol: 1.0.1

  micromark-util-resolve-all@1.0.0:
    dependencies:
      micromark-util-types: 1.0.2

  micromark-util-sanitize-uri@1.0.0:
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-encode: 1.0.1
      micromark-util-symbol: 1.0.1

  micromark-util-subtokenize@1.0.2:
    dependencies:
      micromark-util-chunked: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6

  micromark-util-symbol@1.0.1: {}

  micromark-util-types@1.0.2: {}

  micromark@3.0.10:
    dependencies:
      '@types/debug': 4.1.7
      debug: 4.3.4
      decode-named-character-reference: 1.0.2
      micromark-core-commonmark: 1.0.6
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-chunked: 1.0.0
      micromark-util-combine-extensions: 1.0.0
      micromark-util-decode-numeric-character-reference: 1.0.0
      micromark-util-encode: 1.0.1
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-resolve-all: 1.0.0
      micromark-util-sanitize-uri: 1.0.0
      micromark-util-subtokenize: 1.0.2
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    transitivePeerDependencies:
      - supports-color

  micromatch@4.0.5:
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@2.1.0: {}

  mimic-fn@4.0.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimist@1.2.6: {}

  minipass@2.9.0:
    dependencies:
      safe-buffer: 5.2.1
      yallist: 3.1.1

  minizlib@1.3.3:
    dependencies:
      minipass: 2.9.0

  mitt@3.0.0: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.6

  monaco-editor@0.50.0: {}

  mpegts.js@1.8.0:
    dependencies:
      es6-promise: 4.2.8
      webworkify-webpack: https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef

  mri@1.2.0: {}

  ms@2.1.2: {}

  nanoid@3.3.11: {}

  njre@0.2.0:
    dependencies:
      command-exists-promise: 2.0.2
      node-fetch: 2.6.7
      tar: 4.4.19
      yauzl: 2.10.0
    transitivePeerDependencies:
      - encoding

  node-fetch@2.6.7:
    dependencies:
      whatwg-url: 5.0.0

  node-html-parser@5.4.1:
    dependencies:
      css-select: 4.3.0
      he: 1.2.0

  node-releases@2.0.6: {}

  normalize-path@3.0.0: {}

  npm-run-path@5.1.0:
    dependencies:
      path-key: 4.0.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  object-inspect@1.12.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  option-validator@2.0.6:
    dependencies:
      kind-of: 6.0.3

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-try@2.2.0: {}

  parse5@6.0.1: {}

  parse5@7.1.2:
    dependencies:
      entities: 4.5.0

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-type@4.0.0: {}

  pend@1.2.0: {}

  picocolors@1.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pidtree@0.6.0: {}

  pngjs@5.0.0: {}

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prettier@3.1.1: {}

  promise-polyfill@7.1.0: {}

  property-information@6.1.1: {}

  proxy-from-env@1.1.0: {}

  qrcode@1.5.4:
    dependencies:
      dijkstrajs: 1.0.3
      pngjs: 5.0.0
      yargs: 15.4.1

  queue-microtask@1.2.3: {}

  rechoir@0.6.2:
    dependencies:
      resolve: 1.22.1

  regenerator-runtime@0.13.9: {}

  regenerator-runtime@0.14.0: {}

  rehype-katex@6.0.3:
    dependencies:
      '@types/hast': 2.3.4
      '@types/katex': 0.14.0
      hast-util-from-html-isomorphic: 1.0.0
      hast-util-to-text: 3.1.2
      katex: 0.16.8
      unist-util-visit: 4.1.0

  rehype-raw@6.1.1:
    dependencies:
      '@types/hast': 2.3.4
      hast-util-raw: 7.2.2
      unified: 10.1.2

  remark-gfm@3.0.1:
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-gfm: 2.0.1
      micromark-extension-gfm: 2.0.1
      unified: 10.1.2
    transitivePeerDependencies:
      - supports-color

  remark-math@5.1.1:
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-math: 2.0.2
      micromark-extension-math: 2.1.2
      unified: 10.1.2

  remark-parse@10.0.1:
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-from-markdown: 1.2.0
      unified: 10.1.2
    transitivePeerDependencies:
      - supports-color

  remark-rehype@10.1.0:
    dependencies:
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      mdast-util-to-hast: 12.2.0
      unified: 10.1.2

  require-directory@2.1.1: {}

  require-main-filename@2.0.0: {}

  resolve@1.22.1:
    dependencies:
      is-core-module: 2.10.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  reusify@1.0.4: {}

  rfdc@1.3.0: {}

  rollup-plugin-copy@3.5.0:
    dependencies:
      '@types/fs-extra': 8.1.4
      colorette: 1.4.0
      fs-extra: 8.1.0
      globby: 10.0.1
      is-plain-object: 3.0.1

  rollup@3.29.5:
    optionalDependencies:
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.5.7:
    dependencies:
      tslib: 2.4.0

  sade@1.8.1:
    dependencies:
      mri: 1.2.0

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  scroll-lock@2.1.5: {}

  seemly@0.3.6: {}

  semver@6.3.0: {}

  set-blocking@2.0.0: {}

  sha256@0.2.0:
    dependencies:
      convert-hex: 0.1.0
      convert-string: 0.1.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shelljs@0.8.5:
    dependencies:
      glob: 7.2.3
      interpret: 1.4.0
      rechoir: 0.6.2

  signal-exit@3.0.7: {}

  slash@3.0.0: {}

  slice-ansi@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  smoothscroll@0.4.0: {}

  solid-contextmenu@0.0.2(solid-js@1.4.8)(solid-transition-group@0.0.12(solid-js@1.4.8)):
    dependencies:
      clsx: 1.2.1
      mitt: 3.0.0
      solid-js: 1.4.8
      solid-transition-group: 0.0.12(solid-js@1.4.8)

  solid-icons@1.0.1(solid-js@1.4.8):
    dependencies:
      solid-js: 1.4.8

  solid-js@1.4.8: {}

  solid-markdown@1.2.0(solid-js@1.4.8):
    dependencies:
      comma-separated-tokens: 2.0.2
      property-information: 6.1.1
      remark-gfm: 3.0.1
      remark-parse: 10.0.1
      remark-rehype: 10.1.0
      solid-js: 1.4.8
      space-separated-tokens: 2.0.1
      style-to-object: 0.3.0
      unified: 10.1.2
      unist-util-visit: 4.1.0
      vfile: 5.3.4
    transitivePeerDependencies:
      - supports-color

  solid-refresh@0.4.1(solid-js@1.4.8):
    dependencies:
      '@babel/generator': 7.18.12
      '@babel/helper-module-imports': 7.18.6
      '@babel/types': 7.18.10
      solid-js: 1.4.8

  solid-transition-group@0.0.12(solid-js@1.4.8):
    dependencies:
      solid-js: 1.4.8

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  sourcemap-codec@1.4.8: {}

  space-separated-tokens@2.0.1: {}

  state-local@1.0.7: {}

  streamsaver@2.0.6: {}

  string-argv@0.3.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.0.1:
    dependencies:
      ansi-regex: 6.0.1

  strip-final-newline@3.0.0: {}

  style-to-object@0.3.0:
    dependencies:
      inline-style-parser: 0.1.1

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  systemjs@6.12.3: {}

  tabbable@5.3.3: {}

  tar@4.4.19:
    dependencies:
      chownr: 1.1.4
      fs-minipass: 1.2.7
      minipass: 2.9.0
      minizlib: 1.3.3
      mkdirp: 0.5.6
      safe-buffer: 5.2.1
      yallist: 3.1.1

  terser@5.14.2:
    dependencies:
      '@jridgewell/source-map': 0.3.2
      acorn: 8.8.0
      commander: 2.20.3
      source-map-support: 0.5.21

  through@2.3.8: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  tr46@0.0.3: {}

  trim-lines@3.0.1: {}

  trough@2.1.0: {}

  ts-toolbelt@9.6.0: {}

  tslib@2.4.0: {}

  type-fest@0.21.3: {}

  typescript-natural-sort@0.7.2: {}

  typescript@4.7.4: {}

  uint8-util@2.2.5:
    dependencies:
      base64-arraybuffer: 1.0.2

  unified@10.1.2:
    dependencies:
      '@types/unist': 2.0.6
      bail: 2.0.2
      extend: 3.0.2
      is-buffer: 2.0.5
      is-plain-obj: 4.1.0
      trough: 2.1.0
      vfile: 5.3.4

  unist-builder@3.0.0:
    dependencies:
      '@types/unist': 2.0.6

  unist-util-find-after@4.0.1:
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1

  unist-util-generated@2.0.0: {}

  unist-util-is@5.1.1: {}

  unist-util-position@4.0.3:
    dependencies:
      '@types/unist': 2.0.6

  unist-util-remove-position@4.0.2:
    dependencies:
      '@types/unist': 2.0.6
      unist-util-visit: 4.1.0

  unist-util-stringify-position@3.0.2:
    dependencies:
      '@types/unist': 2.0.6

  unist-util-visit-parents@5.1.0:
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1

  unist-util-visit@4.1.0:
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
      unist-util-visit-parents: 5.1.0

  universalify@0.1.2: {}

  update-browserslist-db@1.0.5(browserslist@4.21.3):
    dependencies:
      browserslist: 4.21.3
      escalade: 3.1.1
      picocolors: 1.0.0

  uvu@0.5.6:
    dependencies:
      dequal: 2.0.3
      diff: 5.1.0
      kleur: 4.1.5
      sade: 1.8.1

  vfile-location@4.0.1:
    dependencies:
      '@types/unist': 2.0.6
      vfile: 5.3.4

  vfile-message@3.1.2:
    dependencies:
      '@types/unist': 2.0.6
      unist-util-stringify-position: 3.0.2

  vfile@5.3.4:
    dependencies:
      '@types/unist': 2.0.6
      is-buffer: 2.0.5
      unist-util-stringify-position: 3.0.2
      vfile-message: 3.1.2

  vite-plugin-dynamic-base@0.4.4:
    dependencies:
      node-html-parser: 5.4.1

  vite-plugin-solid@2.3.0(solid-js@1.4.8)(vite@4.5.13(@types/node@20.0.0)(terser@5.14.2)):
    dependencies:
      '@babel/core': 7.18.10
      '@babel/preset-typescript': 7.18.6(@babel/core@7.18.10)
      babel-preset-solid: 1.4.8(@babel/core@7.18.10)
      merge-anything: 5.0.2
      solid-js: 1.4.8
      solid-refresh: 0.4.1(solid-js@1.4.8)
      vite: 4.5.13(@types/node@20.0.0)(terser@5.14.2)
    transitivePeerDependencies:
      - supports-color

  vite@4.5.13(@types/node@20.0.0)(terser@5.14.2):
    dependencies:
      esbuild: 0.18.20
      postcss: 8.5.3
      rollup: 3.29.5
    optionalDependencies:
      '@types/node': 20.0.0
      fsevents: 2.3.3
      terser: 5.14.2

  web-namespaces@2.0.1: {}

  webidl-conversions@3.0.1: {}

  webworkify-webpack@https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-module@2.0.1: {}

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  y18n@4.0.3: {}

  yallist@3.1.1: {}

  yaml@2.1.3: {}

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  yauzl@2.10.0:
    dependencies:
      buffer-crc32: 0.2.13
      fd-slicer: 1.1.0

  zwitch@2.0.2: {}
