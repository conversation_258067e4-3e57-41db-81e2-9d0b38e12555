import { VStack, HStack, Box, Skeleton } from "@hope-ui/solid"
import { For } from "solid-js"

export const VirtualStorageSkeleton = () => {
  return (
    <VStack spacing="$2" w="$full" px="$4">
      <For each={Array(3).fill(0)}>
        {() => (
          <HStack
            w="$full"
            p="$3"
            borderRadius="$md"
            border="1px solid"
            borderColor="$neutral6"
            spacing="$3"
            bg="$neutral2"
            _hover={{
              bg: "$neutral3",
              transform: "scale(1.02)",
            }}
            transition="all 0.2s ease"
          >
            {/* 存储图标骨架 */}
            <Skeleton 
              height="40px" 
              width="40px" 
              borderRadius="$md"
              bg="$neutral4"
            />
            
            {/* 存储信息骨架 */}
            <VStack flex="1" spacing="$1" alignItems="flex-start">
              {/* 存储名称 */}
              <Skeleton 
                height="20px" 
                width="120px" 
                borderRadius="$sm"
                bg="$neutral4"
              />
              {/* 存储类型 */}
              <Skeleton 
                height="16px" 
                width="200px" 
                borderRadius="$sm"
                bg="$neutral3"
              />
              {/* 容量信息 */}
              <Skeleton 
                height="14px" 
                width="150px" 
                borderRadius="$sm"
                bg="$neutral3"
              />
            </VStack>
            
            {/* 状态骨架 */}
            <Skeleton 
              height="16px" 
              width="80px" 
              borderRadius="$sm"
              bg="$neutral4"
            />
          </HStack>
        )}
      </For>
    </VStack>
  )
}
