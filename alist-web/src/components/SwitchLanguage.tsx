import {
  Center,
  ElementType,
  Menu,
  MenuContent,
  MenuItem,
  MenuTrigger,
  MenuTriggerProps,
  Spinner,
  useColorModeValue,
  HStack,
  Icon,
} from "@hope-ui/solid"
import { useI18n } from "@solid-primitives/i18n"
import { createSignal, For, Show } from "solid-js"
import {
  langMap,
  languages,
  loadedLangs,
  setLang,
  currentLang,
} from "~/app/i18n"
// import { TbLanguageHiragana } from "solid-icons/tb";
import { IoLanguageOutline } from "solid-icons/io"
import { Portal } from "solid-js/web"

const [fetchingLang, setFetchingLang] = createSignal(false)

export const SwitchLanguage = <C extends ElementType = "button">(
  props: MenuTriggerProps<C>,
) => {
  const [, { locale, add }] = useI18n()
  const switchLang = async (lang: string) => {
    if (!loadedLangs.has(lang)) {
      setFetchingLang(true)
      add(lang, (await langMap[lang]()).default)
      setFetchingLang(false)
      loadedLangs.add(lang)
    }
    locale(lang)
    setLang(lang)
    localStorage.setItem("lang", lang)
  }

  // 获取当前语言的缩写
  const getCurrentLangAbbr = () => {
    const current = languages.find((lang) => lang.code === currentLang())
    if (!current) return "EN"

    // 根据语言代码生成缩写
    switch (current.code) {
      case "zh-CN":
        return "中"
      case "zh-TW":
        return "繁"
      case "en":
        return "EN"
      case "ja":
        return "日"
      case "ko":
        return "한"
      case "fr":
        return "FR"
      case "de":
        return "DE"
      case "es":
        return "ES"
      case "ru":
        return "RU"
      default:
        return current.code.substring(0, 2).toUpperCase()
    }
  }

  return (
    <>
      <Menu>
        <MenuTrigger
          cursor="pointer"
          bg="transparent !important"
          bgColor="transparent !important"
          backgroundColor="transparent !important"
          border="none !important"
          outline="none !important"
          boxShadow="none !important"
          _focus={{
            bg: "transparent !important",
            bgColor: "transparent !important",
            backgroundColor: "transparent !important",
            border: "none !important",
            outline: "none !important",
            boxShadow: "none !important",
          }}
          _active={{
            bg: "transparent !important",
            bgColor: "transparent !important",
            backgroundColor: "transparent !important",
            border: "none !important",
            outline: "none !important",
            boxShadow: "none !important",
          }}
        >
          <HStack
            spacing="$1"
            px="$2"
            py="$1"
            rounded="$md"
            transition="all 0.2s ease"
            bg="transparent"
            bgColor="transparent"
            backgroundColor="transparent"
            border="none"
            _hover={{
              bgColor: "#1e3a8a",
              transform: "scale(1.08)",
              "& > *": {
                color: "#ffffff !important",
              },
            }}
          >
            <Icon
              as={IoLanguageOutline}
              boxSize="$6"
              color="#9ca3af"
              transition="color 0.2s ease"
            />
            <span
              style={{
                color: "#9ca3af",
                "font-size": "14px",
                "font-weight": "500",
                transition: "color 0.2s ease",
              }}
            >
              {getCurrentLangAbbr()}
            </span>
          </HStack>
        </MenuTrigger>
        <MenuContent bgColor="#0f1629">
          <For each={languages}>
            {(lang, i) => (
              <MenuItem
                color="#9ca3af"
                _hover={{
                  bgColor: "#1e3a8a",
                  color: "#ffffff",
                }}
                onSelect={() => {
                  switchLang(lang.code)
                }}
              >
                {lang.lang}
              </MenuItem>
            )}
          </For>
        </MenuContent>
      </Menu>
      <Show when={fetchingLang()}>
        <Portal>
          <Center
            h="$full"
            w="$full"
            pos="fixed"
            top={0}
            bg={useColorModeValue("$blackAlpha4", "$whiteAlpha4")()}
            zIndex="9000"
          >
            <Spinner
              thickness="4px"
              speed="0.65s"
              emptyColor="$neutral4"
              color="$info10"
              size="xl"
            />
          </Center>
        </Portal>
      </Show>
    </>
  )
}

export const SwitchLanguageWhite = () => (
  <SwitchLanguage as={IoLanguageOutline} boxSize="$8" />
)
