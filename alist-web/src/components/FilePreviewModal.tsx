import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  HStack,
  VStack,
  Icon,
  Text,
} from "@hope-ui/solid"
import {
  createSignal,
  createMemo,
  onCleanup,
  Show,
  Suspense,
  createEffect,
  createContext,
  useContext,
} from "solid-js"
import { Dynamic } from "solid-js/web"
import { useT, useRouter, useLink } from "~/hooks"
import { objStore, ObjStore } from "~/store"
import { bus } from "~/utils"
import { FullLoading, SelectWrapper } from "~/components"
import { getPreviews } from "~/pages/home/<USER>"
import { FaSolidChevronLeft, FaSolidChevronRight } from "solid-icons/fa"
import { FilePathContext } from "~/pages/home/<USER>/archive"

// 文件预览组件（直接复用 File.tsx 的逻辑）
const FilePreview = () => {
  const previews = createMemo(() => {
    return getPreviews({ ...objStore.obj, provider: objStore.provider })
  })
  const [cur, setCur] = createSignal(previews()[0])

  return (
    <Show when={previews().length > 0}>
      <VStack w="$full" spacing="$2">
        <Show when={previews().length > 1}>
          <HStack w="$full" spacing="$2">
            <SelectWrapper
              value={cur()?.name || ""}
              onChange={(name) => {
                setCur(previews().find((p) => p.name === name)!)
              }}
              options={previews().map((item) => ({ value: item.name }))}
            />
          </HStack>
        </Show>
        <Suspense fallback={<FullLoading />}>
          <Dynamic component={cur()?.component} />
        </Suspense>
      </VStack>
    </Show>
  )
}

export const FilePreviewModal = () => {
  const t = useT()
  const { pathname } = useRouter()
  const { rawLink } = useLink()
  const [isOpen, setIsOpen] = createSignal(false)
  const [currentFile, setCurrentFile] = createSignal<any>(null)
  const [originalObj, setOriginalObj] = createSignal<any>(null)
  const [originalRawUrl, setOriginalRawUrl] = createSignal<string>("")

  // 监听文件预览事件
  const handleFilePreview = (fileName: string) => {
    const file = objStore.objs.find((obj) => obj.name === fileName)
    if (file) {
      // 保存原始状态
      setOriginalObj(objStore.obj)
      setOriginalRawUrl(objStore.raw_url)

      // 临时更新 objStore 的当前文件状态，这样预览组件可以正确获取文件信息
      ObjStore.setObj(file)
      ObjStore.setRawUrl(rawLink(file))

      setCurrentFile(file)
      setIsOpen(true)
    }
  }

  bus.on("file-preview", handleFilePreview)
  onCleanup(() => {
    bus.off("file-preview", handleFilePreview)
  })

  const onClose = () => {
    // 恢复原始状态
    if (originalObj()) {
      ObjStore.setObj(originalObj())
      ObjStore.setRawUrl(originalRawUrl())
    }

    setIsOpen(false)
    setCurrentFile(null)
    setOriginalObj(null)
    setOriginalRawUrl("")
  }

  // 获取当前目录中的所有文件（用于导航）
  const allFiles = createMemo(() => {
    return objStore.objs.filter((obj) => !obj.is_dir)
  })

  // 当前文件在列表中的索引
  const currentIndex = createMemo(() => {
    const file = currentFile()
    if (!file) return -1
    return allFiles().findIndex((f) => f.name === file.name)
  })

  // 上一个文件
  const previousFile = () => {
    const index = currentIndex()
    if (index > 0) {
      const prevFile = allFiles()[index - 1]
      // 更新 objStore
      ObjStore.setObj(prevFile)
      ObjStore.setRawUrl(rawLink(prevFile))
      setCurrentFile(prevFile)
    }
  }

  // 下一个文件
  const nextFile = () => {
    const index = currentIndex()
    if (index < allFiles().length - 1) {
      const nextFile = allFiles()[index + 1]
      // 更新 objStore
      ObjStore.setObj(nextFile)
      ObjStore.setRawUrl(rawLink(nextFile))
      setCurrentFile(nextFile)
    }
  }

  // 键盘快捷键
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isOpen()) return

    switch (e.key) {
      case "Escape":
        onClose()
        break
      case "ArrowLeft":
        previousFile()
        break
      case "ArrowRight":
        nextFile()
        break
    }
  }

  createEffect(() => {
    if (isOpen()) {
      document.addEventListener("keydown", handleKeyDown)
    } else {
      document.removeEventListener("keydown", handleKeyDown)
    }
  })

  onCleanup(() => {
    document.removeEventListener("keydown", handleKeyDown)
  })

  return (
    <Modal
      opened={isOpen()}
      onClose={onClose}
      size={{
        "@initial": "full",
        "@md": "6xl",
      }}
      scrollBehavior="inside"
      blockScrollOnMount={false}
    >
      <ModalOverlay bg="rgba(0, 0, 0, 0.8)" />
      <ModalContent
        bgColor="#0f1629"
        border="1px solid #374151"
        rounded="$lg"
        maxH="95vh"
        m="$2"
      >
        <ModalHeader
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          color="#ffffff"
          borderBottom="1px solid #374151"
          p="$4"
        >
          <HStack spacing="$2" flex="1" minW="0">
            <Text
              fontSize="$lg"
              fontWeight="$semibold"
              color="#ffffff"
              noOfLines={1}
              title={currentFile()?.name}
            >
              {currentFile()?.name}
            </Text>
            <Show when={allFiles().length > 1}>
              <Text fontSize="$sm" color="#9ca3af">
                ({currentIndex() + 1} / {allFiles().length})
              </Text>
            </Show>
          </HStack>

          <HStack spacing="$2">
            <Show when={allFiles().length > 1}>
              <Button
                size="sm"
                variant="ghost"
                colorScheme="neutral"
                disabled={currentIndex() <= 0}
                onClick={previousFile}
                title={t("home.preview.previous")}
              >
                <Icon as={FaSolidChevronLeft} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                colorScheme="neutral"
                disabled={currentIndex() >= allFiles().length - 1}
                onClick={nextFile}
                title={t("home.preview.next")}
              >
                <Icon as={FaSolidChevronRight} />
              </Button>
            </Show>
          </HStack>

          <ModalCloseButton
            position="static"
            color="#ffffff"
            _hover={{ bgColor: "#1e3a8a" }}
          />
        </ModalHeader>

        <ModalBody p="$4" overflow="auto">
          <Show when={currentFile()}>
            <FilePathContext.Provider
              value={`${pathname()}/${currentFile()?.name}`}
            >
              <FilePreview />
            </FilePathContext.Provider>
          </Show>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}
