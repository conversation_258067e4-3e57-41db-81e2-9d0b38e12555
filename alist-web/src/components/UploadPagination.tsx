import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  V<PERSON><PERSON><PERSON>, 
  Text, 
  <PERSON>con<PERSON><PERSON>on, 
  <PERSON>con, 
  Select,
  Badge,
  Button
} from "@hope-ui/solid";
import { createMemo, For, Show } from "solid-js";
import { BsChevronLeft, BsChevronRight } from "solid-icons/bs";
import { UploadFileProps } from "~/pages/home/<USER>/types";

interface UploadPaginationProps {
  files: UploadFileProps[];
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  statusFilter?: string[];
  onStatusFilterChange?: (statuses: string[]) => void;
}

export const UploadPagination = (props: UploadPaginationProps) => {
  // 根据状态筛选文件
  const filteredFiles = createMemo(() => {
    if (!props.statusFilter || props.statusFilter.length === 0) {
      return props.files;
    }
    return props.files.filter(file => 
      props.statusFilter!.includes(file.status)
    );
  });

  const totalPages = () => Math.ceil(filteredFiles().length / props.pageSize);
  const startIndex = () => props.currentPage * props.pageSize;
  const endIndex = () => Math.min(startIndex() + props.pageSize, filteredFiles().length);
  
  // 当前页显示的文件
  const visibleFiles = () => {
    const start = startIndex();
    const end = endIndex();
    return filteredFiles().slice(start, end);
  };

  // 状态统计
  const statusCounts = createMemo(() => {
    const counts: Record<string, number> = {};
    props.files.forEach(file => {
      counts[file.status] = (counts[file.status] || 0) + 1;
    });
    return counts;
  });

  // 状态筛选组件
  const StatusFilter = () => {
    const allStatuses = ["pending", "hashing", "uploading", "backending", "success", "error"];
    const statusLabels: Record<string, string> = {
      pending: "等待中",
      hashing: "计算哈希",
      uploading: "上传中", 
      backending: "处理中",
      success: "成功",
      error: "失败"
    };

    const toggleStatus = (status: string) => {
      if (!props.onStatusFilterChange) return;
      
      const current = props.statusFilter || [];
      const newFilter = current.includes(status)
        ? current.filter(s => s !== status)
        : [...current, status];
      
      props.onStatusFilterChange(newFilter);
    };

    return (
      <HStack spacing="$2" flexWrap="wrap">
        <Text fontSize="$sm" color="$neutral11">状态筛选：</Text>
        <For each={allStatuses}>
          {(status) => (
            <Show when={statusCounts()[status] > 0}>
              <Button
                size="xs"
                variant={props.statusFilter?.includes(status) ? "solid" : "outline"}
                colorScheme={
                  status === "success" ? "success" :
                  status === "error" ? "danger" :
                  status === "uploading" ? "info" : "neutral"
                }
                onClick={() => toggleStatus(status)}
              >
                {statusLabels[status]} ({statusCounts()[status]})
              </Button>
            </Show>
          )}
        </For>
        <Show when={props.statusFilter && props.statusFilter.length > 0}>
          <Button
            size="xs"
            variant="ghost"
            onClick={() => props.onStatusFilterChange?.([])}
          >
            清除筛选
          </Button>
        </Show>
      </HStack>
    );
  };

  return (
    <VStack spacing="$3" w="$full">
      {/* 状态筛选 */}
      <Show when={props.onStatusFilterChange}>
        <StatusFilter />
      </Show>

      {/* 分页信息和控制 */}
      <HStack spacing="$2" justifyContent="space-between" alignItems="center" w="$full">
        <HStack spacing="$2">
          <Text fontSize="$sm">
            显示 {startIndex() + 1}-{endIndex()} 项，共 {filteredFiles().length} 项
          </Text>
          <Show when={props.statusFilter && props.statusFilter.length > 0}>
            <Badge colorScheme="info" size="sm">
              已筛选
            </Badge>
          </Show>
        </HStack>
        
        <HStack spacing="$2">
          <Text fontSize="$sm">每页：</Text>
          <Select
            value={props.pageSize.toString()}
            onChange={(value) => props.onPageSizeChange(parseInt(value))}
            size="sm"
            w="80px"
          >
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
          </Select>
        </HStack>
      </HStack>

      {/* 分页导航 */}
      <Show when={totalPages() > 1}>
        <HStack spacing="$2" justifyContent="center" alignItems="center">
          <IconButton
            aria-label="Previous page"
            icon={<Icon as={BsChevronLeft} />}
            size="sm"
            variant="outline"
            disabled={props.currentPage === 0}
            onClick={() => props.onPageChange(props.currentPage - 1)}
          />
          
          <Text fontSize="$sm">
            第 {props.currentPage + 1} 页，共 {totalPages()} 页
          </Text>
          
          <IconButton
            aria-label="Next page"
            icon={<Icon as={BsChevronRight} />}
            size="sm"
            variant="outline"
            disabled={props.currentPage >= totalPages() - 1}
            onClick={() => props.onPageChange(props.currentPage + 1)}
          />
        </HStack>
      </Show>
    </VStack>
  );
};

// 导出可见文件的计算函数，供父组件使用
export const getVisibleFiles = (
  files: UploadFileProps[],
  currentPage: number,
  pageSize: number,
  statusFilter?: string[]
): UploadFileProps[] => {
  const filteredFiles = statusFilter && statusFilter.length > 0
    ? files.filter(file => statusFilter.includes(file.status))
    : files;
    
  const start = currentPage * pageSize;
  const end = start + pageSize;
  return filteredFiles.slice(start, end);
};
