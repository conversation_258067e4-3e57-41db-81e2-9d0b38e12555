import { Box, Text, VStack, HStack, Badge } from "@hope-ui/solid"
import { createSignal, createEffect, onMount, onCleanup, Show } from "solid-js"
// import { usePreloader } from "~/hooks/usePreloader"

interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  cacheHitRate: number
  loadTime: number
  fps: number
}

export const PerformanceMonitor = () => {
  const [metrics, setMetrics] = createSignal<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    loadTime: 0,
    fps: 0,
  })

  const [isVisible, setIsVisible] = createSignal(false)
  // const { getPreloadStats } = usePreloader()
  
  let frameCount = 0
  let lastTime = performance.now()
  let animationId: number
  
  // FPS 计算
  const calculateFPS = () => {
    frameCount++
    const currentTime = performance.now()
    
    if (currentTime - lastTime >= 1000) {
      const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
      setMetrics(prev => ({ ...prev, fps }))
      frameCount = 0
      lastTime = currentTime
    }
    
    animationId = requestAnimationFrame(calculateFPS)
  }
  
  // 内存使用情况
  const updateMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)
      setMetrics(prev => ({ ...prev, memoryUsage: usedMB }))
    }
  }
  
  // 渲染时间测量
  const measureRenderTime = () => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const renderEntries = entries.filter(entry => 
        entry.name.includes('render') || entry.entryType === 'measure'
      )
      
      if (renderEntries.length > 0) {
        const avgRenderTime = renderEntries.reduce((sum, entry) => 
          sum + entry.duration, 0) / renderEntries.length
        setMetrics(prev => ({ ...prev, renderTime: Math.round(avgRenderTime) }))
      }
    })
    
    observer.observe({ entryTypes: ['measure', 'navigation'] })
    return observer
  }
  
  // 页面加载时间
  const updateLoadTime = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      const loadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart)
      setMetrics(prev => ({ ...prev, loadTime }))
    }
  }
  
  // 缓存命中率（模拟）
  const updateCacheHitRate = () => {
    // 这里可以集成实际的缓存统计
    const hitRate = Math.random() * 100 // 模拟数据
    setMetrics(prev => ({ ...prev, cacheHitRate: Math.round(hitRate) }))
  }
  
  onMount(() => {
    // 开始性能监控
    calculateFPS()
    const renderObserver = measureRenderTime()
    
    // 定期更新指标
    const interval = setInterval(() => {
      updateMemoryUsage()
      updateLoadTime()
      updateCacheHitRate()
    }, 2000)
    
    // 键盘快捷键切换显示
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(!isVisible())
      }
    }
    
    document.addEventListener('keydown', handleKeyPress)
    
    onCleanup(() => {
      cancelAnimationFrame(animationId)
      clearInterval(interval)
      renderObserver.disconnect()
      document.removeEventListener('keydown', handleKeyPress)
    })
  })
  
  const getStatusColor = (value: number, thresholds: [number, number]) => {
    if (value < thresholds[0]) return "success"
    if (value < thresholds[1]) return "warning"
    return "danger"
  }
  
  return (
    <Show when={isVisible()}>
      <Box
        position="fixed"
        top="$4"
        right="$4"
        zIndex="$overlay"
        bg="$neutral1"
        border="1px solid $neutral6"
        borderRadius="$md"
        p="$3"
        shadow="$lg"
        minW="250px"
        fontSize="$xs"
      >
        <VStack spacing="$2">
          <Text fontWeight="$semibold" color="$neutral12">
            性能监控 (Ctrl+Shift+P)
          </Text>
          
          <VStack spacing="$1">
            <HStack justifyContent="space-between">
              <Text>FPS:</Text>
              <Badge colorScheme={getStatusColor(metrics().fps, [30, 50])}>
                {metrics().fps}
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>渲染时间:</Text>
              <Badge colorScheme={getStatusColor(metrics().renderTime, [16, 33])}>
                {metrics().renderTime}ms
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>内存使用:</Text>
              <Badge colorScheme={getStatusColor(metrics().memoryUsage, [50, 100])}>
                {metrics().memoryUsage}MB
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>加载时间:</Text>
              <Badge colorScheme={getStatusColor(metrics().loadTime, [1000, 3000])}>
                {metrics().loadTime}ms
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>缓存命中率:</Text>
              <Badge colorScheme={getStatusColor(100 - metrics().cacheHitRate, [20, 50])}>
                {metrics().cacheHitRate}%
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>文件数量:</Text>
              <Badge colorScheme="info">
                {/* 这里可以显示当前文件列表的数量 */}
                --
              </Badge>
            </HStack>
          </VStack>
        </VStack>
      </Box>
    </Show>
  )
}
