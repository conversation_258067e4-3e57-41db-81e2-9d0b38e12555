import {
  Box,
  VStack,
  HStack,
  Text,
  Progress,
  ProgressIndicator,
  Tooltip,
  useColorModeValue,
} from "@hope-ui/solid"
import { createMemo, For, Show, createSignal, onMount, createResource, Suspense, onCleanup } from "solid-js"
import { BsHddNetwork } from "solid-icons/bs"
import { Icon } from "@hope-ui/solid"
import { me } from "~/store"
import { UserMethods } from "~/types"
import { useT, useRouter } from "~/hooks"
import { r, bus } from "~/utils"
import { initializeUserCapacity, getCapacityInitializationProgress, checkCapacityDataExists } from "~/utils/api"

// 移除了刷新按钮相关的CSS动画样式

interface UserCapacityInfoProps {
  compact?: boolean // 紧凑模式
}

// 格式化容量大小
function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 B"
  const k = 1024
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

// 计算容量使用百分比
const getCapacityPercentage = (usedBytes: number, totalBytes: number): number => {
  if (!totalBytes || totalBytes === 0) {
    return 0
  }
  return Math.round((usedBytes / totalBytes) * 100)
}

// 根据使用率获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 95) return "#ef4444" // 红色 - 超过95%一律红色
  if (percentage > 90) return "#f59e0b" // 橙色 - 警告
  if (percentage > 70) return "#eab308" // 黄色 - 注意
  return "#10b981" // 绿色 - 正常
}

// 根据使用率获取文字颜色
const getTextColor = (percentage: number) => {
  if (percentage >= 95) return "#ef4444" // 红色文字 - 超过95%
  if (percentage > 90) return "#f59e0b" // 橙色文字 - 警告
  if (percentage > 70) return "#eab308" // 黄色文字 - 注意
  return "#6b7280" // 灰色文字 - 正常
}

// 根据使用率获取进度条内文字颜色（考虑背景对比度）
const getProgressTextColor = (percentage: number, isDark: boolean) => {
  if (percentage >= 95) {
    // 超过95%时，进度条为红色背景，使用白色文字确保对比度
    return "white"
  } else if (percentage >= 90) {
    // 90-95%时，进度条为橙色背景，使用白色文字
    return "white"
  } else if (percentage > 50) {
    // 中等使用率时，根据主题选择颜色
    return isDark ? "white" : "#1f2937"
  } else {
    // 低使用率时，进度条背景较浅，使用深色文字
    return isDark ? "#1f2937" : "#1f2937"
  }
}

export const UserCapacityInfo = (props: UserCapacityInfoProps) => {
  const t = useT()
  const { to } = useRouter()

  // 全局刷新状态（保留兼容性）
  const [refreshing, setRefreshing] = createSignal(false)

  // 移除单卡片刷新状态管理，因为已删除刷新按钮

  // 使用信号来管理容量数据，确保能够实时更新
  const [capacityData, setCapacityData] = createSignal([])

  // 单个卡片的容量数据缓存，用于局部更新
  const [cardDataCache, setCardDataCache] = createSignal<Record<string, any>>({})

  // 容量初始化相关状态
  const [isInitializing, setIsInitializing] = createSignal(false)
  const [initializationStarted, setInitializationStarted] = createSignal(false)
  let initProgressInterval: number | undefined

  // 检查是否需要容量初始化（异步检查数据库）
  const checkCapacityInitializationNeeded = async (): Promise<boolean> => {
    const user = me()
    if (!user || !user.base_paths) return false

    // 检查是否有启用容量限制的基础路径
    const hasCapacityPaths = user.base_paths.some(bp =>
      bp.enable_capacity_limit && bp.capacity_limit_gb > 0
    )

    if (!hasCapacityPaths) return false

    try {
      console.log("🔍 检查数据库中的容量数据...")
      const response = await checkCapacityDataExists()

      if (response.code === 200 && response.data) {
        const needInit = response.data.paths_need_initialization.length > 0
        console.log("📊 容量数据检查结果:", {
          needInitialization: needInit,
          pathsNeedInit: response.data.paths_need_initialization,
          pathsWithData: response.data.paths_with_existing_data,
          totalPaths: response.data.total_capacity_paths
        })
        return needInit
      }
    } catch (error) {
      console.error("❌ 检查容量数据失败:", error)
      // 如果检查失败，回退到原有逻辑
      return user.base_paths.some(bp =>
        bp.enable_capacity_limit &&
        bp.capacity_limit_gb > 0 &&
        (bp.used_bytes === 0 || bp.total_bytes === 0)
      )
    }

    return false
  }

  // 启动容量初始化
  const startCapacityInitialization = async () => {
    if (isInitializing() || initializationStarted()) return

    try {
      console.log("🚀 启动容量初始化...")
      setIsInitializing(true)
      setInitializationStarted(true)

      const response = await initializeUserCapacity()

      if (response.code === 200) {
        console.log("✅ 容量初始化启动成功")

        // 开始轮询进度
        initProgressInterval = setInterval(async () => {
          try {
            const progressResponse = await getCapacityInitializationProgress()

            if (progressResponse.code === 200 && progressResponse.data.all_completed) {
              console.log("🎉 容量初始化完成!")
              setIsInitializing(false)

              // 停止轮询
              if (initProgressInterval) {
                clearInterval(initProgressInterval)
                initProgressInterval = undefined
              }

              // 刷新容量数据 - 从服务器重新获取用户数据
              console.log("🔄 容量初始化完成，开始刷新用户数据...")
              setTimeout(async () => {
                try {
                  await refreshAllCapacity()
                  console.log("✅ 容量初始化完成后数据刷新成功")
                } catch (error) {
                  console.error("❌ 容量初始化完成后数据刷新失败:", error)
                  // 如果刷新失败，至少尝试重新获取本地数据
                  await fetchCapacityData()
                }
              }, 1000)
            }
          } catch (err) {
            console.error("获取初始化进度失败:", err)
          }
        }, 3000) // 每3秒检查一次

      } else {
        throw new Error(response.message || "启动容量初始化失败")
      }
    } catch (err: any) {
      console.error("❌ 容量初始化启动失败:", err)
      setIsInitializing(false)
      setInitializationStarted(false)
    }
  }

  // 获取容量数据的函数
  const fetchCapacityData = async () => {
    const user = me()
    if (!user || !user.id) {
      setCapacityData([])
      return
    }

    console.log("fetchCapacityData: Current user data:", user)
    const basePaths = UserMethods.get_base_paths(user).filter(bp => bp.enable_capacity_limit)
    console.log("fetchCapacityData: Filtered base paths:", basePaths)
    setCapacityData(basePaths)

    // 异步检查是否需要容量初始化
    if (!initializationStarted()) {
      try {
        const needsInit = await checkCapacityInitializationNeeded()
        if (needsInit) {
          console.log("🔍 检测到需要容量初始化，自动启动...")
          setTimeout(() => {
            startCapacityInitialization()
          }, 1000) // 延迟1秒启动，确保页面加载完成
        } else {
          console.log("✅ 所有基础路径都已有容量数据，无需初始化")
        }
      } catch (error) {
        console.error("❌ 检查容量初始化需求失败:", error)
      }
    }
  }

  // 初始化时获取数据
  onMount(() => {
    setTimeout(() => {
      fetchCapacityData()
    }, 200)
  })

  // 如果没有启用容量限制的基础路径，不显示组件
  const shouldShow = createMemo(() => {
    const data = capacityData()
    return data && data.length > 0
  })

  // 初始化和监听文件操作事件
  onMount(async () => {
    // 初始化时获取容量数据
    await fetchCapacityData()

    // 登录后自动刷新一次容量数据
    console.log("UserCapacityInfo: 组件挂载完成，开始自动刷新容量数据")
    try {
      await refreshAllCapacity()
      console.log("UserCapacityInfo: 自动刷新容量数据完成")
    } catch (error) {
      console.error("UserCapacityInfo: 自动刷新容量数据失败:", error)
    }

    const handleFileOperation = (eventType: string) => {
      console.log(`🔄 文件操作事件触发: ${eventType}`)
      // 延迟刷新，确保文件操作完成
      setTimeout(async () => {
        console.log(`🔄 开始刷新容量数据 (事件: ${eventType})`)
        await refreshAllCapacity()
        console.log(`✅ 容量数据刷新完成 (事件: ${eventType})`)
      }, 1000)
    }

    // 监听各种文件操作事件
    bus.on("file_uploaded", () => handleFileOperation("file_uploaded"))
    bus.on("file_deleted", () => handleFileOperation("file_deleted"))
    bus.on("file_moved", () => handleFileOperation("file_moved"))
    bus.on("file_copied", () => handleFileOperation("file_copied"))
    bus.on("file_renamed", () => handleFileOperation("file_renamed"))

    onCleanup(() => {
      bus.off("file_uploaded", handleFileOperation)
      bus.off("file_deleted", handleFileOperation)
      bus.off("file_moved", handleFileOperation)
      bus.off("file_copied", handleFileOperation)
      bus.off("file_renamed", handleFileOperation)

      // 清理容量初始化轮询
      if (initProgressInterval) {
        clearInterval(initProgressInterval)
        initProgressInterval = undefined
      }
    })
  })

  // 已删除单个卡片刷新功能

  // 刷新所有容量信息 - 增强版本


  // 刷新所有容量信息 - 增强版本
  const refreshAllCapacity = async () => {
    if (refreshing()) return

    console.log("refreshAllCapacity: Starting refresh for all capacity")

    // 检查当前用户状态和认证token
    const currentUser = me()
    const token = localStorage.getItem("token")
    console.log("refreshAllCapacity: Current user:", currentUser)
    console.log("refreshAllCapacity: Token exists:", !!token)

    if (!currentUser || !currentUser.id || !token) {
      console.error("refreshAllCapacity: 用户未登录或认证失效")
      return
    }

    setRefreshing(true)
    try {
      // 使用原生fetch API刷新所有容量信息
      const apiUrl = `/api/me?refresh_capacity=true`
      console.log("refreshAllCapacity: 请求URL:", apiUrl)

      const fetchResponse = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': localStorage.getItem("token") || "",
          'Content-Type': 'application/json'
        }
      })

      console.log("refreshAllCapacity: fetch响应状态:", fetchResponse.status)

      const responseText = await fetchResponse.text()
      console.log("refreshAllCapacity: fetch响应文本长度:", responseText.length)

      let response
      try {
        response = JSON.parse(responseText)
        console.log("refreshAllCapacity: 成功解析JSON响应:", response)
      } catch (parseError) {
        console.error("refreshAllCapacity: JSON解析失败，响应可能是HTML:", parseError)
        throw new Error(`fetch请求返回非JSON响应: ${responseText.substring(0, 200)}...`)
      }

      console.log("refreshAllCapacity: Response keys:", response ? Object.keys(response) : 'null')
      console.log("refreshAllCapacity: Response code:", response?.code)
      console.log("refreshAllCapacity: Response message:", response?.message)
      console.log("refreshAllCapacity: Response data:", response?.data)

      // 检查响应格式和数据
      if (!response) {
        throw new Error("API响应为空")
      }

      // 提取用户数据
      let userData
      if (typeof response === 'object' && 'code' in response && 'message' in response) {
        // 标准的AList API响应格式
        if (response.code !== 200) {
          throw new Error(`API响应错误: ${response.message || '未知错误'} (code: ${response.code})`)
        }

        if (!response.data) {
          throw new Error(`API响应数据为空: ${JSON.stringify(response)}`)
        }
        userData = response.data
        console.log("refreshAllCapacity: 使用标准API响应格式的数据:", userData)
      } else if (response.id || response.username) {
        // 直接用户数据格式
        userData = response
        console.log("refreshAllCapacity: 使用直接用户数据格式:", userData)
      } else {
        throw new Error("无法从响应中提取用户数据")
      }

      // 更新用户信息到store
      const { setMe } = await import("~/store")
      setMe(userData)
      console.log("refreshAllCapacity: Updated user data in store")

      // 重新获取容量数据
      await fetchCapacityData()
      console.log("refreshAllCapacity: Fetched new capacity data")
    } catch (error) {
      console.error("刷新容量信息失败:", error)
      console.error("错误详情:", {
        name: error?.name,
        message: error?.message,
        stack: error?.stack
      })
    } finally {
      setRefreshing(false)
    }
  }



  // 处理点击跳转到对应存储空间
  const handleCapacityClick = (basePath: any) => {
    // 如果是多基础路径用户，跳转到虚拟存储空间
    if (UserMethods.has_multiple_base_paths(me())) {
      const allBasePaths = UserMethods.get_base_paths(me())
      const index = allBasePaths.findIndex(bp => bp.id === basePath.id)
      // 虚拟存储空间索引从1开始，所以需要+1
      to(`/virtual/storage/${index + 1}`)
    } else {
      to("/")
    }
  }

  return (
    <Show when={shouldShow()}>
      <Box
        w="$full"
        p="$3"
        borderTop="1px solid"
        borderColor={useColorModeValue("$neutral4", "#374151")()}
        bg={useColorModeValue("$neutral1", "#1e2a4a")()}
      >
        <VStack spacing="$2" alignItems="start" w="$full">
          {/* 标题 */}
          <HStack spacing="$2" alignItems="center">
            <Icon as={BsHddNetwork} boxSize="$4" color="$primary10" />
            <Text fontSize="$sm" fontWeight="$medium" color={useColorModeValue("$neutral11", "#ffffff")()}>
              {t("users.storage_quota.heading")}
            </Text>
          </HStack>

          {/* 容量信息列表 */}
          <Suspense fallback={
            <Box w="$full" p="$2" textAlign="center">
              <Text fontSize="$xs" color={useColorModeValue("$neutral9", "#9ca3af")()}>
                加载中...
              </Text>
            </Box>
          }>
            <VStack spacing="$2" w="$full">
              <For each={capacityData()}>
                {(basePath) => {
                  // 使用 createMemo 缓存计算结果，避免重复计算
                  const percentage = createMemo(() => getCapacityPercentage(basePath.used_bytes, basePath.total_bytes))
                  const progressColor = createMemo(() => getProgressColor(percentage()))
                  const textColor = createMemo(() => getTextColor(percentage()))
                  const progressTextColor = createMemo(() => getProgressTextColor(percentage(), false)) // 假设为亮色主题
                  const tooltipText = createMemo(() => `${basePath.alias}: ${formatBytes(basePath.used_bytes)} / ${formatBytes(basePath.total_bytes)} (${percentage()}%)`)

                  return (
                  <Tooltip
                    label={tooltipText()}
                    placement="top"
                  >
                    <Box
                      w="$full"
                      p="$2"
                      borderRadius="$md"
                      bg={useColorModeValue("$neutral2", "#0f1629")()}
                      border="1px solid"
                      borderColor={useColorModeValue("$neutral4", "#374151")()}
                      _hover={{
                        borderColor: "$primary8",
                        transform: "translateY(-1px)",
                        transition: "all 0.2s ease",
                      }}
                    >
                      {/* 可点击区域 - 跳转到存储空间 */}
                      <Box
                        w="$full"
                        h="$full"
                        cursor="pointer"
                        onClick={() => handleCapacityClick(basePath)}
                      >
                        <VStack spacing="$1" w="$full">
                          {/* 存储空间名称 */}
                          <HStack justifyContent="flex-start" w="$full" alignItems="center">
                            <Text
                              fontSize="$xs"
                              fontWeight="$medium"
                              color={useColorModeValue("$neutral11", "#ffffff")()}
                              noOfLines={1}
                              flex="1"
                            >
                              {basePath.alias}
                            </Text>
                          </HStack>

                        {/* 进度条 - 包含容量信息 */}
                        <Box position="relative" w="$full">
                          <Progress
                            value={isInitializing() && (basePath.used_bytes === 0 || basePath.total_bytes === 0) ? 100 : Math.min(percentage(), 100)} // 初始化时显示满进度条
                            max={100}
                            size="md" // 增大进度条以容纳文字
                            w="$full"
                            trackColor={useColorModeValue("$neutral4", "#374151")()}
                            borderRadius="$sm"
                            style={{
                              transition: "all 0.5s ease"
                            }}
                          >
                            <ProgressIndicator
                              bg={isInitializing() && (basePath.used_bytes === 0 || basePath.total_bytes === 0) ? "#3b82f6" : progressColor()} // 初始化时使用蓝色
                              borderRadius="$sm"
                              style={{
                                transition: "all 0.5s ease",
                                animation: isInitializing() && (basePath.used_bytes === 0 || basePath.total_bytes === 0) ? "pulse 2s infinite" : "none" // 初始化时添加脉冲动画
                              }}
                            />
                          </Progress>

                          {/* 在进度条内显示容量信息 */}
                          <Box
                            position="absolute"
                            top="50%"
                            left="50%"
                            transform="translate(-50%, -50%)"
                            zIndex="1"
                            w="$full"
                            px="$2"
                          >
                            {/* 根据状态显示不同内容 */}
                            <Show
                              when={isInitializing() && (basePath.used_bytes === 0 || basePath.total_bytes === 0)}
                              fallback={
                                <HStack justifyContent="space-between" w="$full">
                                  {/* 左侧：容量详情 */}
                                  <Text
                                    fontSize="9px"
                                    fontWeight="medium"
                                    color={progressTextColor()}
                                    textShadow={percentage() >= 90 ? "0 1px 2px rgba(0,0,0,0.5)" : "none"}
                                    noOfLines={1}
                                  >
                                    {formatBytes(basePath.used_bytes)} / {formatBytes(basePath.total_bytes)}
                                  </Text>

                                  {/* 右侧：使用率百分比 */}
                                  <Text
                                    fontSize="9px"
                                    fontWeight="medium"
                                    color={progressTextColor()}
                                    textShadow={percentage() >= 90 ? "0 1px 2px rgba(0,0,0,0.5)" : "none"}
                                  >
                                    {percentage()}%
                                  </Text>
                                </HStack>
                              }
                            >
                              {/* 容量初始化状态 - 居中显示 */}
                              <Box w="$full" textAlign="center">
                                <Text
                                  fontSize="10px"
                                  fontWeight="bold"
                                  color="#ffffff"
                                  textShadow="0 1px 3px rgba(0,0,0,0.8)"
                                  bg="rgba(59, 130, 246, 0.8)"
                                  px="$2"
                                  py="$1"
                                  borderRadius="$sm"
                                  style={{
                                    transition: "all 0.3s ease"
                                  }}
                                >
                                  正在计算容量...
                                </Text>
                              </Box>
                            </Show>
                          </Box>
                        </Box>
                        </VStack>
                      </Box>
                    </Box>
                  </Tooltip>
                  )
                }}
              </For>
            </VStack>
          </Suspense>
        </VStack>
      </Box>
    </Show>
  )
}
