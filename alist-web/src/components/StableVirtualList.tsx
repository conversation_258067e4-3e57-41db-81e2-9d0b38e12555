import { VStack, Box } from "@hope-ui/solid"
import { createSignal, createMemo, onMount, onCleanup, For, JSX } from "solid-js"

interface StableVirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => JSX.Element
  overscan?: number
  onScroll?: (scrollTop: number) => void
}

export function StableVirtualList<T>(props: StableVirtualListProps<T>) {
  const [scrollTop, setScrollTop] = createSignal(0)
  let containerElement: HTMLDivElement | undefined
  
  const overscan = () => props.overscan ?? 3
  
  // 计算可见范围 - 使用更保守的计算方式
  const visibleRange = createMemo(() => {
    const containerHeight = props.containerHeight
    const itemHeight = props.itemHeight
    const currentScrollTop = scrollTop()
    
    if (itemHeight <= 0 || props.items.length === 0) {
      return { startIndex: 0, endIndex: 0 }
    }
    
    const startIndex = Math.max(0, Math.floor(currentScrollTop / itemHeight) - overscan())
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const endIndex = Math.min(
      props.items.length - 1,
      startIndex + visibleCount + overscan() * 2
    )
    
    return { startIndex, endIndex }
  })
  
  // 可见项目列表
  const visibleItems = createMemo(() => {
    const { startIndex, endIndex } = visibleRange()
    const items = []
    
    for (let i = startIndex; i <= endIndex; i++) {
      if (i < props.items.length) {
        items.push({
          item: props.items[i],
          index: i
        })
      }
    }
    
    return items
  })
  
  // 总高度
  const totalHeight = createMemo(() => props.items.length * props.itemHeight)
  
  // 偏移量
  const offsetY = createMemo(() => visibleRange().startIndex * props.itemHeight)
  
  // 滚动处理 - 添加节流
  let scrollTimer: number | undefined
  const handleScroll = (e: Event) => {
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    scrollTimer = setTimeout(() => {
      const target = e.target as HTMLDivElement
      const newScrollTop = target.scrollTop
      setScrollTop(newScrollTop)
      props.onScroll?.(newScrollTop)
    }, 16) // 约60fps的更新频率
  }
  
  onMount(() => {
    if (containerElement) {
      containerElement.addEventListener('scroll', handleScroll, { passive: true })
    }
  })
  
  onCleanup(() => {
    if (containerElement) {
      containerElement.removeEventListener('scroll', handleScroll)
    }
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
  })
  
  return (
    <Box
      ref={containerElement}
      height={`${props.containerHeight}px`}
      overflowY="auto"
      overflowX="hidden"
      css={{
        scrollbarWidth: "thin",
        "&::-webkit-scrollbar": {
          width: "6px",
        },
        "&::-webkit-scrollbar-track": {
          background: "transparent",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "$neutral6",
          borderRadius: "3px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "$neutral7",
        },
      }}
    >
      {/* 总容器，用于撑开滚动条 */}
      <Box 
        height={`${totalHeight()}px`} 
        position="relative"
        width="100%"
      >
        {/* 可见项目容器 */}
        <Box
          position="absolute"
          top={`${offsetY()}px`}
          left="0"
          right="0"
          width="100%"
        >
          <VStack spacing="$2" width="100%">
            <For each={visibleItems()}>
              {({ item, index }) => (
                <Box width="100%">
                  {props.renderItem(item, index)}
                </Box>
              )}
            </For>
          </VStack>
        </Box>
      </Box>
    </Box>
  )
}
