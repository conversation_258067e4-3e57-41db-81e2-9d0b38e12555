import { Box, Text, VStack, HStack, Badge } from "@hope-ui/solid"
import { createSignal, Show } from "solid-js"
import { useVirtualScrollOptimization } from "~/hooks/useVirtualScrollOptimization"

export const VirtualScrollDebugger = () => {
  const [isVisible, setIsVisible] = createSignal(false)
  const { metrics, getOptimizationSuggestions, getOptimalConfig } = useVirtualScrollOptimization()
  
  // 键盘快捷键切换显示
  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'V') {
      setIsVisible(!isVisible())
    }
  }
  
  document.addEventListener('keydown', handleKeyPress)
  
  const getStatusColor = (value: number, thresholds: [number, number]) => {
    if (value < thresholds[0]) return "success"
    if (value < thresholds[1]) return "warning"
    return "danger"
  }
  
  return (
    <Show when={isVisible()}>
      <Box
        position="fixed"
        top="$20"
        right="$4"
        zIndex="$overlay"
        bg="$neutral1"
        border="1px solid $neutral6"
        borderRadius="$md"
        p="$3"
        shadow="$lg"
        minW="280px"
        fontSize="$xs"
      >
        <VStack spacing="$2">
          <Text fontWeight="$semibold" color="$neutral12">
            虚拟滚动调试 (Ctrl+Shift+V)
          </Text>
          
          <VStack spacing="$1">
            <HStack justifyContent="space-between">
              <Text>总项目数:</Text>
              <Badge colorScheme="info">
                {metrics().totalItems}
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>可见项目数:</Text>
              <Badge colorScheme={getStatusColor(metrics().visibleItems, [20, 50])}>
                {metrics().visibleItems}
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>渲染时间:</Text>
              <Badge colorScheme={getStatusColor(metrics().renderTime, [16, 33])}>
                {metrics().renderTime.toFixed(1)}ms
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>滚动位置:</Text>
              <Badge colorScheme="neutral">
                {Math.round(metrics().scrollPosition)}px
              </Badge>
            </HStack>
            
            <HStack justifyContent="space-between">
              <Text>滚动状态:</Text>
              <Badge colorScheme={metrics().isScrolling ? "warning" : "success"}>
                {metrics().isScrolling ? "滚动中" : "静止"}
              </Badge>
            </HStack>
          </VStack>
          
          {/* 优化建议 */}
          <Show when={getOptimizationSuggestions().length > 0}>
            <VStack spacing="$1" mt="$2">
              <Text fontWeight="$semibold" color="$warning10" fontSize="$xs">
                优化建议:
              </Text>
              {getOptimizationSuggestions().map(suggestion => (
                <Text fontSize="$xs" color="$neutral10">
                  • {suggestion}
                </Text>
              ))}
            </VStack>
          </Show>
          
          {/* 最优配置 */}
          <VStack spacing="$1" mt="$2">
            <Text fontWeight="$semibold" color="$info10" fontSize="$xs">
              推荐配置:
            </Text>
            <HStack justifyContent="space-between">
              <Text fontSize="$xs">项目高度:</Text>
              <Text fontSize="$xs" color="$info10">
                {getOptimalConfig().itemHeight}px
              </Text>
            </HStack>
            <HStack justifyContent="space-between">
              <Text fontSize="$xs">预渲染数:</Text>
              <Text fontSize="$xs" color="$info10">
                {getOptimalConfig().overscan}
              </Text>
            </HStack>
          </VStack>
        </VStack>
      </Box>
    </Show>
  )
}
