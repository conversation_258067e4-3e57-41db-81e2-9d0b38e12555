import {
  Box,
  Center,
  createDisclosure,
  Drawer,
  DrawerBody,
  DrawerCloseButton,
  DrawerContent,
  DrawerHeader,
  DrawerOverlay,
  Flex,
  Heading,
  HStack,
  IconButton,
  useColorModeValue,
  Image,
  Icon,
  Kbd,
  CenterProps,
  Menu,
  MenuContent,
  MenuItem,
  MenuTrigger,
  Avatar,
  Text,
  VStack,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Button,
  SimpleGrid,
  FormHelperText,
} from "@hope-ui/solid"
import { changeColor } from "seemly"
import { Show, createMemo, createSignal } from "solid-js"
import { TiThMenu } from "solid-icons/ti"
import { IoExit, IoPersonOutline, IoLanguageOutline } from "solid-icons/io"
import { BsSearch } from "solid-icons/bs"
import { FaRegularUser } from "solid-icons/fa"
import { AiOutlineDown } from "solid-icons/ai"
import { SwitchColorMode, SwitchLanguage, CenterLoading } from "~/components"
import { useFetch, useRouter, useT } from "~/hooks"
import {
  getMainColor,
  getSetting,
  local,
  objStore,
  State,
  me,
  setMe,
} from "~/store"
import { changeToken, handleResp, notify, r, bus } from "~/utils"
import { PResp, UserMethods, PEmptyResp } from "~/types"
import { Container } from "~/pages/home/<USER>"
import { Layout } from "~/pages/home/<USER>/layout"
import { isMac } from "~/utils/compatibility"
import { useLocation } from "@solidjs/router"
import { SideMenu } from "~/pages/manage/SideMenu"
import { side_menu_items } from "~/pages/manage/sidemenu_items"
import { home_side_menu_items } from "~/pages/home/<USER>"

const { isOpen, onOpen, onClose } = createDisclosure()
const {
  isOpen: isHomeMenuOpen,
  onOpen: onHomeMenuOpen,
  onClose: onHomeMenuClose,
} = createDisclosure()
const [logOutReqLoading, logOutReq] = useFetch(
  (): PResp<any> => r.get("/auth/logout"),
)

export const UnifiedHeader = () => {
  const t = useT()
  const { to } = useRouter()
  const location = useLocation()

  // 个人设置模态窗口状态
  const {
    isOpen: isProfileModalOpen,
    onOpen: onProfileModalOpen,
    onClose: onProfileModalClose,
  } = createDisclosure()

  // 个人设置表单状态
  const [username, setUsername] = createSignal(me().username)
  const [email, setEmail] = createSignal(me().email || "")
  const [password, setPassword] = createSignal("")
  const [confirmPassword, setConfirmPassword] = createSignal("")

  // 保存个人设置
  const [profileLoading, saveProfile] = useFetch(
    (): PEmptyResp =>
      r.post("/me/update", {
        username: username(),
        email: email(),
        password: password(),
        sso_id: me().sso_id,
      }),
  )

  // 判断当前页面类型
  const isManagePage = createMemo(() =>
    location.pathname.startsWith("/@manage"),
  )
  const isFilePage = createMemo(
    () => !isManagePage() && !location.pathname.startsWith("/@login"),
  )

  // Logo配置
  const logos = getSetting("logo").split("\n")
  const logo = useColorModeValue(logos[0], logos.pop())

  // 保存个人设置
  const saveMe = async () => {
    if (password() && password() !== confirmPassword()) {
      notify.warning(t("users.confirm_password_not_same"))
      return
    }
    const resp = await saveProfile()
    handleResp(resp, () => {
      setMe({ ...me(), username: username(), email: email() })
      notify.success(t("users.update_profile_success"))
      onProfileModalClose()
      // 重置密码字段
      setPassword("")
      setConfirmPassword("")
    })
  }

  // 退出登录
  const logOut = async () => {
    handleResp(await logOutReq(), () => {
      changeToken()
      notify.success(t("manage.logout_success"))
      to(`/@login?redirect=${encodeURIComponent(location.pathname)}`)
    })
  }

  // 检测是否为移动端
  const isMobile = () => window.innerWidth < 768

  // 用户下拉菜单
  const UserMenu = () => (
    <Menu>
      <MenuTrigger
        as={HStack}
        spacing="$2"
        cursor="pointer"
        px="$2"
        py="$1"
        rounded="$md"
        transition="all 0.2s ease"
        _hover={{
          bgColor: "#1e3a8a",
          transform: "scale(1.08)",
        }}
      >
        <Avatar size="sm" name={me().username} />
        <Show when={!isMobile()}>
          <Text fontSize="$sm" fontWeight="$medium">
            {me().username}
          </Text>
          <Icon as={AiOutlineDown} boxSize="$3" />
        </Show>
      </MenuTrigger>
      <MenuContent bgColor="#0f1629">
        <MenuItem
          icon={<IoPersonOutline />}
          color="#9ca3af"
          _hover={{
            bgColor: "#1e3a8a",
            color: "#ffffff",
          }}
          onSelect={() => {
            // 重置表单数据为当前用户数据
            setUsername(me().username)
            setEmail(me().email || "")
            setPassword("")
            setConfirmPassword("")
            onProfileModalOpen()
          }}
        >
          个人设置
        </MenuItem>
        <Show when={!isManagePage()}>
          <MenuItem
            icon={<FaRegularUser />}
            color="#9ca3af"
            _hover={{
              bgColor: "#1e3a8a",
              color: "#ffffff",
            }}
            onSelect={() => to("/@manage")}
          >
            {t("home.footer.manage")}
          </MenuItem>
        </Show>
        <Show when={isManagePage()}>
          <MenuItem
            icon={<FaRegularUser />}
            color="#9ca3af"
            _hover={{
              bgColor: "#1e3a8a",
              color: "#ffffff",
            }}
            onSelect={() => to("/")}
          >
            {t("manage.sidemenu.home")}
          </MenuItem>
        </Show>
        <MenuItem
          icon={<IoExit />}
          color="#9ca3af"
          _hover={{
            bgColor: "#1e3a8a",
            color: "#ffffff",
          }}
          onSelect={logOut}
        >
          {t("login.logout")}
        </MenuItem>
      </MenuContent>
    </Menu>
  )

  const fixedProps = createMemo<CenterProps>(() => {
    // 导航栏固定在顶部，不跟随页面滚动
    return {
      position: "fixed",
      zIndex: "$overlay", // 使用更高的层级确保在最顶层
      top: 0,
      left: 0,
      right: 0,
    }
  })

  return (
    <Box
      {...fixedProps()}
      bgColor={useColorModeValue("$neutral1", "#1e2a4a")()}
      class="unified-header"
      w="$full"
      shadow="$md"
      minH="64px"
      borderBottom="1px solid"
      borderColor={useColorModeValue("$neutral4", "#1e3a8a")()}
    >
      <Flex px="$0" py="$0" w="$full" alignItems="center" minH="64px">
        {/* 左侧固定区域 - 侧边栏宽度 */}
        <Box
          w={{ "@initial": "auto", "@md": "240px" }}
          h="64px"
          display="flex"
          alignItems="center"
          px="$4"
          pl="calc($4 + 30px)"
          flexShrink={0}
        >
          <HStack spacing="$2" alignItems="center">
            {/* 移动端汉堡菜单 - 管理页面 */}
            <Show when={isManagePage()}>
              <IconButton
                aria-label="menu"
                icon={<TiThMenu />}
                display={{ "@sm": "none" }}
                onClick={onOpen}
                size="sm"
                transition="all 0.2s ease"
                _hover={{
                  transform: "scale(1.08)",
                }}
              />
            </Show>

            {/* 移动端汉堡菜单 - 文件页面 */}
            <Show when={isFilePage()}>
              <IconButton
                aria-label="menu"
                icon={<TiThMenu />}
                display={{ "@sm": "none" }}
                onClick={onHomeMenuOpen}
                size="sm"
                transition="all 0.2s ease"
                _hover={{
                  transform: "scale(1.08)",
                }}
              />
            </Show>

            {/* 文件页面标题 */}
            <Show when={isFilePage()}>
              <HStack spacing="$2">
                <Image
                  src={logo()!}
                  h="32px"
                  w="auto"
                  fallback={<CenterLoading />}
                />
                <Heading
                  fontSize="$xl"
                  color={useColorModeValue("$info9", "#ffffff")()}
                  cursor="pointer"
                  onClick={() => to("/")}
                  display={{ "@initial": "none", "@sm": "block" }}
                  transition="all 0.2s ease"
                  _hover={{
                    transform: "scale(1.1)",
                  }}
                >
                  {getSetting("site_title")}
                </Heading>
              </HStack>
            </Show>

            {/* 管理页面标题 */}
            <Show when={isManagePage()}>
              <HStack spacing="$2">
                <Image
                  src={logo()!}
                  h="32px"
                  w="auto"
                  fallback={<CenterLoading />}
                />
                <Heading
                  fontSize="$xl"
                  color={useColorModeValue("$info9", "#ffffff")()}
                  cursor="pointer"
                  onClick={() => to("/@manage")}
                  display={{ "@initial": "none", "@sm": "block" }}
                  transition="all 0.2s ease"
                  _hover={{
                    transform: "scale(1.1)",
                  }}
                >
                  {getSetting("site_title")}
                </Heading>
              </HStack>
            </Show>
          </HStack>
        </Box>

        {/* 右侧功能区域 - 占据剩余空间 */}
        <Box
          flex="1"
          h="64px"
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
          px="$4"
        >
          <HStack spacing="$3" alignItems="center">
            {/* 主题切换 */}
            <SwitchColorMode />

            {/* 语言切换 */}
            <SwitchLanguage />

            {/* 用户菜单 */}
            <Show when={!UserMethods.is_guest(me())}>
              <UserMenu />
            </Show>

            {/* 访客登录按钮 */}
            <Show when={UserMethods.is_guest(me())}>
              <IconButton
                aria-label="login"
                icon={<IoPersonOutline />}
                onClick={() => to("/@login")}
                size="sm"
                transition="all 0.2s ease"
                _hover={{
                  transform: "scale(1.08)",
                }}
              />
            </Show>
          </HStack>
        </Box>
      </Flex>

      {/* 移动端侧边栏 - 管理页面 */}
      <Show when={isManagePage()}>
        <Drawer opened={isOpen()} placement="left" onClose={onClose}>
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton />
            <DrawerHeader color="$info9">
              {getSetting("site_title")}
            </DrawerHeader>
            <DrawerBody>
              <SideMenu items={side_menu_items} />
              <Center>
                <HStack spacing="$4" p="$2" color="$neutral11">
                  <SwitchLanguage as={IoLanguageOutline} boxSize="$8" />
                  <SwitchColorMode />
                </HStack>
              </Center>
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      </Show>

      {/* 移动端侧边栏 - 文件页面 */}
      <Show when={isFilePage()}>
        <Drawer
          opened={isHomeMenuOpen()}
          placement="left"
          onClose={onHomeMenuClose}
        >
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton />
            <DrawerHeader color="$info9">
              {getSetting("site_title")}
            </DrawerHeader>
            <DrawerBody>
              <SideMenu items={home_side_menu_items} />
              <Center>
                <HStack spacing="$4" p="$2" color="$neutral11">
                  <SwitchLanguage as={IoLanguageOutline} boxSize="$8" />
                  <SwitchColorMode />
                </HStack>
              </Center>
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      </Show>

      {/* 个人设置模态窗口 */}
      <Modal opened={isProfileModalOpen()} onClose={onProfileModalClose}>
        <ModalOverlay />
        <ModalContent
          bgColor="#0f1629"
          border="1px solid #0f1629"
          rounded="$lg"
          maxW="500px"
        >
          <ModalHeader color="#ffffff">{t("global.profile.title")}</ModalHeader>
          <ModalCloseButton color="#ffffff" />
          <ModalBody pb="$6">
            <VStack spacing="$4" w="$full">
              <SimpleGrid gap="$4" columns={1} w="$full">
                <FormControl>
                  <FormLabel for="modal-username" color="#ffffff">
                    {t("global.profile.username")}
                  </FormLabel>
                  <Input
                    id="modal-username"
                    value={username()}
                    onInput={(e) => setUsername(e.currentTarget.value)}
                    bgColor="#1e2a4a"
                    border="1px solid #1e3a8a"
                    color="#ffffff"
                    _focus={{
                      borderColor: "#1e3a8a",
                      boxShadow: "0 0 0 1px #1e3a8a",
                    }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel for="modal-email" color="#ffffff">
                    {t("global.profile.email")}
                  </FormLabel>
                  <Input
                    id="modal-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email()}
                    onInput={(e) => setEmail(e.currentTarget.value)}
                    bgColor="#1e2a4a"
                    border="1px solid #1e3a8a"
                    color="#ffffff"
                    _focus={{
                      borderColor: "#1e3a8a",
                      boxShadow: "0 0 0 1px #1e3a8a",
                    }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel for="modal-password" color="#ffffff">
                    {t("global.profile.password")}
                  </FormLabel>
                  <Input
                    id="modal-password"
                    type="password"
                    placeholder="********"
                    value={password()}
                    onInput={(e) => setPassword(e.currentTarget.value)}
                    bgColor="#1e2a4a"
                    border="1px solid #1e3a8a"
                    color="#ffffff"
                    _focus={{
                      borderColor: "#1e3a8a",
                      boxShadow: "0 0 0 1px #1e3a8a",
                    }}
                  />
                  <FormHelperText color="#9ca3af">
                    {t("global.profile.password_help")}
                  </FormHelperText>
                </FormControl>

                <FormControl>
                  <FormLabel for="modal-confirm-password" color="#ffffff">
                    {t("global.profile.confirm_password")}
                  </FormLabel>
                  <Input
                    id="modal-confirm-password"
                    type="password"
                    placeholder="********"
                    value={confirmPassword()}
                    onInput={(e) => setConfirmPassword(e.currentTarget.value)}
                    bgColor="#1e2a4a"
                    border="1px solid #1e3a8a"
                    color="#ffffff"
                    _focus={{
                      borderColor: "#1e3a8a",
                      boxShadow: "0 0 0 1px #1e3a8a",
                    }}
                  />
                  <FormHelperText color="#9ca3af">
                    {t("global.profile.confirm_password_help")}
                  </FormHelperText>
                </FormControl>
              </SimpleGrid>

              <Button
                w="$full"
                bgColor="#3b82f6"
                color="#ffffff"
                loading={profileLoading()}
                onClick={saveMe}
                _hover={{
                  bgColor: "#2563eb",
                }}
              >
                {t("global.save")}
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  )
}

export { onClose }
