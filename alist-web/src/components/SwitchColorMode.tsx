import { Icon, useColorMode, useColorModeValue, Box } from "@hope-ui/solid"
// import { IoMoonOutline as Moon } from "solid-icons/io";
import { FiSun as Sun } from "solid-icons/fi"
import { FiMoon as Moon } from "solid-icons/fi"

const SwitchColorMode = () => {
  const { toggleColorMode } = useColorMode()
  const icon = useColorModeValue(
    {
      size: "$6",
      component: Moon,
      p: "$0_5",
    },
    {
      size: "$6",
      component: Sun,
      p: "$0_5",
    },
  )
  return (
    <Box
      cursor="pointer"
      onClick={toggleColorMode}
      px="$3"
      py="$1"
      transition="all 0.2s ease"
      _hover={{
        bgColor: "#1e3a8a",
        transform: "scale(1.08)",
      }}
      rounded="$md"
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      <Icon
        boxSize={icon().size}
        as={icon().component}
        color="#9ca3af"
        _hover={{
          color: "#ffffff",
        }}
      />
    </Box>
  )
}
export { SwitchColorMode }
