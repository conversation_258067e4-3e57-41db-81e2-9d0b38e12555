import { VStack, Box } from "@hope-ui/solid"
import { createSignal, createMemo, onMount, onCleanup, For, JSX } from "solid-js"

interface SimpleVirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => JSX.Element
  overscan?: number
}

export function SimpleVirtualList<T>(props: SimpleVirtualListProps<T>) {
  const [scrollTop, setScrollTop] = createSignal(0)
  let containerRef: HTMLDivElement | undefined
  
  const overscan = () => props.overscan ?? 5
  
  // 计算可见范围
  const visibleRange = createMemo(() => {
    const containerHeight = props.containerHeight
    const itemHeight = props.itemHeight
    const scrollTop_ = scrollTop()
    
    const startIndex = Math.max(0, Math.floor(scrollTop_ / itemHeight) - overscan())
    const endIndex = Math.min(
      props.items.length - 1,
      Math.floor((scrollTop_ + containerHeight) / itemHeight) + overscan()
    )
    
    return { startIndex, endIndex }
  })
  
  // 可见项目
  const visibleItems = createMemo(() => {
    const { startIndex, endIndex } = visibleRange()
    return props.items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
      originalIndex: startIndex + index
    }))
  })
  
  // 总高度
  const totalHeight = createMemo(() => props.items.length * props.itemHeight)
  
  // 偏移量
  const offsetY = createMemo(() => visibleRange().startIndex * props.itemHeight)
  
  // 滚动处理
  const handleScroll = (e: Event) => {
    const target = e.target as HTMLDivElement
    setScrollTop(target.scrollTop)
  }
  
  onMount(() => {
    if (containerRef) {
      containerRef.addEventListener('scroll', handleScroll, { passive: true })
    }
  })
  
  onCleanup(() => {
    if (containerRef) {
      containerRef.removeEventListener('scroll', handleScroll)
    }
  })
  
  return (
    <Box
      ref={containerRef}
      height={`${props.containerHeight}px`}
      overflowY="auto"
      css={{
        scrollbarWidth: "thin",
        "&::-webkit-scrollbar": {
          width: "6px",
        },
        "&::-webkit-scrollbar-track": {
          background: "transparent",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "$neutral6",
          borderRadius: "3px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "$neutral7",
        },
      }}
    >
      {/* 虚拟容器，用于撑开滚动条 */}
      <Box height={`${totalHeight()}px`} position="relative">
        {/* 可见项目容器 */}
        <Box
          position="absolute"
          top={`${offsetY()}px`}
          left="0"
          right="0"
        >
          <VStack spacing="$2">
            <For each={visibleItems()}>
              {({ item, originalIndex }) => props.renderItem(item, originalIndex)}
            </For>
          </VStack>
        </Box>
      </Box>
    </Box>
  )
}
