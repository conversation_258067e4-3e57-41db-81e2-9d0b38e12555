import { password } from "~/store"
import { EmptyResp } from "~/types"
import { r } from "~/utils"
import { SetUpload, Upload } from "./types"
import { calculateHash, calculateHashWithProgress, shouldTryRapidUpload } from "./util"
export const StreamUpload: Upload = async (
  uploadPath: string,
  file: File,
  setUpload: SetUpload,
  asTask = false,
  overwrite = false,
  rapid = false,
): Promise<Error | undefined> => {
  let oldTimestamp = new Date().valueOf()
  let oldLoaded = 0
  let headers: { [k: string]: any } = {
    "File-Path": encodeURIComponent(uploadPath),
    "As-Task": asTask,
    "Content-Type": file.type || "application/octet-stream",
    "Last-Modified": file.lastModified,
    Password: password(),
    Overwrite: overwrite.toString(),
  }

  // 智能秒传判断
  const shouldTryRapid = rapid || shouldTryRapidUpload(file);

  if (shouldTryRapid) {
    try {
      setUpload("status", "hashing");
      setUpload("msg", "正在计算文件哈希，准备秒传...");

      const { md5, sha1, sha256 } = await calculateHashWithProgress(file, (progress) => {
        setUpload("progress", Math.round(progress * 0.3)); // 哈希计算占30%进度
        setUpload("msg", `计算哈希中... ${Math.round(progress)}%`);
      });

      headers["X-File-Md5"] = md5;
      headers["X-File-Sha1"] = sha1;
      headers["X-File-Sha256"] = sha256;

      setUpload("msg", "尝试秒传...");
      setUpload("progress", 30);
    } catch (error) {
      console.warn("Hash calculation failed, fallback to normal upload:", error);
      setUpload("msg", "哈希计算失败，使用普通上传");
      setUpload("progress", 0);
    }
  }
  const resp: EmptyResp = await r.put("/fs/put", file, {
    headers: headers,
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total) {
        const complete =
          ((progressEvent.loaded / progressEvent.total) * 100) | 0
        setUpload("progress", complete)

        const timestamp = new Date().valueOf()
        const duration = (timestamp - oldTimestamp) / 1000
        if (duration > 1) {
          const loaded = progressEvent.loaded - oldLoaded
          const speed = loaded / duration
          const remain = progressEvent.total - progressEvent.loaded
          const remainTime = remain / speed
          setUpload("speed", speed)
          console.log(remainTime)

          oldTimestamp = timestamp
          oldLoaded = progressEvent.loaded
        }

        if (complete === 100) {
          setUpload("status", "backending")
        }
      }
    },
  })
  if (resp.code === 200) {
    return
  } else {
    return new Error(resp.message)
  }
}
