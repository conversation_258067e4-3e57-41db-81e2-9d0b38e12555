import { Checkbox, createDisclosure } from "@hope-ui/solid"
import { createSignal, onCleanup, createMemo } from "solid-js"
import { ModalInput } from "~/components"
import { useFetch, usePath, useRouter, useT } from "~/hooks"
import { password, me } from "~/store"
import { bus, fsNewFile, handleRespWithNotifySuccess, pathJoin, notify } from "~/utils"
import { UserMethods } from "~/types"

export const NewFile = () => {
  const t = useT()
  const { isOpen, onOpen, onClose } = createDisclosure()
  const [loading, ok] = useFetch(fsNewFile)
  const { refresh } = usePath()
  const { pathname } = useRouter()
  const [overwrite, setOverwrite] = createSignal(false)

  // 检查是否为普通用户在根目录
  const isRootForNormalUser = createMemo(() => {
    const user = me()
    const path = pathname()
    return !UserMethods.is_admin(user) && (path === "/" || path === "")
  })

  const handler = (name: string) => {
    if (name === "new_file") {
      // 检查普通用户是否在根目录
      if (isRootForNormalUser()) {
        notify.error("当前目录不支持创建文件！")
        return
      }
      onOpen()
      setOverwrite(false)
    }
  }
  bus.on("tool", handler)
  onCleanup(() => {
    bus.off("tool", handler)
  })
  return (
    <ModalInput
      title="home.toolbar.input_filename"
      footerSlot={
        <Checkbox
          mr="auto"
          checked={overwrite()}
          onChange={() => {
            setOverwrite(!overwrite())
          }}
        >
          {t("home.conflict_policy.overwrite_existing")}
        </Checkbox>
      }
      opened={isOpen()}
      onClose={onClose}
      loading={loading()}
      onSubmit={async (name) => {
        const resp = await ok(
          pathJoin(pathname(), name),
          password(),
          overwrite(),
        )
        handleRespWithNotifySuccess(resp, () => {
          refresh()
          onClose()
        })
      }}
    />
  )
}
