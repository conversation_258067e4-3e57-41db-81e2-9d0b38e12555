import { HStack, Icon, Text } from "@hope-ui/solid"
import { Motion } from "@motionone/solid"
import { useContextMenu } from "solid-contextmenu"
import { batch, Show } from "solid-js"
import { LinkWithPush } from "~/components"
import { useP<PERSON>, useRouter, useUtil } from "~/hooks"
import {
  checkboxOpen,
  getMainColor,
  local,
  OrderBy,
  selectIndex,
} from "~/store"
import { ObjType, StoreObj } from "~/types"
import {
  bus,
  formatDate,
  getFileSize,
  hoverColor,
  truncateFilename,
} from "~/utils"
import { getIconByObj, getIconAndColorByObj } from "~/utils/icon"
import { ItemCheckbox, useSelectWithMouse } from "./helper"

export interface Col {
  name: OrderBy
  textAlign: "left" | "right"
  w: any
}

export const cols: Col[] = [
  { name: "name", textAlign: "left", w: "70%" },
  {
    name: "size",
    textAlign: "right",
    w: { "@initial": "60px", "@md": "9%" },
  }, // 文件大小区域占9%
  {
    name: "modified",
    textAlign: "right",
    w: { "@initial": 0, "@md": "21%" },
  }, // 修改时间区域占21%
]

export const ListItem = (props: { obj: StoreObj; index: number }) => {
  const { isHide } = useUtil()
  if (isHide(props.obj)) {
    return null
  }
  const { setPathAs } = usePath()
  const { show } = useContextMenu({ id: 1 })
  const { pushHref, to } = useRouter()
  const { openWithDoubleClick, toggleWithClick, restoreSelectionCache } =
    useSelectWithMouse()
  const filenameStyle = () => local["list_item_filename_overflow"]

  // 获取图标和颜色信息
  const iconInfo = () => getIconAndColorByObj(props.obj)
  return (
    <Motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      style={{
        width: "100%",
        maxWidth: "100%",
        boxSizing: "border-box",
        overflow: "visible", // 允许悬停效果显示
      }}
    >
      <HStack
        classList={{ selected: !!props.obj.selected }}
        class="list-item viselect-item"
        data-index={props.index}
        w="100%"
        maxW="100%"
        minW="0"
        p="$3"
        my="$1"
        rounded="$xl"
        transition="all 0.2s"
        border="2px solid #1e3a8a"
        bgColor="#1e2a4a"
        _hover={{
          transform: "scale(1.02)",
          transformOrigin: "center", // 确保从中心缩放
          borderColor: "#7790BC",
          zIndex: 10, // 提升层级，避免被遮挡
          position: "relative", // 确保zIndex生效
        }}
        as={LinkWithPush}
        href={
          props.obj.type === ObjType.VIRTUAL_STORAGE
            ? props.obj.path
            : props.obj.name
        }
        cursor={
          openWithDoubleClick() || toggleWithClick() ? "default" : "pointer"
        }
        on:dblclick={() => {
          if (!openWithDoubleClick()) return
          selectIndex(props.index, true, true)

          if (!props.obj.is_dir && props.obj.type !== ObjType.VIRTUAL_STORAGE) {
            // 图片文件使用原本的预览方式（LightGallery）
            if (props.obj.type === ObjType.IMAGE) {
              bus.emit("gallery", props.obj.name)
            } else {
              // 其他文件类型使用模态窗口预览
              bus.emit("file-preview", props.obj.name)
            }
          } else {
            // 如果是文件夹或虚拟存储空间，正常跳转
            if (props.obj.type === ObjType.VIRTUAL_STORAGE) {
              // 虚拟存储空间使用特殊路径
              to(props.obj.path)
            } else {
              to(pushHref(props.obj.name))
            }
          }
        }}
        on:click={(e: MouseEvent) => {
          e.preventDefault()
          if (openWithDoubleClick()) return
          if (e.ctrlKey || e.metaKey || e.shiftKey) return
          if (!restoreSelectionCache()) return
          if (toggleWithClick())
            return selectIndex(props.index, !props.obj.selected)

          if (!props.obj.is_dir && props.obj.type !== ObjType.VIRTUAL_STORAGE) {
            // 图片文件使用原本的预览方式（LightGallery）
            if (props.obj.type === ObjType.IMAGE) {
              bus.emit("gallery", props.obj.name)
            } else {
              // 其他文件类型使用模态窗口预览
              bus.emit("file-preview", props.obj.name)
            }
          } else {
            // 如果是文件夹或虚拟存储空间，正常跳转
            if (props.obj.type === ObjType.VIRTUAL_STORAGE) {
              // 虚拟存储空间使用特殊路径
              to(props.obj.path)
            } else {
              to(pushHref(props.obj.name))
            }
          }
        }}
        onMouseEnter={() => {
          if (props.obj.type === ObjType.VIRTUAL_STORAGE) {
            // 虚拟存储空间使用绝对路径
            setPathAs(props.obj.path, true, false)
          } else {
            setPathAs(props.obj.name, props.obj.is_dir, true)
          }
        }}
        onContextMenu={(e: MouseEvent) => {
          batch(() => {
            // if (!checkboxOpen()) {
            //   toggleCheckbox();
            // }
            selectIndex(props.index, true, true)
          })
          show(e, { props: props.obj })
        }}
        css={{
          boxSizing: "border-box",
          overflow: "hidden", // 防止内容溢出
          maxWidth: "calc(100vw - 312px)", // 强制限制最大宽度：视窗宽度减去侧边栏(240px)和左右内边距(32px)和额外边距(40px)
          width: "100%",
        }}
      >
        <HStack class="name-box" spacing="$1" w="70%" minW="0" maxW="70%">
          <Show when={checkboxOpen()}>
            <ItemCheckbox
              // colorScheme="neutral"
              on:mousedown={(e: MouseEvent) => {
                e.stopPropagation()
              }}
              on:click={(e: MouseEvent) => {
                e.stopPropagation()
              }}
              checked={props.obj.selected}
              onChange={(e: any) => {
                selectIndex(props.index, e.target.checked)
              }}
            />
          </Show>
          <Icon
            class="icon"
            boxSize="$6"
            color={iconInfo().color}
            as={iconInfo().icon}
            mr="$3"
            cursor={props.obj.type !== ObjType.IMAGE ? "inherit" : "pointer"}
            on:click={(e: MouseEvent) => {
              if (props.obj.type !== ObjType.IMAGE) return
              if (e.ctrlKey || e.metaKey || e.shiftKey) return
              if (!restoreSelectionCache()) return
              bus.emit("gallery", props.obj.name)
              e.preventDefault()
              e.stopPropagation()
            }}
          />
          <Text
            class="name"
            fontSize="$base"
            fontWeight="$medium"
            flex="1"
            css={{
              wordBreak: "break-all",
              whiteSpace: "nowrap", // 强制单行显示
              overflow: "hidden", // 隐藏溢出内容
              textOverflow: "ellipsis", // 显示省略号
              "scrollbar-width": "none", // firefox
              "&::-webkit-scrollbar": {
                // webkit
                display: "none",
              },
              minWidth: "0", // 允许文件名自适应
              maxWidth: "100%", // 限制最大宽度不超出容器
              flexShrink: 1, // 允许文件名区域收缩
            }}
            title={props.obj.name}
          >
            {truncateFilename(props.obj.name, 50)}
          </Text>
        </HStack>
        <Text
          class="size"
          w={cols[1].w}
          textAlign={cols[1].textAlign as any}
          fontSize="$sm"
          color="$neutral11"
        >
          {getFileSize(props.obj.size)}
        </Text>
        <Text
          class="modified"
          display={{ "@initial": "none", "@md": "inline" }}
          w={cols[2].w}
          textAlign={cols[2].textAlign as any}
          fontSize="$sm"
          color="$neutral11"
        >
          {formatDate(props.obj.modified)}
        </Text>
      </HStack>
    </Motion.div>
  )
}
