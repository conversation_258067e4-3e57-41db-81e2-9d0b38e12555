import { Center, VStack, Icon, Text } from "@hope-ui/solid"
import { Motion } from "@motionone/solid"
import { useContextMenu } from "solid-contextmenu"
import { batch, Show } from "solid-js"
import { CenterLoading, LinkWithPush, ImageWithError } from "~/components"
import { usePath, useRouter, useUtil } from "~/hooks"
import { checkboxOpen, getMainColor, local, selectIndex } from "~/store"
import { ObjType, StoreObj } from "~/types"
import { bus, hoverColor, truncateFilename } from "~/utils"
import { getIconByObj, getIconAndColorByObj } from "~/utils/icon"
import { ItemCheckbox, useSelectWithMouse } from "./helper"

export const GridItem = (props: { obj: StoreObj; index: number }) => {
  const { isHide } = useUtil()
  if (isHide(props.obj)) {
    return null
  }
  const { setPathAs } = usePath()

  // 获取图标和颜色信息
  const iconInfo = () => getIconAndColorByObj(props.obj)

  const objIcon = (
    <Icon
      color={iconInfo().color}
      boxSize={`${parseInt(local["grid_item_size"]) - 30}px`}
      as={iconInfo().icon}
    />
  )
  const { show } = useContextMenu({ id: 1 })
  const { pushHref, to } = useRouter()
  const { openWithDoubleClick, toggleWithClick, restoreSelectionCache } =
    useSelectWithMouse()
  return (
    <Motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      style={{
        width: "100%",
        maxWidth: "100%",
        minWidth: "0",
        overflow: "visible", // 允许缩放效果向外扩展
        boxSizing: "border-box",
        position: "relative", // 确保缩放时不影响其他元素布局
      }}
    >
      <VStack
        classList={{ selected: !!props.obj.selected }}
        class="grid-item viselect-item group"
        data-index={props.index}
        w="$full"
        maxW="100%"
        minW="0"
        p="$1"
        spacing="$1"
        rounded="$lg"
        transition="all 0.3s"
        border="2px solid #1e3a8a"
        bgColor="#1e2a4a"
        _hover={{
          transform: "scale(1.06)",
          borderColor: "#7790BC",
          border: "2px solid #7790BC", // 确保悬停时边框不消失
          zIndex: 10, // 提升层级避免被其他元素遮挡
        }}
        as={LinkWithPush}
        href={
          props.obj.type === ObjType.VIRTUAL_STORAGE
            ? props.obj.path
            : props.obj.name
        }
        cursor={
          openWithDoubleClick() || toggleWithClick() ? "default" : "pointer"
        }
        bgColor={props.obj.selected ? hoverColor() : "#1e2a4a"}
        css={{
          boxSizing: "border-box",
          overflow: "visible", // 允许缩放效果向外扩展
          maxWidth: "100%",
          backgroundColor: "#1e2a4a", // 确保背景色为指定颜色
          transformOrigin: "center", // 确保从中心点缩放
          "&:hover": {
            backgroundColor: "#1e2a4a", // 悬停时保持背景色
            border: "2px solid #7790BC", // 悬停时边框颜色
            transform: "scale(1.06)",
            transformOrigin: "center", // 从中心点向外缩放
            zIndex: 10,
            position: "relative", // 确保zIndex生效
          },
        }}
        on:dblclick={() => {
          if (!openWithDoubleClick()) return
          selectIndex(props.index, true, true)

          if (!props.obj.is_dir && props.obj.type !== ObjType.VIRTUAL_STORAGE) {
            // 图片文件使用原本的预览方式（LightGallery）
            if (props.obj.type === ObjType.IMAGE) {
              bus.emit("gallery", props.obj.name)
            } else {
              // 其他文件类型使用模态窗口预览
              bus.emit("file-preview", props.obj.name)
            }
          } else {
            // 如果是文件夹或虚拟存储空间，正常跳转
            if (props.obj.type === ObjType.VIRTUAL_STORAGE) {
              // 虚拟存储空间使用特殊路径
              to(props.obj.path)
            } else {
              to(pushHref(props.obj.name))
            }
          }
        }}
        on:click={(e: MouseEvent) => {
          e.preventDefault()
          if (openWithDoubleClick()) return
          if (e.ctrlKey || e.metaKey || e.shiftKey) return
          if (!restoreSelectionCache()) return
          if (toggleWithClick())
            return selectIndex(props.index, !props.obj.selected)

          if (!props.obj.is_dir && props.obj.type !== ObjType.VIRTUAL_STORAGE) {
            // 图片文件使用原本的预览方式（LightGallery）
            if (props.obj.type === ObjType.IMAGE) {
              bus.emit("gallery", props.obj.name)
            } else {
              // 其他文件类型使用模态窗口预览
              bus.emit("file-preview", props.obj.name)
            }
          } else {
            // 如果是文件夹或虚拟存储空间，正常跳转
            if (props.obj.type === ObjType.VIRTUAL_STORAGE) {
              // 虚拟存储空间使用特殊路径
              to(props.obj.path)
            } else {
              to(pushHref(props.obj.name))
            }
          }
        }}
        onMouseEnter={() => {
          if (props.obj.type === ObjType.VIRTUAL_STORAGE) {
            // 虚拟存储空间使用绝对路径
            setPathAs(props.obj.path, true, false)
          } else {
            setPathAs(props.obj.name, props.obj.is_dir, true)
          }
        }}
        onContextMenu={(e: MouseEvent) => {
          batch(() => {
            // if (!checkboxOpen()) {
            //   toggleCheckbox();
            // }
            selectIndex(props.index, true, true)
          })
          show(e, { props: props.obj })
        }}
      >
        <Center
          class="item-thumbnail"
          h={`${parseInt(local["grid_item_size"])}px`}
          w="$full"
          cursor={props.obj.type !== ObjType.IMAGE ? "inherit" : "pointer"}
          on:click={(e: MouseEvent) => {
            if (props.obj.type !== ObjType.IMAGE) return
            if (e.ctrlKey || e.metaKey || e.shiftKey) return
            if (!restoreSelectionCache()) return
            bus.emit("gallery", props.obj.name)
            e.preventDefault()
            e.stopPropagation()
          }}
          pos="relative"
        >
          <Show when={checkboxOpen()}>
            <ItemCheckbox
              pos="absolute"
              left="$1"
              top="$1"
              class="grid-item-checkbox"
              // colorScheme="neutral"
              on:mousedown={(e: MouseEvent) => {
                e.stopPropagation()
              }}
              on:click={(e: MouseEvent) => {
                e.stopPropagation()
              }}
              checked={props.obj.selected}
              onChange={(e: any) => {
                selectIndex(props.index, e.target.checked)
              }}
            />
          </Show>
          <Show when={props.obj.thumb} fallback={objIcon}>
            <ImageWithError
              maxH="$full"
              maxW="$full"
              rounded="$lg"
              shadow="$md"
              fallback={<CenterLoading size="lg" />}
              fallbackErr={objIcon}
              src={props.obj.thumb}
              loading="lazy"
            />
          </Show>
        </Center>
        <Text
          css={{
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            minWidth: "0", // 允许文本收缩
            maxWidth: "100%", // 限制最大宽度为容器宽度
            boxSizing: "border-box",
          }}
          w="$full"
          overflow="hidden"
          textAlign="center"
          fontSize="$sm"
          title={props.obj.name}
        >
          {truncateFilename(props.obj.name, 50)}
        </Text>
      </VStack>
    </Motion.div>
  )
}
