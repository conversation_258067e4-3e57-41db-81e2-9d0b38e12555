import {
  VStack,
  Input,
  Heading,
  HStack,
  IconButton,
  Checkbox,
  Text,
  Badge,
  Progress,
  ProgressIndicator,
  Button,
  Box,
} from "@hope-ui/solid"
import { createSignal, For, Show, createMemo, onMount, onCleanup } from "solid-js"
import { usePath, useRouter, useT } from "~/hooks"
import { getMainColor, me } from "~/store"
import { UserMethods } from "~/types"
import { UploadPagination, getVisibleFiles } from "~/components/UploadPagination"
import {
  globalUploadManager,
  globalPerformanceMonitor,
  sortFilesByPriority
} from "~/utils/SmartUploadManager"

// 文件大小格式化函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
import {
  RiDocumentFolderUploadFill,
  RiDocumentFileUploadFill,
} from "solid-icons/ri"
import { getFileSize, notify, pathJoin, bus } from "~/utils"
import { asyncPool } from "~/utils/async_pool"
import { createStore } from "solid-js/store"
import { UploadFileProps, StatusBadge } from "./types"
import { File2Upload, traverseFileTree } from "./util"
import { SelectWrapper } from "~/components"
import { getUploads } from "./uploads"

const UploadFile = (props: UploadFileProps) => {
  const t = useT()
  return (
    <VStack
      w="$full"
      spacing="$1"
      rounded="$lg"
      border="1px solid $neutral7"
      alignItems="start"
      p="$2"
      _hover={{
        border: `1px solid ${getMainColor()}`,
      }}
    >
      <Text
        css={{
          wordBreak: "break-all",
        }}
      >
        {props.path}
      </Text>
      <HStack spacing="$2">
        <Badge colorScheme={StatusBadge[props.status]}>
          {t(`home.upload.${props.status}`)}
        </Badge>
        <Text>{getFileSize(props.speed)}/s</Text>
      </HStack>
      <Progress
        w="$full"
        trackColor="$info3"
        rounded="$full"
        value={props.progress}
        size="sm"
      >
        <ProgressIndicator color={getMainColor()} rounded="$md" />
        {/* <ProgressLabel /> */}
      </Progress>
      <Text color="$danger10">{props.msg}</Text>
    </VStack>
  )
}

const Upload = () => {
  const t = useT()
  const { pathname } = useRouter()
  const { refresh } = usePath()
  const [drag, setDrag] = createSignal(false)
  const [uploading, setUploading] = createSignal(false)
  const [asTask, setAsTask] = createSignal(false)
  const [overwrite, setOverwrite] = createSignal(false)
  const [rapid, setRapid] = createSignal(true)

  // 分页相关状态
  const [currentPage, setCurrentPage] = createSignal(0)
  const [pageSize, setPageSize] = createSignal(10)
  const [statusFilter, setStatusFilter] = createSignal<string[]>([])
  const [uploadFiles, setUploadFiles] = createStore<{
    uploads: UploadFileProps[]
  }>({
    uploads: [],
  })
  const allDone = () => {
    return uploadFiles.uploads.every(({ status }) =>
      ["success", "error"].includes(status),
    )
  }

  // 计算可见的文件列表
  const visibleFiles = createMemo(() => {
    return getVisibleFiles(
      uploadFiles.uploads,
      currentPage(),
      pageSize(),
      statusFilter()
    );
  });

  // 重置分页到第一页（当文件列表变化时）
  const resetPagination = () => {
    setCurrentPage(0);
  };

  // 监听直接上传事件
  onMount(() => {
    const uploadFilesHandler = (files: File[]) => {
      handleAddFiles(files);
    };

    bus.on("upload_files", uploadFilesHandler);

    onCleanup(() => {
      bus.off("upload_files", uploadFilesHandler);
    });
  });
  let fileInput: HTMLInputElement
  let folderInput: HTMLInputElement

  // 检查是否为普通用户在根目录
  const isRootForNormalUser = createMemo(() => {
    const user = me()
    const path = pathname()
    return !UserMethods.is_admin(user) && (path === "/" || path === "")
  })

  const handleAddFiles = async (files: File[]) => {
    if (files.length === 0) return

    // 检查普通用户是否在根目录上传
    if (isRootForNormalUser()) {
      notify.error("当前目录不支持创建文件！")
      return
    }

    setUploading(true)

    // 重置分页到第一页
    resetPagination();

    for (const file of files) {
      const upload = File2Upload(file)
      setUploadFiles("uploads", (uploads) => [...uploads, upload])
    }

    // 显示上传统计信息
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const folderFiles = files.filter(file => file.webkitRelativePath);
    const regularFiles = files.filter(file => !file.webkitRelativePath);

    console.log(`开始上传: ${files.length} 个文件 (${formatFileSize(totalSize)})`);
    if (folderFiles.length > 0) {
      console.log(`包含文件夹文件: ${folderFiles.length} 个`);
    }
    if (regularFiles.length > 0) {
      console.log(`包含普通文件: ${regularFiles.length} 个`);
    }

    // 检查内存使用
    if (!globalPerformanceMonitor.checkMemoryUsage()) {
      notify.warning('内存使用过高，建议减少同时上传的文件数量');
    }

    // 按优先级排序文件
    const sortedFiles = sortFilesByPriority(files);
    console.log('文件已按优先级排序：小文件和常见类型优先');

    // 使用智能上传管理器
    const uploadPromises = sortedFiles.map(file =>
      globalUploadManager.add(() => handleFile(file))
    );

    // 等待所有上传完成
    const results = await Promise.allSettled(uploadPromises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.length - successful;

    console.log(`上传完成: ${successful} 成功, ${failed} 失败`);

    // 显示上传管理器状态
    const status = globalUploadManager.getStatus();
    console.log('上传管理器状态:', status);
    refresh(undefined, true)
  }
  const setUpload = (path: string, key: keyof UploadFileProps, value: any) => {
    setUploadFiles("uploads", (upload) => upload.path === path, key, value)
  }
  const uploaders = getUploads()
  const [curUploader, setCurUploader] = createSignal(uploaders[0])
  const handleFile = async (file: File) => {
    const path = file.webkitRelativePath ? file.webkitRelativePath : file.name
    setUpload(path, "status", "uploading")
    const uploadPath = pathJoin(pathname(), path)

    const startTime = Date.now();
    let lastProgressTime = startTime;
    let lastProgressLoaded = 0;

    try {
      const err = await curUploader().upload(
        uploadPath,
        file,
        (key, value) => {
          setUpload(path, key, value)

          // 监控网络速度
          if (key === 'progress' && typeof value === 'number') {
            const now = Date.now();
            const currentLoaded = (value / 100) * file.size;

            if (now - lastProgressTime > 1000) { // 每秒更新一次
              const bytesTransferred = currentLoaded - lastProgressLoaded;
              const timeElapsed = (now - lastProgressTime) / 1000;
              const speed = bytesTransferred / timeElapsed;

              // 更新全局网络速度
              globalUploadManager.updateNetworkSpeed(speed);

              lastProgressTime = now;
              lastProgressLoaded = currentLoaded;
            }
          }
        },
        asTask(),
        overwrite(),
        rapid(),
      )
      if (!err) {
        setUpload(path, "status", "success")
        setUpload(path, "progress", 100)

        // 计算上传时间
        const uploadTime = (Date.now() - startTime) / 1000;
        const avgSpeed = file.size / uploadTime;
        console.log(`文件上传完成: ${file.name}, 耗时: ${uploadTime.toFixed(1)}s, 平均速度: ${formatFileSize(avgSpeed)}/s`);

        // 触发文件上传事件，用于刷新容量信息
        bus.emit("file_uploaded")
      } else {
        setUpload(path, "status", "error")
        setUpload(path, "msg", err.message)
      }
    } catch (e: any) {
      console.error(e)
      setUpload(path, "status", "error")
      setUpload(path, "msg", e.message)
    }
  }
  return (
    <VStack w="$full" pb="$2" spacing="$2">
      <Show
        when={!uploading()}
        fallback={
          <>
            <HStack spacing="$2" flexWrap="wrap">
              <Button
                colorScheme="accent"
                onClick={() => {
                  setUploadFiles("uploads", (_uploads) =>
                    _uploads.filter(
                      ({ status }) => !["success", "error"].includes(status),
                    ),
                  )
                  console.log(uploadFiles.uploads)
                }}
              >
                {t("home.upload.clear_done")}
              </Button>

              {/* 批量操作按钮 */}
              <Button
                colorScheme="warning"
                onClick={() => {
                  // 暂停所有正在上传的文件
                  setUploadFiles("uploads", (uploads) =>
                    uploads.map(upload =>
                      upload.status === "uploading"
                        ? { ...upload, status: "pending" as const }
                        : upload
                    )
                  )
                  notify.info("已暂停所有上传任务")
                }}
                disabled={!uploadFiles.uploads.some(upload => upload.status === "uploading")}
              >
                批量暂停
              </Button>

              <Button
                colorScheme="info"
                onClick={() => {
                  // 恢复所有暂停的文件
                  const pendingFiles = uploadFiles.uploads.filter(upload => upload.status === "pending")
                  if (pendingFiles.length > 0) {
                    pendingFiles.forEach(upload => {
                      // 重新开始上传
                      const file = new File([], upload.name)
                      globalUploadManager.add(() => handleFile(file))
                    })
                    notify.info(`已恢复 ${pendingFiles.length} 个上传任务`)
                  }
                }}
                disabled={!uploadFiles.uploads.some(upload => upload.status === "pending")}
              >
                批量恢复
              </Button>

              <Button
                colorScheme="danger"
                onClick={() => {
                  // 取消所有未完成的上传
                  setUploadFiles("uploads", (uploads) =>
                    uploads.map(upload =>
                      !["success", "error"].includes(upload.status)
                        ? { ...upload, status: "error" as const, msg: "用户取消" }
                        : upload
                    )
                  )
                  notify.info("已取消所有未完成的上传任务")
                }}
                disabled={!uploadFiles.uploads.some(upload => !["success", "error"].includes(upload.status))}
              >
                批量取消
              </Button>

              <Show when={allDone()}>
                <Button
                  onClick={() => {
                    setUploading(false)
                  }}
                >
                  {t("home.upload.back")}
                </Button>
              </Show>
            </HStack>
            {/* 分页控制 */}
            <Show when={uploadFiles.uploads.length > 0}>
              <UploadPagination
                files={uploadFiles.uploads}
                currentPage={currentPage()}
                pageSize={pageSize()}
                onPageChange={setCurrentPage}
                onPageSizeChange={(size) => {
                  setPageSize(size);
                  resetPagination();
                }}
                statusFilter={statusFilter()}
                onStatusFilterChange={setStatusFilter}
              />
            </Show>

            {/* 文件列表 */}
            <For each={visibleFiles()}>
              {(upload) => <UploadFile {...upload} />}
            </For>
          </>
        }
      >
        <Input
          type="file"
          multiple
          ref={fileInput!}
          display="none"
          onChange={(e) => {
            // @ts-ignore
            handleAddFiles(Array.from(e.target.files ?? []))
          }}
        />
        <Input
          type="file"
          multiple
          // @ts-ignore
          webkitdirectory
          ref={folderInput!}
          display="none"
          onChange={(e) => {
            // @ts-ignore
            handleAddFiles(Array.from(e.target.files ?? []))
          }}
        />
        <VStack
          w="$full"
          justifyContent="center"
          border={`2px dashed ${drag() ? getMainColor() : "$neutral8"}`}
          rounded="$lg"
          onDragOver={(e: DragEvent) => {
            e.preventDefault()
            setDrag(true)
          }}
          onDragLeave={() => {
            setDrag(false)
          }}
          onDrop={async (e: DragEvent) => {
            e.preventDefault()
            e.stopPropagation()
            setDrag(false)
            const res: File[] = []
            const items = Array.from(e.dataTransfer?.items ?? [])
            const files = Array.from(e.dataTransfer?.files ?? [])
            let itemLength = items.length
            const folderEntries = []
            for (let i = 0; i < itemLength; i++) {
              const item = items[i]
              const entry = item.webkitGetAsEntry()
              if (entry?.isFile) {
                res.push(files[i])
              } else if (entry?.isDirectory) {
                folderEntries.push(entry)
              }
            }
            for (const entry of folderEntries) {
              const innerFiles = await traverseFileTree(entry)
              res.push(...innerFiles)
            }
            if (res.length === 0) {
              notify.warning(t("home.upload.no_files_drag"))
            }
            handleAddFiles(res)
          }}
          spacing="$4"
          // py="$4"
          h="$56"
        >
          <Show
            when={!drag()}
            fallback={
              <VStack spacing="$2">
                <Heading>{t("home.upload.release")}</Heading>
                <Text fontSize="$sm" color="$neutral11">
                  支持文件和文件夹混合拖拽上传
                </Text>
              </VStack>
            }
          >
            <VStack spacing="$2">
              <Heading>{t("home.upload.upload-tips")}</Heading>
              <Text fontSize="$sm" color="$neutral11">
                支持文件、文件夹混合上传，自动尝试秒传
              </Text>
            </VStack>
            <Box w="30%">
              <SelectWrapper
                value={curUploader().name}
                onChange={(name) => {
                  setCurUploader(
                    uploaders.find((uploader) => uploader.name === name)!,
                  )
                }}
                options={uploaders.map((uploader) => {
                  return {
                    label: uploader.name,
                    value: uploader.name,
                  }
                })}
              />
            </Box>
            <HStack spacing="$4">
              <IconButton
                compact
                size="xl"
                aria-label={t("home.upload.upload_folder")}
                colorScheme="accent"
                icon={<RiDocumentFolderUploadFill />}
                onClick={() => {
                  folderInput.click()
                }}
              />
              <IconButton
                compact
                size="xl"
                aria-label={t("home.upload.upload_files")}
                icon={<RiDocumentFileUploadFill />}
                onClick={() => {
                  fileInput.click()
                }}
              />
            </HStack>
            <HStack spacing="$4">
              <Checkbox
                checked={asTask()}
                onChange={() => {
                  setAsTask(!asTask())
                }}
              >
                {t("home.upload.add_as_task")}
              </Checkbox>
              <Checkbox
                checked={overwrite()}
                onChange={() => {
                  setOverwrite(!overwrite())
                }}
              >
                {t("home.conflict_policy.overwrite_existing")}
              </Checkbox>
              <Checkbox
                checked={rapid()}
                onChange={() => {
                  setRapid(!rapid())
                }}
              >
                {t("home.upload.try_rapid")}
              </Checkbox>
            </HStack>
          </Show>
        </VStack>
      </Show>
    </VStack>
  )
}

export default Upload
