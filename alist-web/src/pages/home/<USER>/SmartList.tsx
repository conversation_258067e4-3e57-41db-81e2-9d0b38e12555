import { VStack } from "@hope-ui/solid"
import { Show, createMemo, For } from "solid-js"
import { objStore, State } from "~/store"
import { VirtualStorageSkeleton } from "~/components"
import { SimpleVirtualList } from "~/components/SimpleVirtualList"
import { ListItem } from "./ListItem"
import { useSelectWithMouse } from "./helper"
import { UserMethods } from "~/types"
import { me } from "~/store"
import { useRouter } from "~/hooks"

// 智能文件列表组件，根据文件数量自动选择渲染方式
export const SmartList = () => {
  const { isMouseSupported, registerSelectContainer, captureContentMenu } =
    useSelectWithMouse()
  registerSelectContainer()

  const { pathname } = useRouter()

  // 检查是否为多基础路径用户的根路径
  const isVirtualRoot = createMemo(() => {
    const user = me()
    const path = pathname()
    return UserMethods.has_multiple_base_paths(user) && (path === "/" || path === "")
  })

  // 拖拽处理
  const onDragOver = (e: DragEvent) => {
    e.preventDefault()
  }
  
  return (
    <Show
      when={objStore.state !== State.FetchingObjs}
      fallback={
        <Show when={isVirtualRoot()}>
          <VirtualStorageSkeleton />
        </Show>
      }
    >
      {/* 普通列表渲染 */}
      <VStack
        onDragOver={onDragOver}
        oncapture:contextmenu={captureContentMenu}
        class="list viselect-container"
        w="100%"
        maxW="100%"
        minW="0"
        spacing="$2"
        pt="0"
        px="$4"
        pb="$2"
        bgColor="transparent"
        css={{
          boxSizing: "border-box",
          overflow: "visible",
          width: "100% !important",
          maxWidth: "100% !important",
        }}
      >
        <For each={objStore.objs}>
          {(obj, i) => (
            <ListItem obj={obj} index={i()} />
          )}
        </For>
      </VStack>
    </Show>
  )
}
