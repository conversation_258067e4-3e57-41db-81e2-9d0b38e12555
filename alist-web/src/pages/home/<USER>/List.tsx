import { HStack, VStack, Text, Box } from "@hope-ui/solid"
import { batch, createEffect, createSignal, For, Show, createMemo } from "solid-js"
import { useT, useRouter } from "~/hooks"
import {
  allChecked,
  checkboxOpen,
  isIndeterminate,
  objStore,
  selectAll,
  sortObjs,
  State,
  me,
} from "~/store"
import { OrderBy } from "~/store"
import { Col, cols, ListItem } from "./ListItem"
import { ItemCheckbox, useSelectWithMouse } from "./helper"
import { bus } from "~/utils"
import { VirtualStorageSkeleton, StableVirtualList } from "~/components"
import { UserMethods, ObjType } from "~/types"
// import { VirtualStorageItem } from "./VirtualStorageItem"

export const ListTitle = (props: {
  sortCallback: (orderBy: OrderBy, reverse?: boolean) => void
  disableCheckbox?: boolean
}) => {
  const t = useT()
  const [orderBy, setOrderBy] = createSignal<OrderBy>()
  const [reverse, setReverse] = createSignal(false)
  createEffect(() => {
    if (orderBy()) {
      props.sortCallback(orderBy()!, reverse())
    }
  })
  const itemProps = (col: Col) => {
    return {
      fontWeight: "bold",
      fontSize: "$sm",
      color: "$neutral11",
      textAlign: col.textAlign as any,
      cursor: "pointer",
      onClick: () => {
        if (col.name === orderBy()) {
          setReverse(!reverse())
        } else {
          batch(() => {
            setOrderBy(col.name as OrderBy)
            setReverse(false)
          })
        }
      },
    }
  }
  return (
    <HStack
      class="title"
      w="$full"
      p="$2"
      bgColor="#0f1629"
      borderBottom="1px solid #1e3a8a"
      position="sticky"
      top="174px" // 导航栏(64px) + 路径导航栏(60px) + 工具导航栏(74px) - 间距减少(24px)
      zIndex="$sticky"
    >
      <HStack w="70%" spacing="$1">
        <Text {...itemProps(cols[0])}>{t(`home.obj.${cols[0].name}`)}</Text>
      </HStack>
      <Text w={cols[1].w} {...itemProps(cols[1])}>
        {t(`home.obj.${cols[1].name}`)}
      </Text>
      <Text
        w={cols[2].w}
        {...itemProps(cols[2])}
        display={{ "@initial": "none", "@md": "inline" }}
      >
        {t(`home.obj.${cols[2].name}`)}
      </Text>
    </HStack>
  )
}

const ListLayout = () => {
  const onDragOver = (e: DragEvent) => {
    const items = Array.from(e.dataTransfer?.items ?? [])
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (item.kind === "file") {
        bus.emit("tool", "upload")
        e.preventDefault()
        break
      }
    }
  }
  const { isMouseSupported, registerSelectContainer, captureContentMenu } =
    useSelectWithMouse()
  registerSelectContainer()

  const { pathname } = useRouter()

  // 检查是否为多基础路径用户的根路径
  const isVirtualRoot = createMemo(() => {
    const user = me()
    const path = pathname()
    return UserMethods.has_multiple_base_paths(user) && (path === "/" || path === "")
  })

  // 判断是否使用虚拟滚动 - 更保守的阈值
  const shouldUseVirtualScroll = createMemo(() => {
    const itemCount = objStore.objs.length
    // 当文件数量超过100个时使用虚拟滚动
    return itemCount > 100
  })

  // 计算容器高度
  const containerHeight = createMemo(() => {
    // 计算可用高度：视窗高度 - 导航栏 - 工具栏 - 分页器等
    const viewportHeight = window.innerHeight
    const reservedHeight = 250 // 预留给其他组件的高度
    return Math.max(400, viewportHeight - reservedHeight)
  })

  // 渲染单个文件项
  const renderItem = (obj: any, index: number) => {
    return <ListItem obj={obj} index={index} />
  }

  return (
    <Show
      when={objStore.state !== State.FetchingObjs}
      fallback={
        <Show when={isVirtualRoot()}>
          <VirtualStorageSkeleton />
        </Show>
      }
    >
      <Show
        when={shouldUseVirtualScroll()}
        fallback={
          // 普通列表渲染（文件数量较少时）
          <VStack
            onDragOver={onDragOver}
            oncapture:contextmenu={captureContentMenu}
            class="list viselect-container"
            w="100%"
            maxW="100%"
            minW="0"
            spacing="$2"
            pt="0"
            px="$4"
            pb="$2"
            bgColor="transparent"
            css={{
              boxSizing: "border-box",
              overflow: "visible",
              width: "100% !important",
              maxWidth: "100% !important",
            }}
          >
            <For each={objStore.objs}>
              {(obj, i) => {
                return <ListItem obj={obj} index={i()} />
              }}
            </For>
          </VStack>
        }
      >
        {/* 虚拟滚动列表（文件数量较多时） */}
        <Box
          onDragOver={onDragOver}
          oncapture:contextmenu={captureContentMenu}
          class="list viselect-container"
          w="100%"
          maxW="100%"
          minW="0"
          pt="0"
          px="$4"
          pb="$2"
          bgColor="transparent"
          css={{
            boxSizing: "border-box",
            width: "100% !important",
            maxWidth: "100% !important",
          }}
        >
          <StableVirtualList
            items={objStore.objs}
            itemHeight={65} // 每个列表项的高度（稍微增加一点）
            containerHeight={containerHeight()}
            renderItem={renderItem}
            overscan={3} // 减少预渲染项目数，提高稳定性
            onScroll={(scrollTop) => {
              // 可以在这里添加滚动事件处理
              console.log('虚拟滚动位置:', scrollTop)
            }}
          />
        </Box>
      </Show>
    </Show>
  )
}

export default ListLayout
