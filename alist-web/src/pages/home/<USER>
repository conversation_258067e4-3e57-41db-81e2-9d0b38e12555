import { VStack, Heading, Text, Box } from "@hope-ui/solid"
import { useT } from "~/hooks"
import { Container } from "./Container"

/**
 * 分享文件页面 - 演示如何复用主布局
 * 这个页面使用相同的导航栏和侧边栏，但显示不同的内容
 */
export const SharedFiles = () => {
  const t = useT()

  return (
    <Container>
      <VStack
        class="shared-files"
        py="$2"
        px="$4"
        minH="80vh"
        w="$full"
        gap="$4"
        minW="0"
      >
        <Box w="$full">
          <Heading size="lg" mb="$4">
            {t("home.sidemenu.shared_files")}
          </Heading>

          <VStack spacing="$4" align="stretch">
            <Box
              p="$4"
              border="1px solid"
              borderColor="$neutral6"
              rounded="$lg"
              bgColor="$neutral2"
            >
              <Text fontSize="$lg" fontWeight="$semibold" mb="$2">
                共享文件夹 1
              </Text>
              <Text color="$neutral11">
                包含 25 个文件，最后更新：2024-12-20
              </Text>
            </Box>

            <Box
              p="$4"
              border="1px solid"
              borderColor="$neutral6"
              rounded="$lg"
              bgColor="$neutral2"
            >
              <Text fontSize="$lg" fontWeight="$semibold" mb="$2">
                共享文件夹 2
              </Text>
              <Text color="$neutral11">
                包含 12 个文件，最后更新：2024-12-19
              </Text>
            </Box>

            <Box
              p="$4"
              border="1px solid"
              borderColor="$neutral6"
              rounded="$lg"
              bgColor="$neutral2"
            >
              <Text fontSize="$lg" fontWeight="$semibold" mb="$2">
                共享文件夹 3
              </Text>
              <Text color="$neutral11">
                包含 8 个文件，最后更新：2024-12-18
              </Text>
            </Box>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
