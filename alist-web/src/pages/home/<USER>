import { VStack } from "@hope-ui/solid"
import { Obj } from "./Obj"
import { Readme } from "./Readme"
import { Container } from "./Container"
import { Sidebar } from "./Sidebar"
import { Suspense, lazy, createMemo } from "solid-js"
import { objStore } from "~/store"

// 懒加载非关键组件
const LazySidebar = lazy(() => import("./Sidebar").then(m => ({ default: m.Sidebar })))

/**
 * 文件列表页面的主体内容组件
 * 专注于内容展示，不处理布局逻辑
 * 优化：使用懒加载和条件渲染提升性能
 */
export const Body = () => {
  // 只有在需要时才渲染Readme组件
  const shouldRenderReadme = createMemo(() => {
    return objStore.readme || objStore.header
  })

  return (
    <Container>
      <VStack
        class="body"
        pt="0" // 取消上边内边距
        px="0" // 取消左右内边距
        pb="50px" // 下边50px内边距
        minH="60vh"
        w="$full"
        maxW="100%" // 防止超出容器宽度
        gap="$4"
        minW="0" // 防止内容溢出
        overflowX="visible" // 允许内容正常显示
        alignItems="stretch" // 让子元素占满容器宽度
        justifyContent="stretch" // 确保内容占满宽度
      >
        {/* 条件渲染Readme组件 */}
        {shouldRenderReadme() && (
          <Readme files={["header.md", "top.md", "index.md"]} fromMeta="header" />
        )}

        {/* 主要内容区域 - 优先加载 */}
        <Obj />

        {/* 条件渲染底部Readme */}
        {shouldRenderReadme() && (
          <Readme
            files={["readme.md", "footer.md", "bottom.md"]}
            fromMeta="readme"
          />
        )}

        {/* 懒加载侧边栏 */}
        <Suspense fallback={null}>
          <LazySidebar />
        </Suspense>
      </VStack>
    </Container>
  )
}
