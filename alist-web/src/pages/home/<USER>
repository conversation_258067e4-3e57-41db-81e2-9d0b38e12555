import { Markdown, FilePreviewModal, PerformanceMonitor } from "~/components"
import { useTitle } from "~/hooks"
import { getSetting } from "~/store"
import { notify } from "~/utils"
import { MainLayout } from "./MainLayout"
import { Body } from "./Body"
import { Footer } from "./Footer"
import { Toolbar } from "./toolbar/Toolbar"
import { Suspense, lazy } from "solid-js"
import { FullLoading } from "~/components"

// 懒加载非关键组件
const LazyFooter = lazy(() => import("./Footer").then(m => ({ default: m.Footer })))
const LazyFilePreviewModal = lazy(() => import("~/components").then(m => ({ default: m.FilePreviewModal })))
const LazyPerformanceMonitor = lazy(() => import("~/components").then(m => ({ default: m.PerformanceMonitor })))

const Index = () => {
  useTitle(getSetting("site_title"))
  const announcement = getSetting("announcement")
  if (announcement) {
    notify.render(() => <Markdown children={announcement} />)
  }

  return (
    <MainLayout>
      <Toolbar />
      <Body />
      <Suspense fallback={null}>
        <LazyFooter />
      </Suspense>
      <Suspense fallback={null}>
        <LazyFilePreviewModal />
      </Suspense>
      {/* 性能监控组件 - 开发调试用 */}
      <Suspense fallback={null}>
        <LazyPerformanceMonitor />
      </Suspense>
    </MainLayout>
  )
}

export default Index
