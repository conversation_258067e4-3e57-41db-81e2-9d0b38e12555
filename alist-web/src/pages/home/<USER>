import { Box, useColorModeValue, VStack } from "@hope-ui/solid"
import { Show, createMemo, lazy, Suspense } from "solid-js"
import { SideMenu } from "~/pages/manage/SideMenu"
import { home_side_menu_items } from "./sidemenu_items"
import { local, me } from "~/store"
import { UserMethods } from "~/types"

// 懒加载容量信息组件，只在需要时才加载
const UserCapacityInfo = lazy(() => import("~/components/UserCapacityInfo").then(m => ({ default: m.UserCapacityInfo })))

export const HomeSidebar = () => {
  // 检查是否显示侧边栏
  const showSidebar = createMemo(() => {
    return local["show_home_sidebar"] !== "none"
  })

  // 检查是否需要显示容量信息 - 只有登录用户且有容量限制时才显示
  const shouldShowCapacityInfo = createMemo(() => {
    const user = me()
    if (!user || !user.id) return false

    // 检查是否有启用容量限制的基础路径
    const basePaths = UserMethods.get_base_paths(user)
    return basePaths.some(bp => bp.enable_capacity_limit && bp.total_bytes > 0)
  })

  return (
    <Show when={showSidebar()}>
      <Box
        w="240px"
        h="calc(100vh - 64px)" // 与导航栏高度保持一致
        pos="fixed"
        left="0"
        top="64px" // 与导航栏高度保持一致
        zIndex="$docked"
        bgColor={useColorModeValue("$neutral1", "#1e2a4a")()}
        borderRight="1px solid"
        borderColor={useColorModeValue("$neutral4", "#1e3a8a")()}
        display={{ "@initial": "none", "@md": "block" }}
      >
        <VStack spacing="$0" h="$full" justifyContent="space-between">
          {/* 菜单区域 - 可滚动 */}
          <Box
            flex="1"
            overflow="auto"
            w="$full"
          >
            <SideMenu items={home_side_menu_items} />
          </Box>

          {/* 容量信息区域 - 固定在底部，只在需要时才加载 */}
          <Show when={shouldShowCapacityInfo()}>
            <Suspense fallback={null}>
              <UserCapacityInfo />
            </Suspense>
          </Show>
        </VStack>
      </Box>
    </Show>
  )
}
