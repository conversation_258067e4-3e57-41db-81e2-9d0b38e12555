import { JSXElement, createMemo } from "solid-js"
import { Box } from "@hope-ui/solid"
import { local } from "~/store"

interface ContentAreaProps {
  children: JSXElement
}

/**
 * 内容区域组件 - 处理侧边栏的布局逻辑
 * 自动适应侧边栏的显示/隐藏状态
 */
export const ContentArea = (props: ContentAreaProps) => {
  // 检查是否显示侧边栏
  const showSidebar = createMemo(() => {
    return local["show_home_sidebar"] !== "none"
  })

  return (
    <Box
      class="content-area-scrollbar"
      // 根据侧边栏状态调整左边距
      ml={{ "@initial": "0", "@md": showSidebar() ? "240px" : "0" }}
      // 确保内容区域宽度正确计算，避免超出可视区域
      w={{
        "@initial": "100%",
        "@md": showSidebar() ? "calc(100vw - 240px)" : "100%",
      }}
      maxW={{
        "@initial": "100%",
        "@md": showSidebar() ? "calc(100vw - 240px)" : "100%",
      }}
      minW="0" // 防止flex子元素溢出
      // 隐藏超出内容，确保不会显示在可变内容区域外
      overflowX="hidden"
      overflowY="auto"
      // 平滑过渡动画
      transition="margin-left 0.3s ease, width 0.3s ease"
      // 高度设置为从工具栏下方到页面底部
      h="calc(100vh - 198px)" // 减去导航栏高度(64px) + 路径导航栏高度(60px) + 工具导航栏高度(74px)
      // 顶部边距，避免与固定导航栏重叠
      mt="198px" // 导航栏(64px) + 路径导航栏(60px) + 工具导航栏(74px) = 198px
      // 内容布局
      display="flex"
      flexDirection="column"
      justifyContent="flex-start"
      alignItems="stretch"
      // 确保内容紧贴左边，不居中
      css={{
        boxSizing: "border-box",
        "& > *": {
          width: "100%",
          maxWidth: "100%",
          boxSizing: "border-box",
        },
        "& .viselect-container": {
          width: "100%",
          maxWidth: "100%",
          boxSizing: "border-box",
        },
      }}
    >
      {props.children}
    </Box>
  )
}
