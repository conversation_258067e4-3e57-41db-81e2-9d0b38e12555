import { JSXElement, Match, Switch, createMemo } from "solid-js"
import { getSetting, local } from "~/store"
import { Box, Container as <PERSON><PERSON>ontaine<PERSON> } from "@hope-ui/solid"

export const Container = (props: { children: JSXElement }) => {
  const container = getSetting("home_container")

  // 检查是否显示侧边栏，动态调整容器宽度
  const showSidebar = createMemo(() => {
    return local["show_home_sidebar"] !== "none"
  })

  return (
    <Switch
      fallback={
        <Box
          w="100%"
          // 确保容器占满可用宽度
          maxW="100%"
          // 确保容器紧贴左边，不居中
          ml="0"
          mr="auto"
          css={{
            // 确保容器能够自适应宽度
            display: "block",
            boxSizing: "border-box",
            width: "100%",
            maxWidth: "100%",
          }}
        >
          {props.children}
        </Box>
      }
    >
      <Match when={container === "hope_container"}>
        <HopeContainer>{props.children}</HopeContainer>
      </Match>
    </Switch>
  )
}
