import { JSXElement } from "solid-js"
import { UnifiedHeader } from "~/components"
import { HomeSidebar } from "./HomeSidebar"
import { ContentArea } from "./ContentArea"
import { Nav } from "./Nav"
import { ToolNavbar } from "./ToolNavbar"

interface MainLayoutProps {
  children: JSXElement
}

/**
 * 主布局组件 - 包含公共的导航栏和侧边栏
 * 内容区域通过 children 传入，实现布局与内容的分离
 */
export const MainLayout = (props: MainLayoutProps) => {
  return (
    <>
      {/* 统一导航栏 - 公共组件 */}
      <UnifiedHeader />

      {/* 路径导航栏 - 公共组件 */}
      <Nav />

      {/* 工具导航栏 - 公共组件 */}
      <ToolNavbar />

      {/* 侧边栏 - 公共组件 */}
      <HomeSidebar />

      {/* 内容区域 - 可变内容 */}
      <ContentArea>{props.children}</ContentArea>
    </>
  )
}
