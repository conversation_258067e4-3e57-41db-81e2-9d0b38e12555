import {
  Box,
  HStack,
  VStack,
  Icon,
  IconButton,
  Menu,
  MenuContent,
  MenuItem,
  MenuTrigger,
  Tooltip,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Button,
  Text,
  createDisclosure,
} from "@hope-ui/solid"
import { createMemo, Show } from "solid-js"
import { useT, useDownload, usePath, useRouter } from "~/hooks"
import {
  allChecked,
  checkboxOpen,
  isIndeterminate,
  layout,
  local,
  objStore,
  selectAll,
  setLayout,
  sortObjs,
  State,
  toggleCheckbox,
  userCan,
  haveSelected,
  getSettingBool,
  me,
  oneChecked,
  selectedObjs,
} from "~/store"
import { UserMethods } from "~/types"
import { bus, hoverColor, notify } from "~/utils"
import { ItemCheckbox } from "./folder/helper"
import { operations } from "./toolbar/operations"
import { isArchive } from "~/store/archive"

// 图标导入
import { FaSolidListUl } from "solid-icons/fa"
import { BsGridFill, BsSortDown, BsSearch } from "solid-icons/bs"
import { AiOutlineCloudUpload, AiOutlineCloudDownload } from "solid-icons/ai"
import { CgFolderAdd, CgRename } from "solid-icons/cg"
import { TbCopy, TbFileArrowRight, TbArchive } from "solid-icons/tb"
import { AiTwotoneDelete } from "solid-icons/ai"
import { RiSystemRefreshLine } from "solid-icons/ri"
import { BiRegularRename } from "solid-icons/bi"

export const ToolNavbar = () => {
  const t = useT()
  const { refresh } = usePath()
  const showSidebar = createMemo(() => {
    return local["show_home_sidebar"] !== "none"
  })

  const isFolder = createMemo(() => objStore.state === State.Folder)

  // 工具按钮组件
  const ToolButton = (props: {
    icon: any
    tooltip: string
    onClick: () => void
    disabled?: boolean
    iconSize?: string
    iconColor?: string
  }) => (
    <Tooltip label={t(`home.toolbar.${props.tooltip}`)} placement="bottom">
      <IconButton
        aria-label={props.tooltip}
        icon={
          <Icon
            as={props.icon}
            boxSize={props.iconSize || "$5"}
            color={props.iconColor || (props.disabled ? "#4b5563" : "#9ca3af")}
          />
        }
        size="md"
        variant="ghost"
        color={props.disabled ? "#4b5563" : "#9ca3af"}
        border={`1px solid ${props.disabled ? "#374151" : "#1e3a8a"}`}
        rounded="$md"
        disabled={props.disabled}
        transition="all 0.2s ease"
        _hover={
          props.disabled
            ? {}
            : {
                bgColor: "#1e3a8a",
                transform: "scale(1.08)",
                color: "#ffffff",
              }
        }
        onClick={props.onClick}
      />
    </Tooltip>
  )

  // 带文字和边框的按钮组件
  const TextButton = (props: {
    icon: any
    text: string
    tooltip: string
    onClick: () => void
    disabled?: boolean
    bgColor?: string
    iconColor?: string
    fontWeight?: string
  }) => (
    <Tooltip label={t(`home.toolbar.${props.tooltip}`)} placement="bottom">
      <HStack
        spacing="$2"
        px="$3"
        py="$2"
        border="1px solid #1e3a8a"
        rounded="$md"
        cursor="pointer"
        color="#ffffff"
        bgColor={props.bgColor || "transparent"}
        transition="all 0.2s ease"
        _hover={{
          bgColor: "#1e3a8a",
          transform: "scale(1.08)",
        }}
        onClick={props.onClick}
      >
        <Icon
          as={props.icon}
          boxSize="$5"
          color={props.iconColor || "#ffffff"}
        />
        <span style={{ "font-weight": props.fontWeight || "normal" }}>
          {props.text}
        </span>
      </HStack>
    </Tooltip>
  )

  // 智能下载按钮组件
  const DownloadButton = () => {
    const { batchDownloadSelected } = useDownload()
    const { isOpen, onOpen, onClose } = createDisclosure()

    // 检查是否支持File System Access API
    const isDirectDownloadSupported = () => {
      return 'showDirectoryPicker' in window;
    };

    // 获取浏览器信息
    const getBrowserInfo = () => {
      const userAgent = navigator.userAgent;
      if (userAgent.includes('Chrome')) {
        const match = userAgent.match(/Chrome\/(\d+)/);
        return { name: 'Chrome', version: match ? parseInt(match[1]) : 0 };
      } else if (userAgent.includes('Edg')) {
        const match = userAgent.match(/Edg\/(\d+)/);
        return { name: 'Edge', version: match ? parseInt(match[1]) : 0 };
      } else if (userAgent.includes('Firefox')) {
        return { name: 'Firefox', version: 0 };
      } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        return { name: 'Safari', version: 0 };
      }
      return { name: 'Unknown', version: 0 };
    };

    // 检查是否需要多文件/文件夹下载
    const needsBatchDownload = (selected: any[]) => {
      return selected.length > 1 || selected.some(obj => obj.is_dir);
    };

    // 智能下载逻辑
    const handleDownloadClick = () => {
      const selected = selectedObjs();

      // 如果没有选中任何文件
      if (selected.length === 0) {
        return;
      }

      // 如果只选中了一个文件（非文件夹），使用原逻辑
      if (selected.length === 1 && !selected[0].is_dir) {
        batchDownloadSelected();
        return;
      }

      // 如果选中了多个文件，或者包含文件夹，检查浏览器支持
      if (needsBatchDownload(selected)) {
        if (isDirectDownloadSupported()) {
          bus.emit("tool", "direct_folder_download");
        } else {
          // 不支持时显示提示模态窗口
          onOpen();
        }
      }
    };

    // 浏览器不支持提示模态窗口
    const BrowserSupportModal = () => {
      const browserInfo = getBrowserInfo();

      return (
        <Modal opened={isOpen()} onClose={onClose} size="md">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>浏览器不支持批量下载</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Alert status="warning" mb="$4">
                <AlertIcon />
                <AlertTitle>当前浏览器不支持批量下载功能</AlertTitle>
              </Alert>

              <Box>
                <Text fontWeight="$semibold" mb="$2">支持的浏览器版本：</Text>
                <VStack spacing="$2" alignItems="start">
                  <Text>• Chrome 86 或更高版本</Text>
                  <Text>• Microsoft Edge 86 或更高版本</Text>
                </VStack>

                <Text mt="$4" fontSize="$sm" color="$neutral11">
                  请更新到支持的浏览器版本以使用批量下载功能。
                  或者您可以选择单个文件进行下载。
                </Text>
              </Box>
            </ModalBody>
            <ModalFooter>
              <Button colorScheme="primary" onClick={onClose}>
                我知道了
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      );
    };

    return (
      <>
        <Tooltip label={t("home.toolbar.download")} placement="bottom">
          <IconButton
            aria-label="download"
            icon={
              <Icon
                as={AiOutlineCloudDownload}
                boxSize="$6"
                color={haveSelected() ? "#3b82f6" : "#1e3a8a"}
              />
            }
            size="md"
            variant="ghost"
            color={haveSelected() ? "#9ca3af" : "#1e3a8a"}
            border={`1px solid ${haveSelected() ? "#1e3a8a" : "#374151"}`}
            rounded="$md"
            disabled={!haveSelected()}
            transition="all 0.2s ease"
            _hover={
              haveSelected()
                ? {
                    bgColor: "#1e3a8a",
                    transform: "scale(1.08)",
                    color: "#ffffff",
                  }
                : {}
            }
            onClick={handleDownloadClick}
          />
        </Tooltip>
        <BrowserSupportModal />
      </>
    )
  }

  // 直接上传按钮组件
  const DirectUploadButton = () => {
    const { pathname } = useRouter();

    // 处理文件选择
    const handleFileSelect = async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      // 检查普通用户是否在根目录
      const user = me();
      const path = pathname();
      if (!UserMethods.is_admin(user) && (path === "/" || path === "")) {
        notify.error("当前目录不支持创建文件！");
        return;
      }

      // 转换为数组并触发上传
      const fileArray = Array.from(files);
      console.log(`选择了 ${fileArray.length} 个文件进行上传`);

      // 触发上传事件，传递文件列表
      bus.emit("upload_files", fileArray);
    };

    // 选择文件
    const selectFiles = () => {
      const input = document.createElement('input');
      input.type = 'file';
      input.multiple = true;
      input.webkitdirectory = false;

      input.onchange = (e) => {
        const target = e.target as HTMLInputElement;
        handleFileSelect(target.files);
      };

      input.click();
    };

    // 选择文件夹
    const selectFolder = () => {
      const input = document.createElement('input');
      input.type = 'file';
      input.webkitdirectory = true;
      input.multiple = false;

      input.onchange = (e) => {
        const target = e.target as HTMLInputElement;
        handleFileSelect(target.files);
      };

      input.click();
    };

    // 点击上传按钮 - 直接选择文件
    const handleUploadClick = () => {
      selectFiles();
    };

    return (
      <TextButton
        icon={AiOutlineCloudUpload}
        text={t("home.toolbar.upload")}
        tooltip="左键：选择文件 | 右键：选择文件夹"
        bgColor="#3b82f6"
        fontWeight="bold"
        onClick={handleUploadClick}
        onContextMenu={(e) => {
          e.preventDefault();
          selectFolder();
        }}
      />
    );
  };

  // 视图切换按钮
  const ViewToggleButton = () => (
    <Tooltip
      label={
        layout() === "list" ? t("home.layouts.grid") : t("home.layouts.list")
      }
      placement="bottom"
    >
      <IconButton
        aria-label="toggle-view"
        icon={
          <Icon
            as={layout() === "list" ? BsGridFill : FaSolidListUl}
            boxSize="$5"
          />
        }
        size="md"
        variant="ghost"
        color="#9ca3af"
        border="1px solid #1e3a8a"
        rounded="$md"
        transition="all 0.2s ease"
        _hover={{
          bgColor: "#1e3a8a",
          transform: "scale(1.08)",
          color: "#ffffff",
        }}
        onClick={() => setLayout(layout() === "list" ? "grid" : "list")}
      />
    </Tooltip>
  )

  return (
    <Box
      position="fixed"
      top="124px" // 路径导航栏下方 (64px + 60px)
      left={{ "@initial": "0", "@md": showSidebar() ? "240px" : "0" }}
      right="0"
      zIndex="$sticky"
      bgColor={useColorModeValue("$neutral1", "#1e2a4a")()}
      borderBottom="1px solid"
      borderColor={useColorModeValue("$neutral4", "#1e3a8a")()}
      h="74px"
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      px="$4"
      transition="left 0.3s ease"
      class="tool-navbar"
    >
      {/* 左侧工具 */}
      <HStack spacing="$2" ml="8px">
        {/* 全选复选框 */}
        <Show when={isFolder()}>
          <Tooltip label={t("home.toolbar.select_all")} placement="bottom">
            <Box
              w="$10"
              h="$10"
              cursor="pointer"
              bgColor="transparent"
              display="flex"
              justifyContent="center"
              alignItems="center"
              transition="all 0.2s ease"
              mr="-$3"
              _hover={{
                transform: "scale(1.08)",
              }}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                if (!checkboxOpen()) {
                  toggleCheckbox()
                  // 如果复选框刚被打开，等待下一个tick再执行全选
                  setTimeout(() => selectAll(true), 10)
                } else {
                  // 直接切换全选状态
                  const currentlyAllChecked =
                    objStore.objs.length > 0 &&
                    objStore.objs.every((obj) => obj.selected)
                  selectAll(!currentlyAllChecked)
                }
              }}
            >
              <ItemCheckbox
                checked={(() => {
                  if (!checkboxOpen()) return false
                  return (
                    objStore.objs.length > 0 &&
                    objStore.objs.every((obj) => obj.selected)
                  )
                })()}
                indeterminate={(() => {
                  if (!checkboxOpen()) return false
                  const selectedCount = objStore.objs.filter(
                    (obj) => obj.selected,
                  ).length
                  return (
                    selectedCount > 0 && selectedCount < objStore.objs.length
                  )
                })()}
                onChange={(e: any) => {
                  e.stopPropagation()
                  selectAll(e.target.checked as boolean)
                }}
              />
            </Box>
          </Tooltip>
        </Show>

        {/* 上传 */}
        <Show when={isFolder() && (userCan("write") || objStore.write)}>
          <DirectUploadButton />
        </Show>

        {/* 新建文件夹 */}
        <Show when={isFolder() && (userCan("write") || objStore.write)}>
          <TextButton
            icon={CgFolderAdd}
            text={t("home.toolbar.mkdir")}
            tooltip="mkdir"
            fontWeight="bold"
            onClick={() => bus.emit("tool", "mkdir")}
          />
        </Show>

        {/* 刷新 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={RiSystemRefreshLine}
            tooltip="refresh"
            iconSize="$6"
            iconColor="#22c55e"
            onClick={() => refresh(undefined, true)}
          />
        </Show>

        {/* 批量重命名 - 已隐藏 */}
        {/* <Show when={isFolder()}>
          <ToolButton
            icon={BiRegularRename}
            tooltip="batch_rename"
            iconSize="$6"
            iconColor={haveSelected() ? "#a855f7" : "#581c87"}
            disabled={!haveSelected()}
            onClick={() => {
              bus.emit("tool", "batchRename")
            }}
          />
        </Show> */}

        {/* 复制按钮已隐藏 */}

        {/* 移动 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={TbFileArrowRight}
            tooltip="move"
            iconSize="$6"
            iconColor={haveSelected() ? "#eab308" : "#78350f"}
            disabled={!haveSelected()}
            onClick={() => bus.emit("tool", "move")}
          />
        </Show>

        {/* 下载 */}
        <Show when={isFolder()}>
          <DownloadButton />
        </Show>

        {/* 删除 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={AiTwotoneDelete}
            tooltip="delete"
            iconSize="$6"
            iconColor={haveSelected() ? "#ef4444" : "#450a0a"}
            disabled={!haveSelected()}
            onClick={() => bus.emit("tool", "delete")}
          />
        </Show>

        {/* 解压 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={TbArchive}
            tooltip="decompress"
            iconSize="$6"
            iconColor={
              haveSelected() &&
              selectedObjs().every((obj) => !obj.is_dir && isArchive(obj.name))
                ? "#f97316"
                : "#7c2d12"
            }
            disabled={
              !haveSelected() ||
              !selectedObjs().every((obj) => !obj.is_dir && isArchive(obj.name))
            }
            onClick={() => bus.emit("tool", "decompress")}
          />
        </Show>

        {/* 选中文件统计 */}
        <Show when={isFolder() && haveSelected()}>
          <Box
            fontSize="$sm"
            px="$3"
            py="$2"
            rounded="$md"
            whiteSpace="nowrap"
            display="flex"
            alignItems="center"
            fontWeight="bold"
          >
            <Box as="span" color="#ffffff" mx="$1">
              {t("home.toolbar.selected_count", {
                folders: (() => {
                  const selected = selectedObjs()
                  return selected.filter((obj) => obj.is_dir).length
                })(),
                files: (() => {
                  const selected = selectedObjs()
                  return selected.filter((obj) => !obj.is_dir).length
                })(),
              })}
            </Box>
          </Box>
        </Show>
      </HStack>

      {/* 右侧工具 */}
      <HStack spacing="$2">
        {/* 搜索 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={BsSearch}
            tooltip="search"
            onClick={() => bus.emit("tool", "search")}
          />
        </Show>

        {/* 视图切换 */}
        <Show when={isFolder()}>
          <ViewToggleButton />
        </Show>

        {/* 排序菜单 */}
        <Show when={isFolder()}>
          <Menu placement="bottom-end">
            <MenuTrigger
              as={IconButton}
              aria-label="sort"
              icon={<Icon as={BsSortDown} boxSize="$6" />}
              size="md"
              variant="ghost"
              color="#9ca3af"
              border="1px solid #1e3a8a"
              rounded="$md"
              transition="all 0.2s ease"
              _hover={{
                bgColor: "#1e3a8a",
                transform: "scale(1.08)",
                color: "#ffffff",
              }}
            />
            <MenuContent>
              <MenuItem onSelect={() => sortObjs("name")}>
                {t("home.obj.name")}
              </MenuItem>
              <MenuItem onSelect={() => sortObjs("size")}>
                {t("home.obj.size")}
              </MenuItem>
              <MenuItem onSelect={() => sortObjs("modified")}>
                {t("home.obj.modified")}
              </MenuItem>
              <MenuItem onSelect={() => sortObjs("type")}>
                {t("home.obj.type")}
              </MenuItem>
            </MenuContent>
          </Menu>
        </Show>
      </HStack>
    </Box>
  )
}
