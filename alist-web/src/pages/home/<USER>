import {
  VStack,
  Heading,
  Text,
  Box,
  HStack,
  Progress,
  ProgressIndicator,
} from "@hope-ui/solid"
import { useT } from "~/hooks"
import { Container } from "./Container"

/**
 * 我的存储页面 - 演示如何复用主布局
 * 显示存储使用情况和管理功能
 */
export const MyStorage = () => {
  const t = useT()

  return (
    <Container>
      <VStack
        class="my-storage"
        py="$2"
        px="$4"
        minH="80vh"
        w="$full"
        gap="$4"
        minW="0"
      >
        <Box w="$full">
          <Heading size="lg" mb="$4">
            {t("home.sidemenu.my_storage")}
          </Heading>

          <VStack spacing="$4" align="stretch">
            {/* 存储概览 */}
            <Box
              p="$4"
              border="1px solid"
              borderColor="$neutral6"
              rounded="$lg"
              bgColor="$neutral2"
            >
              <Text fontSize="$lg" fontWeight="$semibold" mb="$3">
                存储概览
              </Text>

              <VStack spacing="$3">
                <HStack justify="space-between" w="$full">
                  <Text>已使用空间</Text>
                  <Text fontWeight="$semibold">2.5 GB / 10 GB</Text>
                </HStack>

                <Progress value={25} w="$full">
                  <ProgressIndicator />
                </Progress>

                <HStack
                  justify="space-between"
                  w="$full"
                  fontSize="$sm"
                  color="$neutral11"
                >
                  <Text>剩余空间：7.5 GB</Text>
                  <Text>使用率：25%</Text>
                </HStack>
              </VStack>
            </Box>

            {/* 存储驱动列表 */}
            <Box
              p="$4"
              border="1px solid"
              borderColor="$neutral6"
              rounded="$lg"
              bgColor="$neutral2"
            >
              <Text fontSize="$lg" fontWeight="$semibold" mb="$3">
                存储驱动
              </Text>

              <VStack spacing="$3">
                <HStack
                  justify="space-between"
                  w="$full"
                  p="$2"
                  bgColor="$neutral3"
                  rounded="$md"
                >
                  <VStack align="start" spacing="$1">
                    <Text fontWeight="$semibold">本地存储</Text>
                    <Text fontSize="$sm" color="$neutral11">
                      本地文件系统
                    </Text>
                  </VStack>
                  <Text color="$success9" fontWeight="$semibold">
                    已连接
                  </Text>
                </HStack>

                <HStack
                  justify="space-between"
                  w="$full"
                  p="$2"
                  bgColor="$neutral3"
                  rounded="$md"
                >
                  <VStack align="start" spacing="$1">
                    <Text fontWeight="$semibold">百度网盘</Text>
                    <Text fontSize="$sm" color="$neutral11">
                      云存储服务
                    </Text>
                  </VStack>
                  <Text color="$success9" fontWeight="$semibold">
                    已连接
                  </Text>
                </HStack>

                <HStack
                  justify="space-between"
                  w="$full"
                  p="$2"
                  bgColor="$neutral3"
                  rounded="$md"
                >
                  <VStack align="start" spacing="$1">
                    <Text fontWeight="$semibold">阿里云盘</Text>
                    <Text fontSize="$sm" color="$neutral11">
                      云存储服务
                    </Text>
                  </VStack>
                  <Text color="$warning9" fontWeight="$semibold">
                    未配置
                  </Text>
                </HStack>
              </VStack>
            </Box>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
