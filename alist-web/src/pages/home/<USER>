import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VStack } from "@hope-ui/solid"
import { <PERSON> } from "@solidjs/router"
import { AnchorWithBase } from "~/components"
import { useT } from "~/hooks"
import { me } from "~/store"
import { UserMethods } from "~/types"

export const Footer = () => {
  const t = useT()
  return (
    <VStack
      class="footer"
      w="$full"
      py="$4"
      mt="auto"
      justifyContent="center"
      alignItems="center"
      bgColor="transparent"
      position="relative"
      zIndex="1"
    >
      <HStack spacing="$1">
        <Anchor href="https://github.com/alist-org/alist" external>
          {t("home.footer.powered_by")}
        </Anchor>
        <span>|</span>
        <AnchorWithBase
          as={Link}
          href={UserMethods.is_guest(me()) ? "/@login" : "/@manage"}
        >
          {t(UserMethods.is_guest(me()) ? "login.login" : "home.footer.manage")}
        </AnchorWithBase>
      </HStack>
    </VStack>
  )
}
