import { createSign<PERSON>, For, Show } from "solid-js";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>dalBody,
  ModalFooter,
  Text,
  VStack,
  HStack,
  Box,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge,
  Divider,
  IconButton,
  Icon
} from "@hope-ui/solid";
import { fsList, pathJoin } from "~/utils";
import { password, selectedObjs as _selectedObjs } from "~/store";
import { getLinkByDirAndObj, useRouter, useT } from "~/hooks";
import { Obj } from "~/types";

// 图标导入
import { BsChevronLeft, BsChevronRight } from "solid-icons/bs";

interface DownloadFile {
  path: string;
  url: string;
  name: string;
  size: number;
}

interface FileDownloadStatus {
  file: DownloadFile;
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'paused' | 'retrying';
  downloaded: number;
  speed: number;
  error?: string;
  retryCount: number;
  lastError?: string;
  resumeSupported: boolean;
}

interface DownloadProgress {
  total: number;
  completed: number;
  current: string;
  totalSize: number;
  totalDownloaded: number;
  fileStatuses: FileDownloadStatus[];
  startTime: number;
  sessionId: string;
}

interface DownloadSession {
  sessionId: string;
  directoryPath: string;
  progress: DownloadProgress;
  timestamp: number;
}

const DirectFolderDownload = (props: { onClose: () => void }) => {
  const t = useT();
  const { pathname } = useRouter();
  const selectedObjs = _selectedObjs();
  
  const [progress, setProgress] = createSignal<DownloadProgress>({
    total: 0,
    completed: 0,
    current: '',
    totalSize: 0,
    totalDownloaded: 0,
    fileStatuses: [],
    startTime: 0,
    sessionId: ''
  });
  
  const [status, setStatus] = createSignal<'init' | 'selecting' | 'fetching' | 'ready' | 'downloading' | 'completed' | 'error' | 'cancelled'>('init');
  const [errorMessage, setErrorMessage] = createSignal('');
  const [cancelSignal, setCancelSignal] = createSignal(false);
  const [directoryHandle, setDirectoryHandle] = createSignal<FileSystemDirectoryHandle | null>(null);

  // 分页相关状态
  const [currentPage, setCurrentPage] = createSignal(0);
  const pageSize = 10; // 每页显示10个文件

  // 并发控制
  const maxConcurrency = 3; // 最大并发下载数

  // 断点续传和错误处理配置
  const maxRetries = 3; // 最大重试次数
  const retryDelay = 1000; // 重试延迟（毫秒）
  const chunkSize = 1024 * 1024; // 1MB 分块大小
  const sessionStorageKey = 'alist_download_session';

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化下载速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  // 计算剩余时间
  const formatETA = (remainingBytes: number, speed: number): string => {
    if (speed === 0) return '∞';
    const seconds = remainingBytes / speed;
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  // 并发队列控制类
  class ConcurrentQueue {
    private queue: (() => Promise<any>)[] = [];
    private running: Set<Promise<any>> = new Set();
    private maxConcurrency: number;

    constructor(maxConcurrency: number) {
      this.maxConcurrency = maxConcurrency;
    }

    async add<T>(task: () => Promise<T>): Promise<T> {
      return new Promise((resolve, reject) => {
        this.queue.push(async () => {
          try {
            const result = await task();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
        this.process();
      });
    }

    private async process() {
      if (this.running.size >= this.maxConcurrency || this.queue.length === 0) {
        return;
      }

      const task = this.queue.shift();
      if (!task) return;

      const promise = task();
      this.running.add(promise);

      try {
        await promise;
      } finally {
        this.running.delete(promise);
        this.process(); // 处理下一个任务
      }
    }

    clear() {
      this.queue = [];
    }

    get activeCount() {
      return this.running.size;
    }

    get pendingCount() {
      return this.queue.length;
    }
  }

  // 检查浏览器支持
  const isSupported = () => {
    return 'showDirectoryPicker' in window;
  };

  // 分页计算
  const totalPages = () => Math.ceil(progress().fileStatuses.length / pageSize);
  const visibleFiles = () => {
    const start = currentPage() * pageSize;
    const end = start + pageSize;
    return progress().fileStatuses.slice(start, end);
  };

  // 分页控制
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(0, Math.min(page, totalPages() - 1)));
  };

  const nextPage = () => goToPage(currentPage() + 1);
  const prevPage = () => goToPage(currentPage() - 1);

  // 会话管理
  const generateSessionId = () => `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const saveDownloadSession = (session: DownloadSession) => {
    try {
      localStorage.setItem(sessionStorageKey, JSON.stringify(session));
    } catch (error) {
      console.warn('Failed to save download session:', error);
    }
  };

  const loadDownloadSession = (): DownloadSession | null => {
    try {
      const saved = localStorage.getItem(sessionStorageKey);
      if (saved) {
        const session = JSON.parse(saved) as DownloadSession;
        // 检查会话是否过期（24小时）
        if (Date.now() - session.timestamp < 24 * 60 * 60 * 1000) {
          return session;
        }
      }
    } catch (error) {
      console.warn('Failed to load download session:', error);
    }
    return null;
  };

  const clearDownloadSession = () => {
    try {
      localStorage.removeItem(sessionStorageKey);
    } catch (error) {
      console.warn('Failed to clear download session:', error);
    }
  };

  // 递归获取文件夹结构（单个）
  const fetchFolderStructure = async (
    pre: string,
    obj: Obj
  ): Promise<DownloadFile[] | string> => {
    if (!obj.is_dir) {
      return [{
        path: pathJoin(pre, obj.name),
        url: getLinkByDirAndObj(
          pathJoin(pathname(), pre),
          obj,
          "direct",
          true
        ),
        name: obj.name,
        size: obj.size
      }];
    } else {
      const resp = await fsList(pathJoin(pathname(), pre, obj.name), password());
      if (resp.code !== 200) {
        return resp.message;
      }

      const res: DownloadFile[] = [];
      const subItems = resp.data.content ?? [];

      // 并行处理子项目
      const promises = subItems.map(async (_obj) => {
        return await fetchFolderStructure(pathJoin(pre, obj.name), _obj);
      });

      const results = await Promise.allSettled(promises);

      for (const result of results) {
        if (result.status === 'fulfilled') {
          const _res = result.value;
          if (typeof _res === "string") {
            return _res;
          } else {
            res.push(..._res);
          }
        } else {
          console.error('Failed to fetch folder structure:', result.reason);
          return `获取文件结构失败: ${result.reason}`;
        }
      }

      return res;
    }
  };

  // 并行获取多个根文件夹的结构
  const fetchAllFolderStructures = async (objects: Obj[]): Promise<DownloadFile[] | string> => {
    const promises = objects.map(async (obj) => {
      return await fetchFolderStructure('', obj);
    });

    const results = await Promise.allSettled(promises);
    const allFiles: DownloadFile[] = [];

    for (const result of results) {
      if (result.status === 'fulfilled') {
        const files = result.value;
        if (typeof files === "string") {
          return files;
        } else {
          allFiles.push(...files);
        }
      } else {
        console.error('Failed to fetch folder structure:', result.reason);
        return `获取文件结构失败: ${result.reason}`;
      }
    }

    return allFiles;
  };

  // 创建目录结构
  const createDirectoryStructure = async (
    dirHandle: FileSystemDirectoryHandle,
    path: string
  ): Promise<FileSystemDirectoryHandle> => {
    if (!path || path === '/') {
      return dirHandle;
    }
    
    const parts = path.split('/').filter(Boolean);
    let currentHandle = dirHandle;
    
    for (const part of parts) {
      try {
        currentHandle = await currentHandle.getDirectoryHandle(part, {
          create: true
        });
      } catch (error) {
        console.error(`Failed to create directory: ${part}`, error);
        throw new Error(`无法创建目录: ${part}`);
      }
    }
    
    return currentHandle;
  };

  // 更新文件状态
  const updateFileStatus = (
    file: DownloadFile,
    updates: Partial<FileDownloadStatus>
  ) => {
    setProgress(prev => ({
      ...prev,
      fileStatuses: prev.fileStatuses.map(fs =>
        fs.file.path === file.path
          ? { ...fs, ...updates }
          : fs
      )
    }));
  };

  // 检查服务器是否支持断点续传
  const checkResumeSupport = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: { 'Range': 'bytes=0-0' }
      });
      return response.status === 206 || response.headers.get('Accept-Ranges') === 'bytes';
    } catch (error) {
      console.warn('Failed to check resume support:', error);
      return false;
    }
  };

  // 获取已下载的文件大小
  const getDownloadedSize = async (
    dirHandle: FileSystemDirectoryHandle,
    filePath: string,
    fileName: string
  ): Promise<number> => {
    try {
      const pathParts = filePath.split('/').filter(Boolean);
      let currentHandle = dirHandle;

      // 导航到文件所在目录
      for (const part of pathParts) {
        try {
          currentHandle = await currentHandle.getDirectoryHandle(part);
        } catch {
          return 0; // 目录不存在，文件未开始下载
        }
      }

      // 检查文件是否存在
      try {
        const fileHandle = await currentHandle.getFileHandle(fileName);
        const file = await fileHandle.getFile();
        return file.size;
      } catch {
        return 0; // 文件不存在
      }
    } catch (error) {
      console.warn('Failed to get downloaded size:', error);
      return 0;
    }
  };

  // 下载并保存文件（支持断点续传和重试）
  const downloadAndSaveFile = async (
    dirHandle: FileSystemDirectoryHandle,
    file: DownloadFile
  ): Promise<boolean> => {
    const filePath = file.path;
    const lastSlashIndex = filePath.lastIndexOf('/');
    const dirPath = lastSlashIndex > 0 ? filePath.substring(0, lastSlashIndex) : '';
    const fileName = file.name;

    // 获取当前文件状态
    const currentStatus = progress().fileStatuses.find(fs => fs.file.path === file.path);
    let retryCount = currentStatus?.retryCount || 0;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // 检查取消信号
        if (cancelSignal()) {
          throw new Error('Download cancelled');
        }

        // 创建目录结构
        let targetDirHandle = dirHandle;
        if (dirPath) {
          targetDirHandle = await createDirectoryStructure(dirHandle, dirPath);
        }

        // 检查已下载的大小（断点续传）
        const downloadedSize = await getDownloadedSize(targetDirHandle, dirPath, fileName);
        const resumeSupported = downloadedSize > 0 ? await checkResumeSupport(file.url) : false;

        // 更新文件状态
        if (attempt > 0) {
          updateFileStatus(file, {
            status: 'retrying',
            retryCount: attempt,
            lastError: `重试第 ${attempt} 次`
          });
          // 等待重试延迟
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        } else {
          updateFileStatus(file, {
            status: 'downloading',
            downloaded: downloadedSize,
            resumeSupported: resumeSupported
          });
        }

        setProgress(prev => ({ ...prev, current: fileName }));

        // 准备请求头（断点续传）
        const headers: Record<string, string> = {};
        if (resumeSupported && downloadedSize > 0) {
          headers['Range'] = `bytes=${downloadedSize}-`;
        }

        // 下载文件
        const response = await fetch(file.url, { headers });

        if (!response.ok && response.status !== 206) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 创建或打开文件
        const fileHandle = await targetDirHandle.getFileHandle(fileName, {
          create: true
        });

        // 如果是断点续传，追加写入；否则覆盖写入
        const writable = await fileHandle.createWritable({
          keepExistingData: resumeSupported && downloadedSize > 0
        });

        if (resumeSupported && downloadedSize > 0) {
          await writable.seek(downloadedSize);
        }

        // 流式写入并跟踪进度
        if (response.body) {
          const reader = response.body.getReader();
          let currentDownloaded = downloadedSize;
          const startTime = Date.now();
          let lastUpdateTime = startTime;

          try {
            while (true) {
              if (cancelSignal()) {
                await reader.cancel();
                await writable.abort();
                throw new Error('Download cancelled');
              }

              const { done, value } = await reader.read();
              if (done) break;

              currentDownloaded += value.length;
              const currentTime = Date.now();

              // 限制更新频率（每100ms更新一次）
              if (currentTime - lastUpdateTime > 100) {
                const elapsedSeconds = (currentTime - startTime) / 1000;
                const speed = elapsedSeconds > 0 ? (currentDownloaded - downloadedSize) / elapsedSeconds : 0;

                // 更新文件下载进度
                updateFileStatus(file, {
                  status: 'downloading',
                  downloaded: currentDownloaded,
                  speed: speed
                });

                // 更新总体进度
                setProgress(prev => ({
                  ...prev,
                  totalDownloaded: prev.fileStatuses.reduce((sum, fs) => sum + fs.downloaded, 0)
                }));

                lastUpdateTime = currentTime;
              }

              await writable.write(value);
            }
          } finally {
            await writable.close();
          }
        } else {
          await writable.close();
        }

        // 验证文件完整性
        const finalSize = await getDownloadedSize(targetDirHandle, dirPath, fileName);
        if (finalSize !== file.size) {
          throw new Error(`文件大小不匹配: 期望 ${file.size}, 实际 ${finalSize}`);
        }

        // 标记文件完成
        updateFileStatus(file, {
          status: 'completed',
          downloaded: file.size,
          speed: 0
        });

        return true;

      } catch (error: any) {
        retryCount = attempt + 1;

        if (error.message === 'Download cancelled') {
          updateFileStatus(file, {
            status: 'failed',
            error: 'Download cancelled',
            retryCount: retryCount
          });
          throw error;
        }

        console.error(`Download attempt ${attempt + 1} failed for ${file.name}:`, error);

        // 如果是最后一次尝试，标记为失败
        if (attempt === maxRetries) {
          updateFileStatus(file, {
            status: 'failed',
            error: error.message,
            lastError: `重试 ${maxRetries} 次后仍然失败`,
            retryCount: retryCount
          });
          return false;
        }
      }
    }

    return false;
  };

  // 选择文件夹并获取文件结构
  const selectFolderAndFetch = async () => {
    if (!isSupported()) {
      setStatus('error');
      setErrorMessage('您的浏览器不支持 File System Access API，请使用 Chrome 86+ 或 Edge 86+');
      return;
    }

    try {
      // 让用户选择下载文件夹
      setStatus('selecting');
      const selectedDirectoryHandle = await (window as any).showDirectoryPicker({
        mode: 'readwrite'
      });

      setDirectoryHandle(selectedDirectoryHandle);

      // 获取所有文件结构（并行处理）
      setStatus('fetching');
      const filesResult = await fetchAllFolderStructures(selectedObjs);

      if (typeof filesResult === 'string') {
        throw new Error(`获取文件结构失败: ${filesResult}`);
      }

      const allFiles = filesResult;
      if (allFiles.length === 0) {
        throw new Error('没有找到可下载的文件');
      }

      // 计算总大小并初始化文件状态
      const totalSize = allFiles.reduce((sum, file) => sum + file.size, 0);
      const sessionId = generateSessionId();
      const fileStatuses: FileDownloadStatus[] = allFiles.map(file => ({
        file,
        status: 'pending',
        downloaded: 0,
        speed: 0,
        retryCount: 0,
        resumeSupported: false
      }));

      setProgress(prev => ({
        ...prev,
        total: allFiles.length,
        totalSize: totalSize,
        fileStatuses: fileStatuses,
        sessionId: sessionId
      }));

      // 设置为准备状态，等待用户确认开始下载
      setStatus('ready');

      // 重置分页到第一页
      setCurrentPage(0);

    } catch (error: any) {
      console.error('Fetch error:', error);

      // 检查是否是用户取消选择文件夹
      if (error.name === 'AbortError' || error.message.includes('aborted') || error.message.includes('cancelled')) {
        // 用户取消选择，回到初始状态
        setStatus('init');
        return;
      }

      // 其他错误才显示错误信息
      setStatus('error');
      setErrorMessage(error.message || '获取文件结构时发生未知错误');
    }
  };

  // 开始实际下载
  const startActualDownload = async () => {
    const handle = directoryHandle();
    if (!handle) {
      setStatus('error');
      setErrorMessage('未选择下载文件夹');
      return;
    }

    try {
      setStatus('downloading');
      setProgress(prev => ({ ...prev, startTime: Date.now() }));

      // 重置取消信号
      setCancelSignal(false);

      // 保存下载会话
      const session: DownloadSession = {
        sessionId: progress().sessionId,
        directoryPath: handle.name,
        progress: progress(),
        timestamp: Date.now()
      };
      saveDownloadSession(session);

      // 创建并发队列
      const downloadQueue = new ConcurrentQueue(maxConcurrency);
      const allFiles = progress().fileStatuses.map(fs => fs.file);

      // 创建下载任务
      const downloadPromises = allFiles.map(file =>
        downloadQueue.add(async () => {
          if (cancelSignal()) {
            throw new Error('Download cancelled');
          }

          try {
            const success = await downloadAndSaveFile(handle, file);
            if (success) {
              setProgress(prev => ({ ...prev, completed: prev.completed + 1 }));
            }
            return success;
          } catch (error: any) {
            if (error.message === 'Download cancelled') {
              throw error;
            }
            console.error(`Failed to download ${file.name}:`, error);
            return false;
          }
        })
      );

      // 等待所有下载完成
      const results = await Promise.allSettled(downloadPromises);

      // 检查是否被取消
      if (cancelSignal()) {
        setStatus('cancelled');
        clearDownloadSession();
        return;
      }

      // 统计结果
      const successful = results.filter(r => r.status === 'fulfilled' && r.value === true).length;
      const failed = results.length - successful;

      console.log(`Download completed: ${successful} successful, ${failed} failed`);
      setStatus('completed');

      // 清理下载会话
      clearDownloadSession();

    } catch (error: any) {
      console.error('Download error:', error);
      setStatus('error');
      setErrorMessage(error.message || '下载过程中发生未知错误');
    }
  };

  // 取消下载
  const cancelDownload = () => {
    setCancelSignal(true);
  };

  // 检查是否有可恢复的下载会话
  const checkResumeSession = () => {
    const savedSession = loadDownloadSession();
    return savedSession;
  };

  // 恢复下载会话
  const resumeDownloadSession = (session: DownloadSession) => {
    setProgress(session.progress);
    setStatus('ready');
    setCurrentPage(0);
  };

  // 分页组件
  const PaginationControls = () => (
    <Show when={totalPages() > 1}>
      <HStack spacing="$2" justifyContent="center" alignItems="center">
        <IconButton
          aria-label="Previous page"
          icon={<Icon as={BsChevronLeft} />}
          size="sm"
          variant="outline"
          disabled={currentPage() === 0}
          onClick={prevPage}
        />
        <Text fontSize="$sm">
          第 {currentPage() + 1} 页，共 {totalPages()} 页
        </Text>
        <Text fontSize="$xs" color="$neutral11">
          ({progress().fileStatuses.length} 个文件)
        </Text>
        <IconButton
          aria-label="Next page"
          icon={<Icon as={BsChevronRight} />}
          size="sm"
          variant="outline"
          disabled={currentPage() >= totalPages() - 1}
          onClick={nextPage}
        />
      </HStack>
    </Show>
  );

  return (
    <>
      <ModalBody>
        <VStack w="$full" alignItems="start" spacing="$4">
          <Show when={!isSupported()}>
            <Alert status="warning">
              <AlertIcon />
              <AlertTitle>浏览器不支持批量下载功能</AlertTitle>
              <AlertDescription>
                <VStack spacing="$2" alignItems="start">
                  <Text fontWeight="$semibold">支持的浏览器版本：</Text>
                  <Text>• Chrome 86 或更高版本</Text>
                  <Text>• Microsoft Edge 86 或更高版本</Text>
                  <Text fontSize="$sm" color="$neutral11">
                    请更新到支持的浏览器版本以使用此功能。
                  </Text>
                </VStack>
              </AlertDescription>
            </Alert>
          </Show>

          <Show when={status() === 'init' && isSupported()}>
            <VStack spacing="$2" alignItems="start">
              <Text>将要下载 {selectedObjs.length} 个项目，请选择要保存到本地的路径</Text>

              {/* 检查是否有可恢复的下载会话 */}
              <Show when={checkResumeSession()}>
                <Alert status="info">
                  <AlertIcon />
                  <AlertTitle>发现未完成的下载</AlertTitle>
                  <AlertDescription>
                    检测到有未完成的下载任务，是否要恢复？
                  </AlertDescription>
                </Alert>
              </Show>
            </VStack>
          </Show>

          <Show when={status() === 'selecting'}>
            <Text>请选择下载文件夹...</Text>
          </Show>

          <Show when={status() === 'fetching'}>
            <Text>正在获取文件结构...</Text>
          </Show>

          <Show when={status() === 'ready'}>
            <VStack w="$full" spacing="$3">
              <Alert status="info">
                <AlertIcon />
                <AlertTitle>文件结构获取完成</AlertTitle>
                <AlertDescription>
                  共找到 {progress().total} 个文件，总大小 {formatFileSize(progress().totalSize)}
                </AlertDescription>
              </Alert>

              {/* 下载文件队列预览 */}
              <VStack w="$full" spacing="$3">
                <VStack w="$full" spacing="$2" alignItems="start">
                  <Text fontWeight="$semibold">下载文件队列:</Text>
                  <PaginationControls />
                </VStack>

                <VStack w="$full" spacing="$2" minH="300px">
                  <For each={visibleFiles()}>
                    {(fileStatus) => (
                      <Box w="$full" p="$2" borderRadius="$md" bg="$neutral2" border="1px solid $neutral6">
                        <HStack w="$full" justifyContent="space-between" alignItems="center">
                          {/* 文件名 */}
                          <VStack alignItems="start" spacing="$1" flex="1" minW="0">
                            <Text fontSize="$sm" fontWeight="$medium" noOfLines={1}>
                              {fileStatus.file.name}
                            </Text>
                            <Text fontSize="$xs" color="$neutral11" noOfLines={1}>
                              {fileStatus.file.path}
                            </Text>
                          </VStack>

                          {/* 文件大小 */}
                          <VStack alignItems="end" spacing="$1" minW="80px">
                            <Text fontSize="$xs">
                              {formatFileSize(fileStatus.file.size)}
                            </Text>
                            <Badge colorScheme="neutral" size="sm">
                              等待下载
                            </Badge>
                          </VStack>
                        </HStack>
                      </Box>
                    )}
                  </For>
                </VStack>

                <Show when={totalPages() > 1}>
                  <PaginationControls />
                </Show>
              </VStack>
            </VStack>
          </Show>

          <Show when={status() === 'downloading'}>
            <VStack w="$full" spacing="$3">
              {/* 总体统计 */}
              <HStack w="$full" justifyContent="space-between">
                <Text fontWeight="$semibold">
                  下载进度: {progress().completed} / {progress().total}
                </Text>
                <Text fontSize="$sm" color="$neutral11">
                  {formatFileSize(progress().totalDownloaded)} / {formatFileSize(progress().totalSize)}
                </Text>
              </HStack>

              <Divider />

              {/* 文件下载队列 */}
              <VStack w="$full" spacing="$3">
                <VStack w="$full" spacing="$2" alignItems="start">
                  <Text fontWeight="$semibold">文件下载状态:</Text>
                  <PaginationControls />
                </VStack>

                <VStack w="$full" spacing="$2" minH="300px">
                  <For each={visibleFiles()}>
                    {(fileStatus) => (
                      <Box w="$full" p="$2" borderRadius="$md" bg="$neutral2" border="1px solid $neutral6">
                        <HStack w="$full" justifyContent="space-between" alignItems="center">
                          {/* 文件名 */}
                          <VStack alignItems="start" spacing="$1" flex="1" minW="0">
                            <Text fontSize="$sm" fontWeight="$medium" noOfLines={1}>
                              {fileStatus.file.name}
                            </Text>
                            <Text fontSize="$xs" color="$neutral11" noOfLines={1}>
                              {fileStatus.file.path}
                            </Text>
                          </VStack>

                          {/* 下载信息 */}
                          <VStack alignItems="end" spacing="$1" minW="120px">
                            <Text fontSize="$xs">
                              {formatFileSize(fileStatus.downloaded)} / {formatFileSize(fileStatus.file.size)}
                            </Text>
                            <HStack spacing="$2" alignItems="center">
                              <Show when={fileStatus.status === 'downloading' && fileStatus.speed > 0}>
                                <Text fontSize="$xs" color="$info9">
                                  {formatSpeed(fileStatus.speed)}
                                </Text>
                              </Show>
                              <Badge
                                colorScheme={
                                  fileStatus.status === 'completed' ? 'success' :
                                  fileStatus.status === 'failed' ? 'danger' :
                                  fileStatus.status === 'downloading' ? 'info' :
                                  fileStatus.status === 'retrying' ? 'warning' :
                                  fileStatus.status === 'paused' ? 'neutral' : 'neutral'
                                }
                                size="sm"
                              >
                                {
                                  fileStatus.status === 'completed' ? '已完成' :
                                  fileStatus.status === 'failed' ? '失败' :
                                  fileStatus.status === 'downloading' ? '下载中' :
                                  fileStatus.status === 'retrying' ? `重试中(${fileStatus.retryCount})` :
                                  fileStatus.status === 'paused' ? '已暂停' : '等待中'
                                }
                              </Badge>
                            </HStack>
                          </VStack>
                        </HStack>

                        {/* 错误信息和状态信息 */}
                        <Show when={fileStatus.status === 'failed' && fileStatus.error}>
                          <Text fontSize="$xs" color="$danger9" mt="$1">
                            错误: {fileStatus.error}
                          </Text>
                        </Show>

                        <Show when={fileStatus.status === 'retrying' && fileStatus.lastError}>
                          <Text fontSize="$xs" color="$warning9" mt="$1">
                            {fileStatus.lastError}
                          </Text>
                        </Show>

                        <Show when={fileStatus.resumeSupported && fileStatus.downloaded > 0}>
                          <Text fontSize="$xs" color="$info9" mt="$1">
                            支持断点续传 (已下载: {formatFileSize(fileStatus.downloaded)})
                          </Text>
                        </Show>
                      </Box>
                    )}
                  </For>
                </VStack>

                <Show when={totalPages() > 1}>
                  <PaginationControls />
                </Show>
              </VStack>
            </VStack>
          </Show>

          <Show when={status() === 'completed'}>
            <VStack spacing="$3" alignItems="start" w="$full">
              <Alert status="success">
                <AlertIcon />
                <AlertTitle>下载完成！</AlertTitle>
                <AlertDescription>
                  总共 {progress().total} 个文件，成功 {progress().fileStatuses.filter(fs => fs.status === 'completed').length} 个，
                  失败 {progress().fileStatuses.filter(fs => fs.status === 'failed').length} 个
                </AlertDescription>
              </Alert>

              {/* 显示最终文件列表 */}
              <VStack w="$full" spacing="$3">
                <VStack w="$full" spacing="$2" alignItems="start">
                  <Text fontWeight="$semibold">下载结果:</Text>
                  <PaginationControls />
                </VStack>

                <VStack w="$full" spacing="$2" minH="200px">
                  <For each={visibleFiles()}>
                    {(fileStatus) => (
                      <HStack w="$full" justifyContent="space-between" alignItems="center" p="$2" borderRadius="$sm" bg="$neutral1">
                        <Text fontSize="$sm" noOfLines={1} flex="1">
                          {fileStatus.file.name}
                        </Text>
                        <HStack spacing="$2">
                          <Text fontSize="$xs">
                            {formatFileSize(fileStatus.file.size)}
                          </Text>
                          <Badge
                            colorScheme={fileStatus.status === 'completed' ? 'success' : 'danger'}
                            size="sm"
                          >
                            {fileStatus.status === 'completed' ? '成功' : '失败'}
                          </Badge>
                        </HStack>
                      </HStack>
                    )}
                  </For>
                </VStack>

                <Show when={totalPages() > 1}>
                  <PaginationControls />
                </Show>
              </VStack>
            </VStack>
          </Show>

          <Show when={status() === 'cancelled'}>
            <VStack spacing="$3" alignItems="start" w="$full">
              <Alert status="warning">
                <AlertIcon />
                <AlertTitle>下载已取消</AlertTitle>
                <AlertDescription>
                  已完成 {progress().fileStatuses.filter(fs => fs.status === 'completed').length} / {progress().total} 个文件的下载
                </AlertDescription>
              </Alert>

              {/* 显示取消时的文件状态 */}
              <VStack w="$full" spacing="$3">
                <VStack w="$full" spacing="$2" alignItems="start">
                  <Text fontWeight="$semibold">取消时状态:</Text>
                  <PaginationControls />
                </VStack>

                <VStack w="$full" spacing="$2" minH="200px">
                  <For each={visibleFiles()}>
                    {(fileStatus) => (
                      <HStack w="$full" justifyContent="space-between" alignItems="center" p="$2" borderRadius="$sm" bg="$neutral1">
                        <Text fontSize="$sm" noOfLines={1} flex="1">
                          {fileStatus.file.name}
                        </Text>
                        <HStack spacing="$2">
                          <Text fontSize="$xs">
                            {formatFileSize(fileStatus.downloaded)} / {formatFileSize(fileStatus.file.size)}
                          </Text>
                          <Badge
                            colorScheme={
                              fileStatus.status === 'completed' ? 'success' :
                              fileStatus.status === 'failed' ? 'danger' : 'warning'
                            }
                            size="sm"
                          >
                            {
                              fileStatus.status === 'completed' ? '已完成' :
                              fileStatus.status === 'failed' ? '失败' : '已取消'
                            }
                          </Badge>
                        </HStack>
                      </HStack>
                    )}
                  </For>
                </VStack>

                <Show when={totalPages() > 1}>
                  <PaginationControls />
                </Show>
              </VStack>
            </VStack>
          </Show>

          <Show when={status() === 'error'}>
            <Alert status="danger">
              <AlertIcon />
              <AlertTitle>下载失败</AlertTitle>
              <AlertDescription>{errorMessage()}</AlertDescription>
            </Alert>
          </Show>
        </VStack>
      </ModalBody>
      
      <ModalFooter display="flex" gap="$2">
        {/* 初始状态：选择文件夹按钮 */}
        <Show when={status() === 'init' && isSupported()}>
          <HStack spacing="$2">
            <Button colorScheme="primary" onClick={selectFolderAndFetch}>
              保存到
            </Button>

            {/* 恢复下载按钮 */}
            <Show when={checkResumeSession()}>
              <Button
                colorScheme="info"
                variant="outline"
                onClick={() => {
                  const session = checkResumeSession();
                  if (session) {
                    resumeDownloadSession(session);
                  }
                }}
              >
                恢复下载
              </Button>
            </Show>
          </HStack>
        </Show>

        {/* 准备状态：开始下载和取消按钮 */}
        <Show when={status() === 'ready'}>
          <Button colorScheme="success" onClick={startActualDownload}>
            开始下载
          </Button>
          <Button colorScheme="neutral" onClick={props.onClose}>
            取消
          </Button>
        </Show>

        {/* 下载中：取消下载按钮 */}
        <Show when={status() === 'downloading'}>
          <Button colorScheme="danger" onClick={cancelDownload}>
            取消下载
          </Button>
        </Show>

        {/* 其他状态：关闭按钮 */}
        <Show when={status() !== 'init' && status() !== 'ready' && status() !== 'downloading'}>
          <Button
            variant="outline"
            onClick={props.onClose}
            disabled={status() === 'fetching' || status() === 'selecting'}
          >
            {(status() === 'fetching' || status() === 'selecting') ? '处理中...' : '关闭'}
          </Button>
        </Show>
      </ModalFooter>
    </>
  );
};

export default DirectFolderDownload;
