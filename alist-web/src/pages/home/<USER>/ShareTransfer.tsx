import {
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Textarea,
  Text,
  Checkbox,
  Progress,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from "@hope-ui/solid"
import { createSignal, For, Show } from "solid-js"
import { useT } from "~/hooks"
import { ModalWrapper } from "./ModalWrapper"
import { r, notify } from "~/utils"
import { PEmptyResp } from "~/types"

export const ShareTransfer = () => {
  const t = useT()
  const [shareLinks, setShareLinks] = createSignal("")
  const [baiduCookie, setBaiduCookie] = createSignal("")
  const [loading, setLoading] = createSignal(false)
  const [progress, setProgress] = createSignal(0)
  const [results, setResults] = createSignal<string[]>([])
  const [showResults, setShowResults] = createSignal(false)

  const validateInputs = () => {
    const links = shareLinks()
      .trim()
      .split("\n")
      .filter((link) => link.trim())

    if (links.length === 0) {
      notify.error(t("home.toolbar.share_transfer.error_no_links"))
      return false
    }

    if (links.length > 10) {
      notify.error(t("home.toolbar.share_transfer.error_too_many_links"))
      return false
    }

    if (!baiduCookie().trim()) {
      notify.error(t("home.toolbar.share_transfer.error_no_cookie"))
      return false
    }

    if (!baiduCookie().includes("BAIDUID")) {
      notify.error(t("home.toolbar.share_transfer.error_invalid_cookie"))
      return false
    }

    return true
  }

  const handleTransfer = async () => {
    if (!validateInputs()) {
      return
    }

    const links = shareLinks()
      .trim()
      .split("\n")
      .filter((link) => link.trim())

    setLoading(true)
    setProgress(0)
    setResults([])
    setShowResults(false)

    try {
      // 检查当前token
      const currentToken = localStorage.getItem("token")
      console.log(
        "当前token:",
        currentToken ? currentToken.substring(0, 50) + "..." : "无token",
      )

      console.log("发送转存请求:", {
        share_links: links,
        baidu_cookie: baiduCookie().substring(0, 50) + "...", // 只显示前50个字符
      })

      const resp = await r.post("/fs/share_transfer", {
        share_links: links,
        baidu_cookie: baiduCookie(),
      })

      console.log("API响应:", resp)

      if (resp.code === 200) {
        const result = resp.data
        setProgress(100)
        setResults(result.results || [])
        setShowResults(true)

        notify.success(
          `转存完成！成功: ${result.success}, 失败: ${result.failed}`,
        )

        // 只有全部成功时才清空表单
        if (result.failed === 0) {
          setShareLinks("")
          setBaiduCookie("")
        }
      } else {
        console.error("API错误:", resp)
        notify.error(resp.message || `转存失败 (错误码: ${resp.code})`)
      }
    } catch (error: any) {
      console.error("ShareTransfer error:", error)
      if (error.response) {
        console.error("错误响应:", error.response)
        const status = error.response.status
        const message = error.response.data?.message || error.message

        if (status === 401) {
          notify.error("请先登录后再使用此功能")
        } else if (status === 403) {
          notify.error("权限不足，无法执行转存操作")
        } else {
          notify.error(`请求失败 (${status}): ${message}`)
        }
      } else if (error.code) {
        // 这是我们自定义的错误响应格式
        if (error.code === 401) {
          notify.error("请先登录后再使用此功能")
        } else if (error.code === 403) {
          notify.error("权限不足，无法执行转存操作")
        } else {
          notify.error(`转存失败 (错误码: ${error.code}): ${error.message}`)
        }
      } else {
        notify.error(error.message || "网络请求失败")
      }
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setShareLinks("")
    setBaiduCookie("")
    setProgress(0)
    setResults([])
    setShowResults(false)
  }

  return (
    <ModalWrapper
      name="share_transfer"
      title={t("home.toolbar.share_transfer.title")}
    >
      <VStack spacing="$4" w="$full">
        <Alert status="info">
          <AlertIcon />
          <AlertTitle>智能分享转存</AlertTitle>
          <AlertDescription>
            自动识别分享链接类型，并转存到对应的分享文件夹中。支持百度网盘、阿里云盘、123云盘等多种平台。
          </AlertDescription>
        </Alert>

        <FormControl required>
          <FormLabel for="baidu-cookie">百度网盘Cookie</FormLabel>
          <Textarea
            id="baidu-cookie"
            placeholder="请输入完整的百度网盘Cookie，包含BAIDUID、BDUSS等字段..."
            value={baiduCookie()}
            onInput={(e) => setBaiduCookie(e.currentTarget.value)}
            rows={3}
          />
        </FormControl>

        <FormControl required>
          <FormLabel for="share-links">分享链接</FormLabel>
          <Textarea
            id="share-links"
            placeholder="请输入分享链接，每行一个，最多10个。支持百度网盘、阿里云盘、123云盘等多种平台..."
            value={shareLinks()}
            onInput={(e) => setShareLinks(e.currentTarget.value)}
            rows={6}
          />
          <Text size="sm" color="$neutral11" mt="$1">
            当前链接数量：{" "}
            {
              shareLinks()
                .trim()
                .split("\n")
                .filter((link) => link.trim()).length
            }
            /10
          </Text>
          <Text size="sm" color="$info11" mt="$1">
            💡 系统将自动识别链接类型，并转存到对应的分享文件夹中
          </Text>
        </FormControl>

        <Show when={loading()}>
          <VStack spacing="$2" w="$full">
            <Text>正在转存中，请稍候...</Text>
            <Progress value={progress()} w="$full" />
          </VStack>
        </Show>

        <Show when={showResults()}>
          <VStack spacing="$2" w="$full">
            <Text fontWeight="bold">转存结果</Text>
            <VStack spacing="$1" w="$full" maxH="200px" overflowY="auto">
              <For each={results()}>
                {(result) => (
                  <Text
                    size="sm"
                    color={result.includes("成功") ? "$success11" : "$danger11"}
                  >
                    {result}
                  </Text>
                )}
              </For>
            </VStack>
          </VStack>
        </Show>

        <HStack w="$full" justifyContent="flex-end" spacing="$2">
          <Button
            colorScheme="neutral"
            variant="subtle"
            onClick={resetForm}
            disabled={loading()}
          >
            重置
          </Button>
          <Button
            colorScheme="accent"
            loading={loading()}
            onClick={handleTransfer}
          >
            开始转存
          </Button>
        </HStack>
      </VStack>
    </ModalWrapper>
  )
}
