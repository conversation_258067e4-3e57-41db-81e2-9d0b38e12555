import { H<PERSON><PERSON>ck, V<PERSON><PERSON><PERSON>, Text, Badge, Icon } from "@hope-ui/solid"
import { createMemo } from "solid-js"
import { Obj } from "~/types"
import {
  BsHddNetwork,
  BsFolder,
  BsShare
} from "solid-icons/bs"

interface VirtualStorageItemProps {
  obj: Obj & {
    StorageInfo?: string
    Capacity?: string
    Status?: string
  }
  index: number
}

export const VirtualStorageItem = (props: VirtualStorageItemProps) => {
  // 根据存储类型选择图标
  const storageIcon = createMemo(() => {
    const storageType = props.obj.StorageInfo?.toLowerCase() || ""

    if (storageType.includes("local")) return BsFolder
    if (storageType.includes("share")) return BsShare

    return BsHddNetwork // 默认网络存储图标
  })
  
  // 根据状态选择颜色
  const statusColor = createMemo(() => {
    const status = props.obj.Status || "正常"
    return status === "正常" ? "success" : "danger"
  })
  
  // 格式化存储类型显示
  const formatStorageType = createMemo(() => {
    const type = props.obj.StorageInfo || "未知"
    const typeMap: Record<string, string> = {
      "baidu_netdisk": "百度网盘",
      "aliyundrive": "阿里云盘", 
      "onedrive": "OneDrive",
      "123": "123云盘",
      "local": "本地存储",
      "unknown": "未知存储"
    }
    return typeMap[type] || type
  })
  
  return (
    <HStack
      w="$full"
      p="$3"
      borderRadius="$md"
      border="1px solid"
      borderColor="$neutral6"
      spacing="$3"
      bg="$neutral2"
      cursor="pointer"
      _hover={{
        bg: "$neutral3",
        transform: "scale(1.02)",
        borderColor: "$primary8",
      }}
      transition="all 0.2s ease"
      onClick={() => {
        console.log("点击虚拟存储空间:", props.obj.name)
      }}
    >
      {/* 存储图标 */}
      <Icon
        as={storageIcon()}
        boxSize="40px"
        color="$primary10"
        p="$2"
        borderRadius="$md"
        bg="$primary3"
      />
      
      {/* 存储信息 */}
      <VStack flex="1" spacing="$1" alignItems="flex-start">
        {/* 存储名称 */}
        <Text
          fontSize="$lg"
          fontWeight="$semibold"
          color="$neutral12"
          lineHeight="$5"
        >
          {props.obj.name}
        </Text>
        
        {/* 存储类型 */}
        <Text
          fontSize="$sm"
          color="$neutral10"
          lineHeight="$4"
        >
          类型: {formatStorageType()}
        </Text>
        
        {/* 容量信息 */}
        {props.obj.Capacity && (
          <Text
            fontSize="$xs"
            color="$neutral9"
            lineHeight="$3"
          >
            容量: {props.obj.Capacity}
          </Text>
        )}
      </VStack>
      
      {/* 状态标识 */}
      <Badge
        variant="solid"
        colorScheme={statusColor()}
        size="sm"
      >
        {props.obj.Status || "正常"}
      </Badge>
    </HStack>
  )
}
