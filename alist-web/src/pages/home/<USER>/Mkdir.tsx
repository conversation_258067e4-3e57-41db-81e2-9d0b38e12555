import { createDisclosure } from "@hope-ui/solid"
import { ModalInput } from "~/components"
import { useFetch, usePath, useRouter } from "~/hooks"
import { bus, fsMkdir, handleRespWithNotifySuccess, pathJoin, notify } from "~/utils"
import { onCleanup, createMemo } from "solid-js"
import { me } from "~/store"
import { UserMethods } from "~/types"

export const Mkdir = () => {
  const { isOpen, onOpen, onClose } = createDisclosure()
  const [loading, ok] = useFetch(fsMkdir)
  const { pathname } = useRouter()
  const { refresh } = usePath()

  // 检查是否为普通用户在根目录
  const isRootForNormalUser = createMemo(() => {
    const user = me()
    const path = pathname()
    return !UserMethods.is_admin(user) && (path === "/" || path === "")
  })

  const handler = (name: string) => {
    if (name === "mkdir") {
      // 检查普通用户是否在根目录
      if (isRootForNormalUser()) {
        notify.error("当前目录不支持创建文件！")
        return
      }
      onOpen()
    }
  }
  bus.on("tool", handler)
  onCleanup(() => {
    bus.off("tool", handler)
  })
  return (
    <ModalInput
      title="home.toolbar.input_dir_name"
      opened={isOpen()}
      onClose={onClose}
      loading={loading()}
      onSubmit={async (name) => {
        const resp = await ok(pathJoin(pathname(), name))
        handleRespWithNotifySuccess(resp, async () => {
          // 强制刷新文件列表，绕过缓存
          await refresh(false, true) // force = true
          onClose()
        })
      }}
    />
  )
}
