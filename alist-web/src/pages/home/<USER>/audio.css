#audio-player {
  border-radius: 8px;
}
.aplayer {
  background: var(--hope-colors-neutral2);
  color: var(--hope-colors-neutral12) !important;
}
.aplayer-body .aplayer-pic {
  background-color: unset !important;
}
.aplayer .aplayer-info .aplayer-music .aplayer-author {
  color: var(--hope-colors-neutral11);
}
.aplayer .aplayer-list ol li:hover {
  background: var(--hope-colors-neutral4);
}
.aplayer .aplayer-list ol li.aplayer-list-light {
  background: var(--hope-colors-neutral4);
}
.aplayer .aplayer-list ol li .aplayer-list-author {
  color: var(--hope-colors-neutral9);
}
.aplayer .aplayer-list ol li .aplayer-list-index {
  color: var(--hope-colors-neutral10);
}
.aplayer .aplayer-lrc:after {
  background: none;
}
.aplayer .aplayer-lrc:before {
  background: none;
}
.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon path {
  fill: var(--hope-colors-neutral12);
}
.aplayer .aplayer-lrc p {
  color: var(--hope-colors-neutral12);
}
.aplayer .aplayer-list ol li {
  border-top: 1px solid var(--hope-colors-neutral6);
}
.aplayer.aplayer-withlist .aplayer-info {
  border-bottom: 1px solid var(--hope-colors-neutral7);
}
