import { Grid } from "@hope-ui/solid"
import { For } from "solid-js"
import { GridItem } from "./GridItem"
import "lightgallery/css/lightgallery-bundle.css"
import { local, objStore } from "~/store"
import { useSelectWithMouse } from "./helper"

const GridLayout = () => {
  const { isMouseSupported, registerSelectContainer, captureContentMenu } =
    useSelectWithMouse()
  registerSelectContainer()
  return (
    <Grid
      oncapture:contextmenu={captureContentMenu}
      class="viselect-container"
      w="100%"
      maxW="100%"
      minW="0"
      gap="16px"
      templateColumns="repeat(auto-fill, minmax(200px, 1fr))"
      pt="0"
      px="16px"
      pb="50px"
      bgColor="transparent"
      css={{
        boxSizing: "border-box", // 确保所有尺寸计算正确
        overflow: "visible", // 允许悬停缩放效果向外扩展
        width: "100%", // 使用100%宽度
        maxWidth: "100%", // 最大宽度100%
        display: "grid", // 确保是grid布局
        // 调整网格模板列，确保考虑内边距后的实际可用宽度
        gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
        // 确保网格项不会超出容器边界
        gridAutoFlow: "row",
        // 为悬停效果预留空间
        padding: "8px 8px 8px 8px", // 增加内边距为悬停缩放预留空间
        margin: "0 -8px -8px -8px", // 负边距抵消左右下内边距对布局的影响，保持顶部间距
        // 网格项基础样式
        "& > *": {
          minWidth: "0",
          maxWidth: "100%",
          overflow: "visible", // 允许子元素缩放效果
          position: "relative", // 确保缩放时层级正确
        },
      }}
    >
      <For each={objStore.objs}>
        {(obj, i) => {
          return <GridItem obj={obj} index={i()} />
        }}
      </For>
    </Grid>
  )
}

export default GridLayout
