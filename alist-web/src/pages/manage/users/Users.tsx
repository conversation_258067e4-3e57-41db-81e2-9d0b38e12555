import {
  Badge,
  Box,
  Button,
  HStack,
  Progress,
  ProgressIndicator,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  VStack,
} from "@hope-ui/solid"
import { createSignal, For, Show, createMemo } from "solid-js"
import {
  useFetch,
  useListFetch,
  useManageTitle,
  useRouter,
  useT,
} from "~/hooks"
import { handleResp, notify, r } from "~/utils"
import {
  UserPermissions,
  User,
  UserMethods,
  PPageResp,
  PEmptyResp,
} from "~/types"
import { DeletePopover } from "../common/DeletePopover"
import { Wether } from "~/components"

// 格式化容量大小
function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 B"
  const k = 1024
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

// 计算容量使用百分比的辅助函数
const getCapacityPercentage = (usedBytes: number, totalBytes: number): number => {
  if (!totalBytes || totalBytes === 0) {
    return 0
  }
  return Math.round((usedBytes / totalBytes) * 100)
}

// 基础路径显示组件
const BasePathsDisplay = (props: { user: User }) => {
  const t = useT()

  // 使用 createMemo 缓存基础路径数据，避免重复计算
  const basePaths = createMemo(() => UserMethods.get_base_paths(props.user))

  // 如果只有一个基础路径且是传统的base_path，显示简化版本
  const singleBasePath = createMemo(() => {
    const paths = basePaths()
    return paths.length === 1 && paths[0].id === 0 ? paths[0] : null
  })

  return (
    <Show
      when={!singleBasePath()}
      fallback={<Text fontSize="10px">{singleBasePath()?.path}</Text>}
    >
      <VStack spacing="$1" alignItems="start" w="$full">
        <For each={basePaths()}>
          {(basePath) => (
            <Box
              w="$full"
              p="$2"
              border="1px solid $neutral6"
              borderRadius="$md"
              bg="$neutral1"
            >
              <VStack spacing="$1" alignItems="start">
                {/* 存储空间名称和路径 */}
                <HStack spacing="$1" alignItems="center" w="$full">
                  <Text fontSize="10px" fontWeight="bold" color="$primary11">
                    {basePath.alias}
                  </Text>
                  <Text fontSize="9px" color="$neutral10">
                    ({basePath.path})
                  </Text>
                </HStack>

                {/* 容量信息 - 只对启用容量限制的基础路径显示 */}
                <Show when={basePath.enable_capacity_limit && basePath.total_bytes > 0}>
                  {(() => {
                    // 只在需要显示时才计算百分比，提升性能
                    const percentage = getCapacityPercentage(basePath.used_bytes, basePath.total_bytes)

                    // 根据使用率定义颜色
                    const getProgressColor = () => {
                      if (percentage >= 99) return "#ef4444" // 红色 - 几乎满了
                      if (percentage > 90) return "#f59e0b" // 橙色 - 警告
                      if (percentage > 70) return "#eab308" // 黄色 - 注意
                      return "#10b981" // 绿色 - 正常
                    }

                    const getTextColor = () => {
                      if (percentage >= 90) return "white" // 高使用率时用白色文字
                      return "#1f2937" // 深灰色文字
                    }

                    return (
                      <Box position="relative" w="$full">
                        <Progress
                          value={percentage}
                          max={100}
                          size="md"
                          w="$full"
                          trackColor="$neutral4"
                          borderRadius="$md"
                        >
                          <ProgressIndicator
                            bg={getProgressColor()}
                            borderRadius="$md"
                          />
                        </Progress>
                        {/* 在进度条内显示容量信息 */}
                        <Box
                          position="absolute"
                          top="50%"
                          left="50%"
                          transform="translate(-50%, -50%)"
                          zIndex="1"
                        >
                          <Text
                            fontSize="9px"
                            fontWeight="medium"
                            color={getTextColor()}
                            textAlign="center"
                            whiteSpace="nowrap"
                            textShadow={percentage >= 90 ? "0 1px 2px rgba(0,0,0,0.5)" : "none"}
                          >
                            {formatBytes(basePath.used_bytes)} / {formatBytes(basePath.total_bytes)} ({percentage}%)
                          </Text>
                        </Box>
                      </Box>
                    )
                  })()}
                </Show>



                {/* 未启用容量限制时的显示 */}
                <Show when={!basePath.enable_capacity_limit}>
                  <Text fontSize="9px" color="#6b7280" fontStyle="italic">
                    {t("users.capacity_limit_disabled")}
                  </Text>
                </Show>
              </VStack>
            </Box>
          )}

      </For>
      </VStack>
    </Show>
  )
}

const Role = (props: { role: number }) => {
  const t = useT()
  const roles = [
    { name: "general", color: "info" },
    { name: "guest", color: "neutral" },
    { name: "admin", color: "accent" },
  ]
  return (
    <Badge colorScheme={roles[props.role].color as any}>
      {t(`users.roles.${roles[props.role].name}`)}
    </Badge>
  )
}

const Permissions = (props: { user: User }) => {
  const t = useT()
  const color = (can: boolean) => `$${can ? "success" : "danger"}9`
  return (
    <HStack spacing="$0_5">
      <For each={UserPermissions}>
        {(item, i) => (
          <Tooltip label={t(`users.permissions.${item}`)}>
            <Box
              boxSize="$2"
              rounded="$full"
              bg={color(UserMethods.can(props.user, i()))}
            ></Box>
          </Tooltip>
        )}
      </For>
    </HStack>
  )
}

const Users = () => {
  const t = useT()
  useManageTitle("manage.sidemenu.users")
  const { to } = useRouter()
  const [getUsersLoading, getUsers] = useFetch(
    (): PPageResp<User> => r.get("/admin/user/list"),
  )
  const [users, setUsers] = createSignal<User[]>([])
  const refresh = async () => {
    const resp = await getUsers()
    handleResp(resp, (data) => setUsers(data.content))
  }
  refresh()

  const [deleting, deleteUser] = useListFetch(
    (id: number): PEmptyResp => r.post(`/admin/user/delete?id=${id}`),
  )
  const [cancel_2faId, cancel_2fa] = useListFetch(
    (id: number): PEmptyResp => r.post(`/admin/user/cancel_2fa?id=${id}`),
  )
  return (
    <VStack spacing="$2" alignItems="start" w="$full">
      <HStack spacing="$2">
        <Button
          colorScheme="accent"
          loading={getUsersLoading()}
          onClick={refresh}
        >
          {t("global.refresh")}
        </Button>
        <Button
          onClick={() => {
            to("/@manage/users/add")
          }}
        >
          {t("global.add")}
        </Button>
      </HStack>
      <Box w="$full" overflowX="auto">
        <Table highlightOnHover dense>
          <Thead>
            <Tr>
              <For
                each={[
                  "username",
                  "email",
                  "role",
                  "base_path",
                  "available",
                ]}
              >
                {(title) => <Th>{t(`users.${title}`)}</Th>}
              </For>
              <Th>{t("global.operations")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            <For each={users()}>
              {(user) => (
                <Tr>
                  <Td>{user.username}</Td>
                  <Td>{user.email}</Td>
                  <Td>
                    <Role role={user.role} />
                  </Td>
                  <Td>
                    <BasePathsDisplay user={user} />
                  </Td>
                  <Td>
                    <Wether yes={!user.disabled} />
                  </Td>
                  <Td>
                    <HStack spacing="$2">
                      <Button
                        onClick={() => {
                          to(`/@manage/users/edit/${user.id}`)
                        }}
                      >
                        {t("global.edit")}
                      </Button>
                      <DeletePopover
                        name={user.username}
                        loading={deleting() === user.id}
                        onClick={async () => {
                          const resp = await deleteUser(user.id)
                          handleResp(resp, () => {
                            notify.success(t("global.delete_success"))
                            refresh()
                          })
                        }}
                      />
                      <Button
                        colorScheme="accent"
                        loading={cancel_2faId() === user.id}
                        onClick={async () => {
                          const resp = await cancel_2fa(user.id)
                          handleResp(resp, () => {
                            notify.success(t("users.cancel_2fa_success"))
                            refresh()
                          })
                        }}
                      >
                        {t("users.cancel_2fa")}
                      </Button>
                    </HStack>
                  </Td>
                </Tr>
              )}
            </For>
          </Tbody>
        </Table>
      </Box>
    </VStack>
  )
}

export default Users
