import {
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  HStack,
  IconButton,
  Text,
  Box,
} from "@hope-ui/solid"
import { MaybeLoading, FolderChooseInput } from "~/components"
import { useFetch, useRouter, useT } from "~/hooks"
import { handleResp, notify, r } from "~/utils"
import {
  PEmptyResp,
  PResp,
  User,
  UserMethods,
  UserPermissions,
  UserBasePath,
} from "~/types"
import { createStore } from "solid-js/store"
import { For, Show, createSignal } from "solid-js"
import { me, setMe } from "~/store"
import { PublicKeys } from "./PublicKeys"
import { FaSolidPlus, FaSolidTrash } from "solid-icons/fa"

const Permission = (props: {
  can: boolean
  onChange: (val: boolean) => void
  name: string
}) => {
  const t = useT()
  return (
    <FormControl
      display="inline-flex"
      flexDirection="row"
      alignItems="center"
      gap="$2"
      rounded="$md"
      shadow="$md"
      p="$2"
      w="fit-content"
    >
      <FormLabel mb="0">{t(`users.permissions.${props.name}`)}</FormLabel>
      <Checkbox
        checked={props.can}
        onChange={() => props.onChange(!props.can)}
      />
    </FormControl>
  )
}

const AddOrEdit = () => {
  const t = useT()
  const { params, back } = useRouter()
  const { id } = params
  const [user, setUser] = createStore<User>({
    id: 0,
    username: "",
    password: "",
    email: "",
    base_path: "",
    base_paths: [],
    role: 0,
    permission: 0,
    disabled: false,
    sso_id: "",
  })
  const [useMultiplePaths, setUseMultiplePaths] = createSignal(false)
  const [userLoading, loadUser] = useFetch(
    (): PResp<User> => r.get(`/admin/user/get?id=${id}`),
  )

  const initEdit = async () => {
    const resp = await loadUser()
    handleResp(resp, (userData) => {
      setUser(userData)
      // Check if user has multiple base paths
      if (userData.base_paths && userData.base_paths.length > 0) {
        setUseMultiplePaths(true)
      }
    })
  }
  if (id) {
    initEdit()
  }
  const [okLoading, ok] = useFetch((): PEmptyResp => {
    return r.post(`/admin/user/${id ? "update" : "create"}`, user)
  })
  return (
    <MaybeLoading loading={userLoading()}>
      <VStack w="$full" alignItems="start" spacing="$2">
        <Heading>{t(`global.${id ? "edit" : "add"}`)}</Heading>
        <Show when={!UserMethods.is_guest(user)}>
          <FormControl w="$full" display="flex" flexDirection="column" required>
            <FormLabel for="username" display="flex" alignItems="center">
              {t(`users.username`)}
            </FormLabel>
            <Input
              id="username"
              value={user.username}
              onInput={(e) => setUser("username", e.currentTarget.value)}
            />
          </FormControl>
          <FormControl w="$full" display="flex" flexDirection="column" required>
            <FormLabel for="password" display="flex" alignItems="center">
              {t(`users.password`)}
            </FormLabel>
            <Input
              id="password"
              type="password"
              placeholder="********"
              value={user.password}
              onInput={(e) => setUser("password", e.currentTarget.value)}
            />
          </FormControl>
          <FormControl w="$full" display="flex" flexDirection="column">
            <FormLabel for="email" display="flex" alignItems="center">
              {t(`users.email`)}
            </FormLabel>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={user.email}
              onInput={(e) => setUser("email", e.currentTarget.value)}
            />
          </FormControl>
        </Show>

        <FormControl w="$full" display="flex" flexDirection="column">
          <FormLabel display="flex" alignItems="center" gap="$2">
            {t(`users.base_path_mode`)}
            <Checkbox
              checked={useMultiplePaths()}
              onChange={(e: any) => {
                const isMultiple = e.currentTarget.checked
                setUseMultiplePaths(isMultiple)
                if (!isMultiple) {
                  // Switch to single path mode
                  setUser("base_paths", [])
                } else {
                  // Switch to multiple paths mode
                  if (user.base_path) {
                    const now = new Date().toISOString()
                    setUser("base_paths", [
                      {
                        id: 0,
                        user_id: user.id,
                        path: user.base_path,
                        alias: "默认存储",
                        order: 0,
                        enable_capacity_limit: false,
                        capacity_limit_gb: 0,
                        used_bytes: 0,
                        total_bytes: 0,
                        created_at: now,
                        updated_at: now,
                      },
                    ])
                  }
                }
              }}
            >
              {t(`users.use_multiple_paths`)}
            </Checkbox>
          </FormLabel>
        </FormControl>

        <Show when={!useMultiplePaths()}>
          <FormControl w="$full" display="flex" flexDirection="column" required>
            <FormLabel for="base_path" display="flex" alignItems="center">
              {t(`users.base_path`)}
            </FormLabel>
            <FolderChooseInput
              id="base_path"
              value={user.base_path}
              onChange={(path) => setUser("base_path", path)}
              onlyFolder
            />
          </FormControl>
        </Show>

        <Show when={useMultiplePaths()}>
          <FormControl w="$full" display="flex" flexDirection="column">
            <FormLabel
              display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              {t(`users.base_paths`)}
              <IconButton
                aria-label="Add base path"
                icon={<FaSolidPlus />}
                size="sm"
                colorScheme="success"
                onClick={() => {
                  const now = new Date().toISOString()
                  const newPath: UserBasePath = {
                    id: 0,
                    user_id: user.id,
                    path: "",
                    alias: `存储空间${user.base_paths.length + 1}`,
                    order: user.base_paths.length,
                    enable_capacity_limit: false,
                    capacity_limit_gb: 0,
                    used_bytes: 0,
                    total_bytes: 0,
                    created_at: now,
                    updated_at: now,
                  }
                  setUser("base_paths", [...user.base_paths, newPath])
                }}
              />
            </FormLabel>
            <VStack w="$full" spacing="$2">
              <For each={user.base_paths}>
                {(basePath, index) => (
                  <Box
                    w="$full"
                    p="$3"
                    border="1px solid $neutral6"
                    rounded="$md"
                  >
                    <VStack spacing="$2">
                      <HStack w="$full" justifyContent="space-between">
                        <Text fontWeight="bold">存储空间 {index() + 1}</Text>
                        <IconButton
                          aria-label="Remove base path"
                          icon={<FaSolidTrash />}
                          size="sm"
                          colorScheme="danger"
                          onClick={() => {
                            const newPaths = user.base_paths.filter(
                              (_, i) => i !== index(),
                            )
                            setUser("base_paths", newPaths)
                          }}
                        />
                      </HStack>
                      <FormControl w="$full">
                        <FormLabel>别名</FormLabel>
                        <Input
                          value={basePath.alias}
                          placeholder={`存储空间${index() + 1}`}
                          onInput={(e) => {
                            setUser(
                              "base_paths",
                              index(),
                              "alias",
                              e.currentTarget.value,
                            )
                          }}
                        />
                      </FormControl>
                      <FormControl w="$full">
                        <FormLabel>路径</FormLabel>
                        <FolderChooseInput
                          value={basePath.path}
                          onChange={(path) => {
                            setUser("base_paths", index(), "path", path)
                          }}
                          onlyFolder
                        />
                      </FormControl>
                      <FormControl w="$full">
                        <FormLabel display="flex" alignItems="center" gap="$2">
                          <Checkbox
                            checked={basePath.enable_capacity_limit}
                            onChange={(e: any) => {
                              setUser(
                                "base_paths",
                                index(),
                                "enable_capacity_limit",
                                e.currentTarget.checked,
                              )
                            }}
                          >
                            {t(`users.enable_capacity_limit`)}
                          </Checkbox>
                        </FormLabel>
                        <Show when={basePath.enable_capacity_limit}>
                          <HStack spacing="$2" alignItems="center">
                            <Input
                              type="number"
                              min="0.1"
                              max="10000"
                              step="0.1"
                              value={basePath.capacity_limit_gb}
                              placeholder="100"
                              onInput={(e) => {
                                const value = parseFloat(e.currentTarget.value) || 0
                                setUser(
                                  "base_paths",
                                  index(),
                                  "capacity_limit_gb",
                                  value,
                                )
                              }}
                              w="120px"
                            />
                            <Text fontSize="$sm" color="$neutral11">
                              GB
                            </Text>
                          </HStack>
                        </Show>
                      </FormControl>
                    </VStack>
                  </Box>
                )}
              </For>
            </VStack>
          </FormControl>
        </Show>
        <FormControl w="$full" required>
          <FormLabel display="flex" alignItems="center">
            {t(`users.permission`)}
          </FormLabel>
          <Flex w="$full" wrap="wrap" gap="$2">
            <For each={UserPermissions}>
              {(item, i) => (
                <Permission
                  name={item}
                  can={UserMethods.can(user, i())}
                  onChange={(val) => {
                    if (val) {
                      setUser("permission", (user.permission |= 1 << i()))
                    } else {
                      setUser("permission", (user.permission &= ~(1 << i())))
                    }
                  }}
                />
              )}
            </For>
          </Flex>
        </FormControl>
        <FormControl w="fit-content" display="flex">
          <Checkbox
            css={{ whiteSpace: "nowrap" }}
            id="disabled"
            onChange={(e: any) => setUser("disabled", e.currentTarget.checked)}
            color="$neutral10"
            fontSize="$sm"
            checked={user.disabled}
          >
            {t(`users.disabled`)}
          </Checkbox>
        </FormControl>
        <Button
          loading={okLoading()}
          onClick={async () => {
            const resp = await ok()
            // TODO maybe can use handleRespWithNotifySuccess
            handleResp(resp, async () => {
              notify.success(t("global.save_success"))
              if (user.username === me().username)
                handleResp(await r.get("/me"), setMe)
              back()
            })
          }}
        >
          {t(`global.${id ? "save" : "add"}`)}
        </Button>
        <Show when={id && !UserMethods.is_guest(user)}>
          <PublicKeys isMine={false} userId={parseInt(id)} />
        </Show>
      </VStack>
    </MaybeLoading>
  )
}

export default AddOrEdit
