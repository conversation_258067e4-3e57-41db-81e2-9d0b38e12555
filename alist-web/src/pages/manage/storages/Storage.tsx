import {
  Badge,
  Box,
  Button,
  HStack,
  Progress,
  ProgressIndicator,
  Td,
  Text,
  Tr,
  useColorModeValue,
  VStack,
} from "@hope-ui/solid"
import { useFetch, useRouter, useT } from "~/hooks"
import { getMainColor } from "~/store"
import { PEmptyResp, Storage } from "~/types"
import { handleResp, handleRespWithNotifySuccess, notify, r } from "~/utils"
import { DeletePopover } from "../common/DeletePopover"

interface StorageProps {
  storage: Storage
  refresh: () => void
}

// 格式化容量大小
function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 B"
  const k = 1024
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

function StorageOp(props: StorageProps) {
  const t = useT()
  const { to } = useRouter()
  const [deleteLoading, deleteStorage] = useFetch(
    (): PEmptyResp => r.post(`/admin/storage/delete?id=${props.storage.id}`),
  )
  const [enableOrDisableLoading, enableOrDisable] = useFetch(
    (): PEmptyResp =>
      r.post(
        `/admin/storage/${props.storage.disabled ? "enable" : "disable"}?id=${
          props.storage.id
        }`,
      ),
  )
  return (
    <>
      <Button
        onClick={() => {
          to(`/@manage/storages/edit/${props.storage.id}`)
        }}
      >
        {t("global.edit")}
      </Button>
      <Button
        loading={enableOrDisableLoading()}
        colorScheme={props.storage.disabled ? "success" : "warning"}
        onClick={async () => {
          const resp = await enableOrDisable()
          handleRespWithNotifySuccess(resp, () => {
            props.refresh()
          })
        }}
      >
        {t(`global.${props.storage.disabled ? "enable" : "disable"}`)}
      </Button>
      <DeletePopover
        name={props.storage.mount_path}
        loading={deleteLoading()}
        onClick={async () => {
          const resp = await deleteStorage()
          handleResp(resp, () => {
            notify.success(t("global.delete_success"))
            props.refresh()
          })
        }}
      />
    </>
  )
}

export function StorageGridItem(props: StorageProps) {
  const t = useT()

  // 计算容量使用百分比
  const getCapacityPercentage = () => {
    if (
      !props.storage.capacity_info ||
      props.storage.capacity_info.total === 0
    ) {
      return 0
    }
    return Math.round(
      (props.storage.capacity_info.used / props.storage.capacity_info.total) *
        100,
    )
  }

  return (
    <VStack
      w="$full"
      spacing="$2"
      rounded="$lg"
      border="1px solid $neutral7"
      background={useColorModeValue("$neutral2", "$neutral3")()}
      // alignItems="start"
      p="$3"
      _hover={{
        border: `1px solid ${getMainColor()}`,
      }}
    >
      <HStack spacing="$2" w="$full" justifyContent="space-between">
        <Text
          fontWeight="$medium"
          css={{
            wordBreak: "break-all",
          }}
        >
          {props.storage.mount_path}
        </Text>
        <Badge colorScheme="info">
          {t(`drivers.drivers.${props.storage.driver}`)}
        </Badge>
      </HStack>

      {/* 账号信息 */}
      {props.storage.account_info && (
        <HStack w="$full">
          <Text fontSize="$sm" color="$neutral11">
            {t("storages.account")}:&nbsp;
          </Text>
          <Text fontSize="$sm" css={{ wordBreak: "break-all" }}>
            {props.storage.account_info.nickname ||
              props.storage.account_info.username}
          </Text>
        </HStack>
      )}

      {/* 容量信息 */}
      {props.storage.capacity_info ? (
        props.storage.capacity_info.total > 0 ? (
          <VStack w="$full" spacing="$1">
            <HStack w="$full" justifyContent="space-between">
              <Text fontSize="$sm" color="$neutral11">
                {t("storages.capacity")}:
              </Text>
              <Text fontSize="$sm">
                {formatBytes(props.storage.capacity_info.used)} /{" "}
                {formatBytes(props.storage.capacity_info.total)} (
                {getCapacityPercentage()}%)
              </Text>
            </HStack>
            <Progress
              value={getCapacityPercentage()}
              max={100}
              size="sm"
              w="$full"
              colorScheme={
                getCapacityPercentage() > 90
                  ? "danger"
                  : getCapacityPercentage() > 70
                    ? "warning"
                    : "success"
              }
            >
              <ProgressIndicator />
            </Progress>
          </VStack>
        ) : (
          <HStack w="$full">
            <Text fontSize="$sm" color="$neutral11">
              {t("storages.capacity")}:
            </Text>
            <Text fontSize="$sm" color="$neutral9">
              获取失败
            </Text>
          </HStack>
        )
      ) : null}

      <HStack w="$full">
        <Text fontSize="$sm" color="$neutral11">
          {t("storages.common.status")}:&nbsp;
        </Text>
        <Box
          fontSize="$sm"
          css={{ wordBreak: "break-all" }}
          overflowX="auto"
          innerHTML={props.storage.status}
        />
      </HStack>

      {props.storage.remark && (
        <Text
          fontSize="$sm"
          css={{ wordBreak: "break-all" }}
          color="$neutral10"
        >
          {props.storage.remark}
        </Text>
      )}

      <HStack spacing="$2" w="$full" justifyContent="flex-end">
        <StorageOp {...props} />
      </HStack>
    </VStack>
  )
}

export function StorageListItem(props: StorageProps) {
  const t = useT()

  // 计算容量使用百分比
  const getCapacityPercentage = () => {
    if (
      !props.storage.capacity_info ||
      props.storage.capacity_info.total === 0
    ) {
      return 0
    }
    return Math.round(
      (props.storage.capacity_info.used / props.storage.capacity_info.total) *
        100,
    )
  }

  return (
    <Tr>
      <Td>{props.storage.mount_path}</Td>
      <Td>{t(`drivers.drivers.${props.storage.driver}`)}</Td>

      {/* 账号信息列 */}
      <Td>
        {props.storage.account_info ? (
          <VStack spacing="$1" alignItems="start">
            <Text fontSize="$sm" fontWeight="$medium">
              {props.storage.account_info.username}
            </Text>
            {props.storage.account_info.email && (
              <Text fontSize="$xs" color="$neutral10">
                {props.storage.account_info.email}
              </Text>
            )}
          </VStack>
        ) : (
          <Text fontSize="$sm" color="$neutral9">
            未获取
          </Text>
        )}
      </Td>

      {/* 容量信息列 */}
      <Td>
        {props.storage.capacity_info ? (
          props.storage.capacity_info.total > 0 ? (
            <VStack spacing="$1" alignItems="start" w="$full">
              <Text fontSize="$xs">
                {formatBytes(props.storage.capacity_info.used)} /{" "}
                {formatBytes(props.storage.capacity_info.total)}
              </Text>
              <Progress
                value={getCapacityPercentage()}
                max={100}
                size="sm"
                w="100px"
                colorScheme={
                  getCapacityPercentage() > 90
                    ? "danger"
                    : getCapacityPercentage() > 70
                      ? "warning"
                      : "success"
                }
              >
                <ProgressIndicator />
              </Progress>
              <Text fontSize="$xs" color="$neutral10">
                {getCapacityPercentage()}%
              </Text>
            </VStack>
          ) : (
            <Text fontSize="$sm" color="$neutral9">
              获取失败
            </Text>
          )
        ) : (
          <Text fontSize="$sm" color="$neutral9">
            未获取
          </Text>
        )}
      </Td>

      <Td>{props.storage.order}</Td>
      <Td>{props.storage.status}</Td>
      <Td>{props.storage.remark}</Td>
      <Td>
        <HStack spacing="$2">
          <StorageOp {...props} />
        </HStack>
      </Td>
    </Tr>
  )
}
