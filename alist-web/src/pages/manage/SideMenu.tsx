import {
  <PERSON>,
  Flex,
  <PERSON>ing,
  HStack,
  Icon,
  VStack,
  useColorModeValue,
} from "@hope-ui/solid"
import { createMemo, createSignal, For, Match, Show, Switch } from "solid-js"
import { useRouter, useT } from "~/hooks"
import { BiSolidRightArrow } from "solid-icons/bi"
import { onClose } from "./Header"
import { UserMethods, UserRole } from "~/types"
import { me } from "~/store"
import { AnchorWithBase } from "~/components"
import { Link } from "@solidjs/router"
import { hoverColor, joinBase } from "~/utils"
import { IconTypes } from "solid-icons"

export interface SideMenuItemProps {
  title: string
  to: string
  icon?: IconTypes
  children?: SideMenuItemProps[]
  role?: number
  external?: true
  refresh?: true
}

const SideMenuItem = (props: SideMenuItemProps) => {
  const ifShow = createMemo(() => {
    if (!UserMethods.is_admin(me())) {
      if (props.role === undefined) return false
      else if (props.role === UserRole.GENERAL && !UserMethods.is_general(me()))
        return false
    }
    return true
  })
  return (
    <Switch fallback={<SideMenuItemWithTo {...props} />}>
      <Match when={!ifShow()}>{null}</Match>
      <Match when={props.children}>
        <SideMenuItemWithChildren {...props} />
      </Match>
    </Switch>
  )
}

const SideMenuItemWithTo = (props: SideMenuItemProps) => {
  const { pathname } = useRouter()
  const t = useT()
  const isActive = () => {
    const currentPath = pathname()
    // 特殊处理'我的文件'菜单（to: "/"）
    if (props.to === "/") {
      // 当路径是根路径或者是文件夹路径（不是其他特定功能页面）时，'我的文件'应该是选中状态
      return (
        currentPath === "/" ||
        (currentPath.startsWith("/") &&
          !currentPath.startsWith("/shared") &&
          !currentPath.startsWith("/@manage"))
      )
    }
    return currentPath === props.to
  }
  return (
    <AnchorWithBase
      cancelBase={props.to.startsWith("http")}
      display="flex"
      as={Link}
      href={props.to}
      onClick={(e: any) => {
        // to(props.to!);
        onClose()
        if (props.refresh) {
          e.stopPropagation?.()
          let to = props.to
          if (!to.startsWith("http")) {
            to = joinBase(to)
          }
          window.open(to, "_self")
        }
      }}
      w="$full"
      alignItems="center"
      _hover={{
        bgColor: isActive() ? "#3b82f6" : "#1e3a8a",
        textDecoration: "none",
        color: "#ffffff",
      }}
      px="$3"
      py="$2_5"
      rounded="$lg"
      cursor="pointer"
      bgColor={isActive() ? "#3b82f6" : ""}
      color={useColorModeValue(
        isActive() ? "$info11" : "",
        isActive() ? "#ffffff" : "#9ca3af",
      )()}
      external={props.external}
      // _active={{ transform: "scale(.95)", transition: "0.1s" }}
    >
      <Show when={props.icon}>
        {<Icon mr="$3" as={props.icon} boxSize="$5" />}
      </Show>
      <Heading fontSize="$base" fontWeight="$medium">
        {t(props.title)}
      </Heading>
    </AnchorWithBase>
  )
}

const SideMenuItemWithChildren = (props: SideMenuItemProps) => {
  const { pathname } = useRouter()
  const [open, setOpen] = createSignal(pathname().includes(props.to))
  const t = useT()
  return (
    <Box w="$full">
      <Flex
        justifyContent="space-between"
        onClick={() => {
          setOpen(!open())
        }}
        w="$full"
        alignItems="center"
        _hover={{
          bgColor: "#1e3a8a",
        }}
        px="$3"
        py="$2_5"
        rounded="$lg"
        cursor="pointer"
        color={useColorModeValue("", "#9ca3af")()}
      >
        <HStack>
          <Show when={props.icon}>
            {<Icon mr="$3" as={props.icon} boxSize="$5" />}
          </Show>
          <Heading fontSize="$base" fontWeight="$medium">
            {t(props.title)}
          </Heading>
        </HStack>
        <Icon
          as={BiSolidRightArrow}
          transform={open() ? "rotate(90deg)" : "none"}
          transition="transform 0.2s"
        />
      </Flex>
      <Show when={open()}>
        <Box pl="$2">
          <SideMenu items={props.children!} />
        </Box>
      </Show>
    </Box>
  )
}

export const SideMenu = (props: { items: SideMenuItemProps[] }) => {
  return (
    <VStack
      p="$4"
      w="$full"
      color={useColorModeValue("$neutral11", "#ffffff")()}
      spacing="$2"
    >
      <For each={props.items}>
        {(item) => {
          return <SideMenuItem {...item} />
        }}
      </For>
    </VStack>
  )
}
