/* 防止水平滚动条的全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  overflow-x: hidden;
  overflow-y: hidden; /* 禁用body的垂直滚动条 */
  max-width: 100vw;
  height: 100vh; /* 确保body高度为视窗高度 */
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-corner,
::-webkit-scrollbar-track {
  background-color: #e2e2e2;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background-color: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-button:vertical {
  display: none;
}

::-webkit-scrollbar-thumb:vertical:hover {
  background-color: rgba(0, 0, 0, 0.35);
}

::-webkit-scrollbar-thumb:vertical:active {
  background-color: rgba(0, 0, 0, 0.38);
}

/* dark */

.hope-ui-dark ::-webkit-scrollbar-corner,
.hope-ui-dark ::-webkit-scrollbar-track {
  background-color: rgb(15, 15, 15);
}

.hope-ui-dark ::-webkit-scrollbar-thumb {
  background-color: #2d2d2d;
}

.hope-ui-dark ::-webkit-scrollbar-thumb:vertical:hover {
  background-color: rgb(58, 58, 58);
}

.hope-ui-dark ::-webkit-scrollbar-thumb:vertical:active {
  background-color: rgb(58, 58, 58);
}

.hope-select__option {
  flex-shrink: 0;
}

/* 文件列表页面滚动条样式 - 默认隐藏，滚动时显示 */
body.file-list-container {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

body.file-list-container::-webkit-scrollbar {
  width: 6px;
}

body.file-list-container::-webkit-scrollbar-track {
  background: transparent;
}

body.file-list-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s ease;
}

/* 滚动时显示滚动条 */
body.file-list-container.scrolling {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

body.file-list-container.scrolling::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

body.file-list-container.scrolling::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 暗色主题下的滚动条 */
body.hope-ui-dark.file-list-container.scrolling {
  scrollbar-color: #2d2d2d transparent;
}

body.hope-ui-dark.file-list-container.scrolling::-webkit-scrollbar-thumb {
  background: #2d2d2d;
}

body.hope-ui-dark.file-list-container.scrolling::-webkit-scrollbar-thumb:hover {
  background: rgb(58, 58, 58);
}

/* 网格视图复选框悬停显示 */
.grid-item-checkbox {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.grid-item:hover .grid-item-checkbox {
  opacity: 1;
}

/* 选中状态的复选框始终显示 */
.grid-item.selected .grid-item-checkbox {
  opacity: 1;
}

/* 确保网格视图正确布局 */
.viselect-container {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 网格布局优化 */
.viselect-container[style*="grid"] {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 确保网格项目不超出容器 */
.grid-item {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 可变内容区域滚动条样式 */
.content-area-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.content-area-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.content-area-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.content-area-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.content-area-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 暗色主题下的可变内容区域滚动条 */
.hope-ui-dark .content-area-scrollbar {
  scrollbar-color: #2d2d2d transparent;
}

.hope-ui-dark .content-area-scrollbar::-webkit-scrollbar-thumb {
  background: #2d2d2d;
}

.hope-ui-dark .content-area-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(58, 58, 58);
}
