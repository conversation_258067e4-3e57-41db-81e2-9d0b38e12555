import { globalCss, HopeThemeConfig } from "@hope-ui/solid"
import { hoverColor } from "~/utils"

const theme: HopeThemeConfig = {
  initialColorMode: "dark",
  lightTheme: {
    colors: {
      // background: "$neutral2",
      background: "#f7f8fa",
    },
  },
  darkTheme: {
    colors: {
      background: "#0f1629",
      // 模态窗口相关颜色定义
      neutral1: "#0f1629", // 模态窗口背景色
      neutral2: "#0f1629", // 模态窗口内容背景色
      neutral6: "#374151", // 默认边框色 (深灰色)
      neutral7: "#374151", // 边框色变体
      info8: "#1e3a8a", // 聚焦边框色
      info9: "#3b82f6", // 主要按钮背景色
      info10: "#3b82f6", // 主要按钮背景色变体
    },
  },
  components: {
    Button: {
      baseStyle: {
        root: {
          rounded: "$lg",
          _active: {
            transform: "scale(.95)",
            transition: "0.2s",
          },
          _focus: {
            boxShadow: "unset",
          },
        },
      },
      defaultProps: {
        root: {
          colorScheme: "info",
          variant: "subtle",
        },
      },
      variants: {
        solid: {
          root: {
            _dark: {
              bgColor: "$info9",
              color: "white",
              _hover: {
                bgColor: "$info10",
              },
            },
          },
        },
      },
    },
    IconButton: {
      defaultProps: {
        colorScheme: "info",
        variant: "subtle",
      },
    },
    Input: {
      baseStyle: {
        input: {
          rounded: "$lg",
          _focus: {
            boxShadow: "unset",
            borderColor: "$info8",
          },
          _dark: {
            borderColor: "$neutral6",
            _focus: {
              borderColor: "$info8",
              boxShadow: "0 0 0 1px $info8",
            },
          },
        },
      },
      defaultProps: {
        input: {
          variant: "filled",
        },
      },
    },
    Textarea: {
      baseStyle: {
        rounded: "$lg",
        _focus: {
          boxShadow: "unset",
          borderColor: "$info8",
        },
        resize: "vertical",
        wordBreak: "break-all",
        _dark: {
          borderColor: "$neutral6",
          _focus: {
            borderColor: "$info8",
            boxShadow: "0 0 0 1px $info8",
          },
        },
      },
      defaultProps: {
        variant: "filled",
      },
    },
    Select: {
      baseStyle: {
        trigger: {
          rounded: "$lg",
          _focus: {
            boxShadow: "unset",
            borderColor: "$info8",
          },
          _dark: {
            borderColor: "$neutral6",
            _focus: {
              borderColor: "$info8",
              boxShadow: "0 0 0 1px $info8",
            },
          },
        },
        content: {
          border: "none",
          rounded: "$lg",
          _dark: {
            bgColor: "$neutral1",
            borderColor: "$neutral6",
            border: "1px solid $neutral6",
          },
        },
        optionIndicator: {
          color: "$info10",
        },
      },
      defaultProps: {
        root: {
          variant: "filled",
        },
      },
    },
    Checkbox: {
      defaultProps: {
        root: {
          colorScheme: "info",
          variant: "filled",
        },
      },
    },
    Switch: {
      defaultProps: {
        root: {
          colorScheme: "info",
        },
      },
    },
    Menu: {
      baseStyle: {
        content: {
          rounded: "$md",
          minW: "unset",
          border: "unset",
          // py: "0",
        },
        item: {
          rounded: "$md",
          py: "$1",
          // mx: "0",
        },
      },
    },
    Notification: {
      baseStyle: {
        root: {
          rounded: "$lg",
          border: "unset",
        },
      },
    },
    Alert: {
      baseStyle: {
        root: {
          rounded: "$lg",
        },
      },
    },
    Anchor: {
      baseStyle: {
        rounded: "$lg",
        px: "$1_5",
        py: "$1",
        _hover: {
          bgColor: hoverColor(),
          textDecoration: "none",
        },
        _focus: {
          boxShadow: "unset",
        },
        _active: { transform: "scale(.95)", transition: "0.1s" },
      },
    },
    Modal: {
      baseStyle: {
        content: {
          rounded: "$lg",
          bgColor: "$neutral1",
          borderColor: "$neutral6",
          borderWidth: "1px",
          borderStyle: "solid",
          overflow: "hidden", // 确保内容不会超出圆角边界
        },
        overlay: {
          bgColor: "rgba(0, 0, 0, 0.6)",
        },
        header: {
          bgColor: "$neutral1",
          borderBottomColor: "$neutral6",
          borderBottomWidth: "1px",
          borderBottomStyle: "solid",
          roundedTop: "$lg", // 只有顶部圆角
          roundedBottom: "0", // 底部无圆角
        },
        body: {
          bgColor: "$neutral1",
          rounded: "0", // 中间部分无圆角
        },
        footer: {
          bgColor: "$neutral1",
          borderTopColor: "$neutral6",
          borderTopWidth: "1px",
          borderTopStyle: "solid",
          roundedTop: "0", // 顶部无圆角
          roundedBottom: "$lg", // 只有底部圆角
        },
      },
    },
  },
}

export const globalStyles = globalCss({
  "*": {
    margin: 0,
    padding: 0,
  },
  html: {
    fontFamily: `-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol" !important`,
  },

  "#root": {
    display: "flex",
    flexDirection: "column",
    alignItems: "stretch", // 让子元素占满宽度
    width: "100%",
    maxWidth: "100vw", // 防止超出视窗宽度
    overflowX: "hidden", // 防止水平滚动条
    minHeight: "100vh",
  },
  ".hope-breadcrumb__list": {
    flexWrap: "wrap",
    rowGap: "0 !important",
  },
  ".lightgallery-container": {
    "& .lg-backdrop": {
      zIndex: "$popover",
    },
    "& .lg-outer": {
      zIndex: "calc($popover + 10)",
    },
  },
  ".viselect-selection-area": {
    background: "rgba(46, 115, 252, 0.11)",
    border: "2px solid rgba(98, 155, 255, 0.81)",
    borderRadius: "0.1em",
  },
  ".viselect-container": {
    userSelect: "none",
    "& .viselect-item": {
      "-webkit-user-drag": "none",
      "& img": {
        "-webkit-user-drag": "none",
      },
    },
  },
  // 模态窗口圆角优化
  ".hope-modal__content": {
    borderRadius: "var(--hope-radii-lg)",
    overflow: "hidden",
    border: "1px solid var(--hope-colors-neutral6)",
  },
  ".hope-ui-dark .hope-modal__content": {
    backgroundColor: "var(--hope-colors-neutral1)",
    borderColor: "var(--hope-colors-neutral6)",
  },
  ".hope-modal__header": {
    borderTopLeftRadius: "var(--hope-radii-lg)",
    borderTopRightRadius: "var(--hope-radii-lg)",
    borderBottomLeftRadius: "0",
    borderBottomRightRadius: "0",
  },
  ".hope-modal__footer": {
    borderTopLeftRadius: "0",
    borderTopRightRadius: "0",
    borderBottomLeftRadius: "var(--hope-radii-lg)",
    borderBottomRightRadius: "var(--hope-radii-lg)",
  },
})

export { theme }
