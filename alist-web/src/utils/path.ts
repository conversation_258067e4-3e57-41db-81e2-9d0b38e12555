import { base_path } from "."
import { UserMethods } from "~/types"

export const standardizePath = (path: string, noRootSlash?: boolean) => {
  if (path.endsWith("/")) {
    path = path.slice(0, -1)
  }
  if (!path.startsWith("/")) {
    path = "/" + path
  }
  if (noRootSlash && path === "/") {
    return ""
  }
  return path
}

export const pathResolve = (...paths: string[]) => {
  return new URL(pathJoin(...paths), location.origin).pathname
}

export const pathJoin = (...paths: string[]) => {
  return paths.join("/").replace(/\/{2,}/g, "/")
}

export const joinBase = (...paths: string[]) => {
  return pathJoin(base_path, ...paths)
}

export const trimBase = (path: string) => {
  const res = path.replace(base_path, "")
  if (res.startsWith("/")) {
    return res
  }
  return "/" + res
}

export const pathBase = (path: string) => {
  return path.split("/").pop()
}

export const pathDir = (path: string) => {
  return path.split("/").slice(0, -1).join("/")
}

export const encodePath = (path: string, all?: boolean) => {
  return path
    .split("/")
    .map((p) =>
      all
        ? encodeURIComponent(p)
        : p
            .replace(/%/g, "%25")
            .replace(/\?/g, "%3F")
            .replace(/#/g, "%23")
            .replace(/ /g, "%20"),
    )
    .join("/")
}

export const ext = (path: string): string => {
  return path.split(".").pop() ?? ""
}

export const baseName = (fullName: string) => {
  return fullName.split(".").slice(0, -1).join(".")
}

export function createMatcher(path: string) {
  const segments = path.split("/").filter(Boolean)
  const len = segments.length

  return (location: string) => {
    const locSegments = location.split("/").filter(Boolean)
    const lenDiff = locSegments.length - len
    if (lenDiff < 0) return null

    let matchPath = len ? "" : "/"

    for (let i = 0; i < len; i++) {
      const segment = segments[i]
      const locSegment = locSegments[i]

      if (
        segment.localeCompare(locSegment, undefined, {
          sensitivity: "base",
        }) !== 0
      ) {
        return null
      }
      matchPath += `/${locSegment}`
    }

    return matchPath
  }
}

/**
 * 根据用户类型和当前路径确定文件夹选择器的根路径
 * @param user 当前用户
 * @param currentPath 当前路径
 * @returns 文件夹选择器应该使用的根路径
 */
export const getFolderTreeRootPath = (user: any, currentPath: string): string => {
  // 如果没有用户信息，返回根路径
  if (!user) {
    return "/"
  }

  // 管理员可以访问所有路径
  if (UserMethods.is_admin(user)) {
    return "/"
  }

  // 多基础路径用户
  if (user.base_paths && user.base_paths.length > 1) {
    // 检查当前路径是否为虚拟存储空间路径
    if (currentPath.startsWith("/virtual/storage/")) {
      // 解析虚拟路径：/virtual/storage/N/actual/path
      const parts = currentPath.split("/").filter(Boolean)
      if (parts.length >= 3 && parts[0] === "virtual" && parts[1] === "storage") {
        const storageIndex = parseInt(parts[2]) - 1 // 转换为0基索引

        // 获取对应的基础路径
        if (storageIndex >= 0 && storageIndex < user.base_paths.length) {
          const virtualRootPath = `/virtual/storage/${storageIndex + 1}`
          // 返回虚拟存储空间的根路径
          return virtualRootPath
        }
      }
    }
    // 如果在根路径，返回根路径（显示虚拟存储空间列表）
    return "/"
  }

  // 单基础路径用户：限制在自己的基础路径内
  if (user.base_path) {
    return user.base_path
  }

  // 如果有base_paths但只有一个
  if (user.base_paths && user.base_paths.length === 1) {
    return user.base_paths[0].path
  }

  // 默认返回根路径
  return "/"
}
