/**
 * 文本处理工具函数
 */

/**
 * 截断文件名，保持扩展名完整
 * @param filename 原始文件名
 * @param maxLength 最大长度（默认20个字符）
 * @returns 截断后的文件名
 */
export function truncateFilename(
  filename: string,
  maxLength: number = 20,
): string {
  if (filename.length <= maxLength) {
    return filename
  }

  // 查找最后一个点的位置（扩展名分隔符）
  const lastDotIndex = filename.lastIndexOf(".")

  // 如果没有扩展名或者扩展名太长，直接截断
  if (lastDotIndex === -1 || filename.length - lastDotIndex > maxLength / 2) {
    return filename.substring(0, maxLength - 3) + "..."
  }

  // 分离文件名和扩展名
  const name = filename.substring(0, lastDotIndex)
  const extension = filename.substring(lastDotIndex)

  // 计算文件名部分可用的长度
  const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

  if (availableLength <= 0) {
    return filename.substring(0, maxLength - 3) + "..."
  }

  return name.substring(0, availableLength) + "..." + extension
}

/**
 * 截断普通文本
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength - 3) + "..."
}
