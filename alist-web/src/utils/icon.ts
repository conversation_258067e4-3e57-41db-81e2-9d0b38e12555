import {
  BsFileEarmarkWordFill,
  BsFileEarmarkExcelFill,
  BsFileEarmarkPptFill,
  BsFileEarmarkPdfFill,
  BsFileEarmarkPlayFill,
  BsFileEarmarkMusicFill,
  BsFileEarmarkFontFill,
  BsFileEarmarkImageFill,
  BsFileEarmarkMinusFill,
  BsApple,
  BsWindows,
  BsFileEarmarkZipFill,
  BsMarkdownFill,
  BsHddNetwork,
} from "solid-icons/bs"
import {
  FaSolidDatabase,
  FaSolidBook,
  FaSolidCompactDisc,
  FaSolidLink,
} from "solid-icons/fa"
import { IoFolder } from "solid-icons/io"
import { ImAndroid } from "solid-icons/im"
import { Obj, ObjType } from "~/types"
import { ext } from "./path"
import {
  VscodeIconsFileTypeAi2,
  VscodeIconsFileTypePhotoshop2,
} from "~/components"
import { SiAsciinema } from "solid-icons/si"
import { isArchive } from "~/store/archive"

const iconMap = {
  "dmg,ipa,plist,tipa": { icon: BsApple, color: "#8b9dc3" }, // Apple 生态 - 银灰色
  "exe,msi": { icon: BsWindows, color: "#00a1f1" }, // Windows 生态 - 天蓝色
  apk: { icon: ImAndroid, color: "#3ddc84" }, // Android 应用 - 绿色
  db: { icon: FaSolidDatabase, color: "#1e40af" }, // 数据库文件 - 深蓝色
  md: { icon: BsMarkdownFill, color: "#1d4ed8" }, // 文档类 - 深蓝色
  epub: { icon: FaSolidBook, color: "#92400e" }, // 电子书 - 棕色
  iso: { icon: FaSolidCompactDisc, color: "#7c3aed" }, // 光盘镜像 - 紫色
  m3u8: { icon: BsFileEarmarkPlayFill, color: "#dc2626" }, // 流媒体 - 红色
  "doc,docx": { icon: BsFileEarmarkWordFill, color: "#2563eb" }, // Word 文档 - 蓝色
  "xls,xlsx": { icon: BsFileEarmarkExcelFill, color: "#16a34a" }, // Excel 表格 - 绿色
  "ppt,pptx": { icon: BsFileEarmarkPptFill, color: "#ea580c" }, // PowerPoint - 橙色
  pdf: { icon: BsFileEarmarkPdfFill, color: "#dc2626" }, // PDF 文档 - 红色
  psd: { icon: VscodeIconsFileTypePhotoshop2, color: "#9333ea" }, // 设计软件 - 紫色
  ai: { icon: VscodeIconsFileTypeAi2, color: "#9333ea" }, // 设计软件 - 紫色
  url: { icon: FaSolidLink, color: "#0891b2" }, // 链接文件 - 青色
  cast: { icon: SiAsciinema, color: "#ca8a04" }, // 终端录制 - 黄色
}

// 文件类型颜色映射
const typeColorMap = {
  [ObjType.FOLDER]: "#3b82f6", // 文件夹 - 蓝色
  [ObjType.VIDEO]: "#ef4444", // 视频文件 - 红色
  [ObjType.AUDIO]: "#8b5cf6", // 音频文件 - 紫色
  [ObjType.TEXT]: "#6b7280", // 文本文件 - 灰色
  [ObjType.IMAGE]: "#ec4899", // 图片文件 - 粉色
  [ObjType.VIRTUAL_STORAGE]: "#10b981", // 虚拟存储空间 - 绿色
  [ObjType.UNKNOWN]: "#9ca3af", // 未知文件 - 灰色
}

// 新的函数：返回图标和颜色信息
export const getIconAndColorByTypeAndName = (
  type: number,
  name: string,
): { icon: any; color: string } => {
  if (type !== ObjType.FOLDER) {
    // 检查特定扩展名映射
    for (const [extensions, iconInfo] of Object.entries(iconMap)) {
      if (extensions.split(",").includes(ext(name).toLowerCase())) {
        return iconInfo
      }
    }
    // 检查压缩文件
    if (isArchive(name)) {
      return { icon: BsFileEarmarkZipFill, color: "#f59e0b" } // 压缩文件 - 橙色
    }
  }

  // 根据文件类型返回图标和颜色
  switch (type) {
    case ObjType.FOLDER:
      return { icon: IoFolder, color: typeColorMap[ObjType.FOLDER] }
    case ObjType.VIRTUAL_STORAGE:
      return {
        icon: BsHddNetwork,
        color: typeColorMap[ObjType.VIRTUAL_STORAGE],
      }
    case ObjType.VIDEO:
      return { icon: BsFileEarmarkPlayFill, color: typeColorMap[ObjType.VIDEO] }
    case ObjType.AUDIO:
      return {
        icon: BsFileEarmarkMusicFill,
        color: typeColorMap[ObjType.AUDIO],
      }
    case ObjType.TEXT:
      return { icon: BsFileEarmarkFontFill, color: typeColorMap[ObjType.TEXT] }
    case ObjType.IMAGE:
      return {
        icon: BsFileEarmarkImageFill,
        color: typeColorMap[ObjType.IMAGE],
      }
    default:
      return {
        icon: BsFileEarmarkMinusFill,
        color: typeColorMap[ObjType.UNKNOWN],
      }
  }
}

// 保持向后兼容的原函数
export const getIconByTypeAndName = (type: number, name: string) => {
  return getIconAndColorByTypeAndName(type, name).icon
}

// 新的函数：返回对象的图标和颜色信息
export const getIconAndColorByObj = (
  obj: Pick<Obj, "type" | "name">,
): { icon: any; color: string } => {
  return getIconAndColorByTypeAndName(obj.type, obj.name)
}

// 保持向后兼容的原函数
export const getIconByObj = (obj: Pick<Obj, "type" | "name">) => {
  return getIconByTypeAndName(obj.type, obj.name)
}
