// 智能上传管理器
export class SmartUploadManager {
  private maxConcurrency: number = 3;
  private currentConcurrency: number = 0;
  private queue: (() => Promise<any>)[] = [];
  private networkSpeed: number = 0;
  private lastSpeedUpdate: number = Date.now();

  constructor(initialConcurrency: number = 3) {
    this.maxConcurrency = initialConcurrency;
    this.startNetworkMonitoring();
  }

  // 网络监控
  private startNetworkMonitoring() {
    // 每30秒调整一次并发数
    setInterval(() => {
      this.adjustConcurrency();
    }, 30000);
  }

  // 根据网络速度动态调整并发数
  private adjustConcurrency() {
    const speedMbps = this.networkSpeed / (1024 * 1024) * 8; // 转换为Mbps
    
    let newConcurrency: number;
    if (speedMbps > 50) {
      newConcurrency = 6; // 高速网络
    } else if (speedMbps > 20) {
      newConcurrency = 5; // 中高速网络
    } else if (speedMbps > 10) {
      newConcurrency = 4; // 中速网络
    } else if (speedMbps > 5) {
      newConcurrency = 3; // 中低速网络
    } else {
      newConcurrency = 2; // 低速网络
    }

    if (newConcurrency !== this.maxConcurrency) {
      console.log(`调整并发数: ${this.maxConcurrency} -> ${newConcurrency} (网速: ${speedMbps.toFixed(1)}Mbps)`);
      this.maxConcurrency = newConcurrency;
    }
  }

  // 更新网络速度
  updateNetworkSpeed(bytesPerSecond: number) {
    const now = Date.now();
    if (now - this.lastSpeedUpdate > 1000) { // 每秒最多更新一次
      this.networkSpeed = bytesPerSecond;
      this.lastSpeedUpdate = now;
    }
  }

  // 添加任务到队列
  async add<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await task();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      this.processQueue();
    });
  }

  // 处理队列
  private async processQueue() {
    if (this.currentConcurrency >= this.maxConcurrency || this.queue.length === 0) {
      return;
    }

    const task = this.queue.shift();
    if (!task) return;

    this.currentConcurrency++;

    try {
      await task();
    } finally {
      this.currentConcurrency--;
      this.processQueue(); // 处理下一个任务
    }
  }

  // 获取队列状态
  getStatus() {
    return {
      maxConcurrency: this.maxConcurrency,
      currentConcurrency: this.currentConcurrency,
      queueLength: this.queue.length,
      networkSpeed: this.networkSpeed
    };
  }

  // 清空队列
  clear() {
    this.queue = [];
  }
}

// 文件优先级计算
export const calculateFilePriority = (file: File): number => {
  let priority = 0;

  // 1. 文件大小优先级（小文件优先）
  if (file.size < 1024 * 1024) { // < 1MB
    priority += 100;
  } else if (file.size < 10 * 1024 * 1024) { // < 10MB
    priority += 80;
  } else if (file.size < 100 * 1024 * 1024) { // < 100MB
    priority += 60;
  } else {
    priority += 40; // >= 100MB
  }

  // 2. 文件类型优先级
  const highPriorityTypes = ['image/', 'text/', 'application/pdf'];
  const mediumPriorityTypes = ['audio/', 'application/zip', 'application/x-zip-compressed'];
  
  if (highPriorityTypes.some(type => file.type.startsWith(type))) {
    priority += 50;
  } else if (mediumPriorityTypes.some(type => file.type.startsWith(type))) {
    priority += 30;
  } else {
    priority += 10; // 其他类型
  }

  // 3. 扩展名优先级
  const commonExtensions = /\.(jpg|jpeg|png|gif|txt|pdf|doc|docx)$/i;
  if (commonExtensions.test(file.name)) {
    priority += 20;
  }

  return priority;
};

// 文件排序函数
export const sortFilesByPriority = (files: File[]): File[] => {
  return files.sort((a, b) => {
    const priorityA = calculateFilePriority(a);
    const priorityB = calculateFilePriority(b);
    return priorityB - priorityA; // 高优先级在前
  });
};

// 性能监控器
export class PerformanceMonitor {
  private memoryWarningThreshold = 500 * 1024 * 1024; // 500MB
  private lastMemoryCheck = 0;

  // 检查内存使用
  checkMemoryUsage(): boolean {
    const now = Date.now();
    if (now - this.lastMemoryCheck < 5000) return true; // 5秒检查一次

    this.lastMemoryCheck = now;

    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      
      if (usedMB > this.memoryWarningThreshold / 1024 / 1024) {
        console.warn(`内存使用过高: ${usedMB.toFixed(1)}MB`);
        return false;
      }
    }

    return true;
  }

  // 获取性能指标
  getPerformanceMetrics() {
    const metrics: any = {
      timestamp: Date.now()
    };

    if ('memory' in performance) {
      const memory = (performance as any).memory;
      metrics.memory = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
      };
    }

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      metrics.network = {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      };
    }

    return metrics;
  }
}

// 全局实例
export const globalUploadManager = new SmartUploadManager();
export const globalPerformanceMonitor = new PerformanceMonitor();
