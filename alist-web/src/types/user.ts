export enum UserRole {
  GENERAL,
  GUEST,
  ADMIN,
}

export interface UserBasePath {
  id: number
  user_id: number
  path: string
  alias: string
  order: number
  enable_capacity_limit: boolean
  capacity_limit_gb: number
  used_bytes: number
  total_bytes: number
  created_at: string
  updated_at: string
}

export interface User {
  id: number
  username: string
  password: string
  email: string
  base_path: string // for backward compatibility
  base_paths: UserBasePath[] // multiple base paths
  role: UserRole
  permission: number
  sso_id: string
  disabled: boolean
  // otp: boolean;
}

// 容量初始化进度
export interface CapacityInitializationProgress {
  base_path: string
  status: 'not_started' | 'running' | 'completed' | 'failed'
  progress: number // 0.0 - 1.0
  current_path?: string
  processed_files: number
  total_files: number
  processed_bytes: number
  start_time: string
  end_time?: string
  error?: string
}

// 容量状态
export interface CapacityStatus {
  base_path: string
  used_bytes: number
  total_files: number
  last_calculated_at: string
  calculation_status: string
  limit_gb: number
  limit_bytes: number
  error?: string
}

// API响应类型
export interface CapacityInitializationResponse {
  message: string
  paths: string[]
}

export interface CapacityProgressResponse {
  progress: Record<string, CapacityInitializationProgress>
  all_completed: boolean
}

export interface CapacityStatusResponse {
  status: Record<string, CapacityStatus>
}

export interface CapacityCheckResponse {
  paths_need_initialization: string[]
  paths_with_existing_data: string[]
  total_capacity_paths: number
}

export const UserPermissions = [
  "see_hides",
  "access_without_password",
  "offline_download",
  "write",
  "rename",
  "move",
  "copy",
  "delete",
  "webdav_read",
  "webdav_manage",
  "ftp_read",
  "ftp_manage",
  "read_archives",
  "decompress",
] as const

export const UserMethods = {
  is_guest: (user: User) => user.role === UserRole.GUEST,
  is_admin: (user: User) => user.role === UserRole.ADMIN,
  is_general: (user: User) => user.role === UserRole.GENERAL,
  can: (user: User, permission: number) => {
    return ((user.permission >> permission) & 1) == 1
  },
  // Multi base path methods
  get_base_paths: (user: User): UserBasePath[] => {
    if (user.base_paths && user.base_paths.length > 0) {
      return user.base_paths.sort((a, b) => a.order - b.order)
    }
    // Fallback to single base_path for backward compatibility
    if (user.base_path) {
      const now = new Date().toISOString()
      return [
        {
          id: 0,
          user_id: user.id,
          path: user.base_path,
          alias: "默认存储",
          order: 0,
          enable_capacity_limit: false,
          capacity_limit_gb: 0,
          used_bytes: 0,
          total_bytes: 0,
          created_at: now,
          updated_at: now,
        },
      ]
    }
    return []
  },
  has_multiple_base_paths: (user: User): boolean => {
    return UserMethods.get_base_paths(user).length > 1
  },
  get_virtual_storage_name: (user: User, index: number): string => {
    const basePaths = UserMethods.get_base_paths(user)
    if (index >= 0 && index < basePaths.length) {
      return basePaths[index].alias || `存储空间${index + 1}`
    }
    return `存储空间${index + 1}`
  },
  // can_see_hides: (user: User) =>
  //   UserMethods.is_admin(user) || (user.permission & 1) == 1,
  // can_access_without_password: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 1) & 1) == 1,
  // can_offline_download_tasks: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 2) & 1) == 1,
  // can_write: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 3) & 1) == 1,
  // can_rename: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 4) & 1) == 1,
  // can_move: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 5) & 1) == 1,
  // can_copy: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 6) & 1) == 1,
  // can_remove: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 7) & 1) == 1,
  // can_webdav_read: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 8) & 1) == 1,
  // can_webdav_manage: (user: User) =>
  //   UserMethods.is_admin(user) || ((user.permission >> 9) & 1) == 1,
}
