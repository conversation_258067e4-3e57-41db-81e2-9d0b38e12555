{"allow_indexed": "Allow indexed", "allow_mounted": "Allow mounted", "announcement": "Announcement", "aria2_secret": "Aria2 secret", "aria2_uri": "Aria2 uri", "audio_autoplay": "Audio autoplay", "audio_cover": "Audio cover", "audio_types": "Audio types", "auto_update_index": "Auto update index", "copy_task_threads_num": "Copy task threads num", "customize_body": "Customize body", "customize_head": "Customize head", "decompress_download_task_threads_num": "Decompress download task threads num", "decompress_upload_task_threads_num": "Decompress upload task threads num", "default_page_size": "Default page size", "external_previews": "External previews", "favicon": "Favicon", "filename_char_mapping": "Filename char mapping", "filter_readme_scripts": "Filter readme scripts", "forward_direct_link_params": "Forward direct link params", "ftp_implicit_tls": "Ftp implicit tls", "ftp_mandatory_tls": "Ftp mandatory tls", "ftp_pasv_port_map": "Ftp pasv port map", "ftp_proxy_user_agent": "Ftp proxy user agent", "ftp_public_host": "Ftp public host", "ftp_tls_private_key_path": "Ftp tls private key path", "ftp_tls_public_cert_path": "Ftp tls public cert path", "hide_files": "Hide files", "home_container": "Home container", "home_containers": {"hope_container": "Hope container", "max_980px": "Max 980px"}, "home_icon": "Home icon", "iframe_previews": "Iframe previews", "ignore_direct_link_params": "Ignore direct link params", "ignore_paths": "Ignore paths", "ignore_paths-tips": "one path per line", "image_types": "Image types", "index_progress": "Index progress", "ldap_default_dir": "Ldap default dir", "ldap_default_permission": "Ldap default permission", "ldap_login_enabled": "Ldap login enabled", "ldap_login_tips": "Ldap login tips", "ldap_manager_dn": "Ldap manager dn", "ldap_manager_password": "Ldap manager password", "ldap_server": "Ldap server", "ldap_user_search_base": "Ldap user search base", "ldap_user_search_filter": "Ldap user search filter", "link_expiration": "Link expiration", "logo": "Logo", "main_color": "Main color", "max_client_download_speed": "Max client download speed", "max_client_upload_speed": "Max client upload speed", "max_index_depth": "Max index depth", "max_index_depth-tips": "max depth of index", "max_server_download_speed": "Max server download speed", "max_server_upload_speed": "Max server upload speed", "ocr_api": "Ocr api", "offline_download_task_threads_num": "Offline download task threads num", "offline_download_transfer_task_threads_num": "Offline download transfer task threads num", "package_download": "Package download", "pagination_type": "Pagination type", "pagination_types": {"all": "All", "auto_load_more": "Auto load more", "load_more": "Load more", "pagination": "Pagination"}, "preview_archives_by_default": "Preview archives by default", "privacy_regs": "Privacy regs", "proxy_ignore_headers": "Proxy ignore headers", "proxy_types": "Proxy types", "qbittorrent_seedtime": "Qbittorrent seedtime", "qbittorrent_url": "Qbittorrent url", "readme_autorender": "Readme autorender", "robots_txt": "Robots txt", "s3_access_key_id": "S3 access key id", "s3_buckets": "S3 buckets", "s3_secret_access_key": "S3 secret access key", "search_index": "Search index", "search_indexs": {"bleve": "Bleve", "database": "Database", "database_non_full_text": "Database non full text", "meilisearch": "<PERSON><PERSON><PERSON><PERSON>", "none": "None"}, "settings_layout": "Settings layout", "settings_layouts": {"list": "List", "responsive": "Responsive"}, "sign_all": "Sign all", "site_title": "Site title", "sso_application_name": "Sso application name", "sso_auto_register": "Sso auto register", "sso_client_id": "Sso client id", "sso_client_secret": "Sso client secret", "sso_compatibility_mode": "Sso compatibility mode", "sso_default_dir": "Sso default dir", "sso_default_permission": "Sso default permission", "sso_endpoint_name": "Sso endpoint name", "sso_extra_scopes": "Sso extra scopes", "sso_jwt_public_key": "Sso jwt public key", "sso_login_enabled": "Sso login enabled", "sso_login_platform": "Sso login platform", "sso_login_platforms": {"Casdoor": "Casdoor", "Dingtalk": "<PERSON><PERSON><PERSON>", "Github": "<PERSON><PERSON><PERSON>", "Google": "Google", "Microsoft": "Microsoft", "OIDC": "OIDC"}, "sso_oidc_username_key": "Sso oidc username key", "sso_organization_name": "Sso organization name", "text_types": "Text types", "token": "Token", "transmission_seedtime": "Transmission seedtime", "transmission_uri": "Transmission uri", "upload_task_threads_num": "Upload task threads num", "version": "Version", "video_autoplay": "Video autoplay", "video_types": "Video types", "webauthn_login_enabled": "Webauthn login enabled"}