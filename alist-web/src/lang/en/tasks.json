{"offline_download": "Download file to local machine", "offline_download_transfer": "Transfer downloaded file to corresponding storage", "upload": "Upload file to corresponding storage", "copy": "Copy file from a storage to another storage", "decompress": "Download and decompress an archive file", "decompress_upload": "Upload extracted file into target storage", "done": "Completed", "undone": "Running", "clear_succeeded": "Clear Succeeded", "retry": "Retry", "retry_failed": "Retry Failed", "state": {"0": "Pending", "1": "Running", "2": "Succeeded", "3": "Canceling", "4": "Canceled", "5": "Errored", "6": "Failing", "7": "Failed", "8": "WaitingRetry", "9": "BeforeRetry"}, "retry_selected": "Retry Selected", "cancel_selected": "Cancel Selected", "delete_selected": "Delete Selected", "filter": "Filter", "expand": "Expand", "fold": "Fold", "expand_all": "Expand All", "fold_all": "Fold All", "attr": {"name": "Name", "creator": "Creator", "state": "State", "progress": "Progress", "speed": "Speed", "operation": "Operation", "copy": {"src": "Source Path", "dst": "Destination Path"}, "upload": {"path": "Path"}, "offline_download": {"url": "URL", "path": "Destination Path", "transfer_src": "Source Path", "transfer_src_local": "Source Path (Local)", "transfer_dst": "Destination Path"}, "decompress": {"src": "Source Path", "dst": "Destination Path", "inner": "Inner Path", "password": "Extraction Password"}, "time_elapsed": "Time Elapsed", "status": "Status", "err": "Error"}, "show_only_mine": "Show only my tasks"}