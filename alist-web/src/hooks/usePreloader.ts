import { createSignal, onCleanup } from "solid-js"
import { UserMethods } from "~/types"
import { me } from "~/store"
import { fsList } from "~/utils/api"

interface PreloadItem {
  path: string
  priority: number
  timestamp: number
}

// 预加载队列
const preloadQueue = new Map<string, PreloadItem>()
const preloadingPaths = new Set<string>()

// 预加载配置
const PRELOAD_CONFIG = {
  maxConcurrent: 2, // 最大并发预加载数
  maxQueueSize: 10, // 最大队列大小
  priorityThreshold: 5, // 优先级阈值
  debounceTime: 300, // 防抖时间
}

export const usePreloader = () => {
  const [isPreloading, setIsPreloading] = createSignal(false)
  
  // 添加到预加载队列
  const addToPreloadQueue = (path: string, priority: number = 1) => {
    // 检查是否已经在队列中或正在预加载
    if (preloadQueue.has(path) || preloadingPaths.has(path)) {
      return
    }
    
    // 检查队列大小
    if (preloadQueue.size >= PRELOAD_CONFIG.maxQueueSize) {
      // 移除优先级最低的项目
      let lowestPriority = Infinity
      let lowestKey = ""
      
      for (const [key, item] of preloadQueue.entries()) {
        if (item.priority < lowestPriority) {
          lowestPriority = item.priority
          lowestKey = key
        }
      }
      
      if (lowestKey && priority > lowestPriority) {
        preloadQueue.delete(lowestKey)
      } else {
        return // 当前项目优先级不够高
      }
    }
    
    preloadQueue.set(path, {
      path,
      priority,
      timestamp: Date.now(),
    })
    
    processPreloadQueue()
  }
  
  // 处理预加载队列
  const processPreloadQueue = async () => {
    if (preloadingPaths.size >= PRELOAD_CONFIG.maxConcurrent) {
      return
    }
    
    // 按优先级排序
    const sortedItems = Array.from(preloadQueue.values())
      .sort((a, b) => b.priority - a.priority)
    
    for (const item of sortedItems) {
      if (preloadingPaths.size >= PRELOAD_CONFIG.maxConcurrent) {
        break
      }
      
      if (item.priority >= PRELOAD_CONFIG.priorityThreshold) {
        preloadPath(item.path)
      }
    }
  }
  
  // 预加载指定路径
  const preloadPath = async (path: string) => {
    if (preloadingPaths.has(path)) {
      return
    }
    
    preloadingPaths.add(path)
    preloadQueue.delete(path)
    setIsPreloading(true)
    
    try {
      // 静默预加载，不更新UI状态
      await fsList(path, "", 1, 30, false)
      console.log(`预加载完成: ${path}`)
    } catch (error) {
      console.warn(`预加载失败: ${path}`, error)
    } finally {
      preloadingPaths.delete(path)
      
      if (preloadingPaths.size === 0) {
        setIsPreloading(false)
      }
      
      // 继续处理队列
      setTimeout(processPreloadQueue, 100)
    }
  }
  
  // 预加载常用路径
  const preloadCommonPaths = () => {
    const user = me()
    
    if (UserMethods.has_multiple_base_paths(user)) {
      // 为多基础路径用户预加载第一个存储空间
      const basePaths = UserMethods.get_base_paths(user)
      if (basePaths.length > 0) {
        addToPreloadQueue("/virtual/storage/1", 8)
      }
    } else {
      // 为单基础路径用户预加载根目录的子文件夹
      addToPreloadQueue("/", 6)
    }
  }
  
  // 基于用户行为的智能预加载
  const smartPreload = (currentPath: string) => {
    // 预加载父目录
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/'
    if (parentPath !== currentPath) {
      addToPreloadQueue(parentPath, 4)
    }
    
    // 预加载兄弟目录（如果在虚拟存储空间中）
    if (currentPath.startsWith('/virtual/storage/')) {
      const match = currentPath.match(/\/virtual\/storage\/(\d+)/)
      if (match) {
        const currentIndex = parseInt(match[1])
        const user = me()
        const basePaths = UserMethods.get_base_paths(user)
        
        // 预加载下一个存储空间
        if (currentIndex < basePaths.length) {
          addToPreloadQueue(`/virtual/storage/${currentIndex + 1}`, 3)
        }
        
        // 预加载上一个存储空间
        if (currentIndex > 1) {
          addToPreloadQueue(`/virtual/storage/${currentIndex - 1}`, 3)
        }
      }
    }
  }
  
  // 清除预加载队列
  const clearPreloadQueue = () => {
    preloadQueue.clear()
    preloadingPaths.clear()
    setIsPreloading(false)
  }
  
  // 获取预加载统计
  const getPreloadStats = () => ({
    queueSize: preloadQueue.size,
    preloadingCount: preloadingPaths.size,
    isPreloading: isPreloading(),
  })
  
  return {
    addToPreloadQueue,
    preloadCommonPaths,
    smartPreload,
    clearPreloadQueue,
    getPreloadStats,
    isPreloading,
  }
}
