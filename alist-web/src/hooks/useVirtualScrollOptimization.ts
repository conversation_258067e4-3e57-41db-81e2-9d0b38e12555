import { createSignal, createEffect, onCleanup } from "solid-js"

interface VirtualScrollMetrics {
  totalItems: number
  visibleItems: number
  scrollPosition: number
  renderTime: number
  isScrolling: boolean
}

export const useVirtualScrollOptimization = () => {
  const [metrics, setMetrics] = createSignal<VirtualScrollMetrics>({
    totalItems: 0,
    visibleItems: 0,
    scrollPosition: 0,
    renderTime: 0,
    isScrolling: false,
  })
  
  const [isScrolling, setIsScrolling] = createSignal(false)
  let scrollTimer: number | undefined
  
  // 滚动状态管理
  const handleScrollStart = () => {
    setIsScrolling(true)
    setMetrics(prev => ({ ...prev, isScrolling: true }))
  }
  
  const handleScrollEnd = () => {
    setIsScrolling(false)
    setMetrics(prev => ({ ...prev, isScrolling: false }))
  }
  
  // 滚动事件处理（带防抖）
  const onScroll = (scrollTop: number) => {
    handleScrollStart()
    
    setMetrics(prev => ({ ...prev, scrollPosition: scrollTop }))
    
    // 清除之前的定时器
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    // 设置新的定时器，200ms后认为滚动结束
    scrollTimer = setTimeout(handleScrollEnd, 200)
  }
  
  // 更新渲染指标
  const updateRenderMetrics = (totalItems: number, visibleItems: number, renderTime: number) => {
    setMetrics(prev => ({
      ...prev,
      totalItems,
      visibleItems,
      renderTime,
    }))
  }
  
  // 获取优化建议
  const getOptimizationSuggestions = () => {
    const currentMetrics = metrics()
    const suggestions: string[] = []
    
    if (currentMetrics.renderTime > 16) {
      suggestions.push("渲染时间过长，考虑减少预渲染项目数")
    }
    
    if (currentMetrics.visibleItems > 50) {
      suggestions.push("可见项目过多，考虑增加项目高度")
    }
    
    if (currentMetrics.totalItems > 1000) {
      suggestions.push("数据量很大，建议启用服务端分页")
    }
    
    return suggestions
  }
  
  // 计算最优配置
  const getOptimalConfig = () => {
    const currentMetrics = metrics()
    
    // 根据总项目数动态调整配置
    let itemHeight = 65
    let overscan = 3
    
    if (currentMetrics.totalItems > 500) {
      itemHeight = 70 // 增加项目高度，减少可见项目数
      overscan = 2   // 减少预渲染项目数
    }
    
    if (currentMetrics.totalItems > 1000) {
      itemHeight = 75
      overscan = 1
    }
    
    return { itemHeight, overscan }
  }
  
  // 性能监控
  const startPerformanceMonitoring = () => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const renderEntries = entries.filter(entry => 
        entry.name.includes('virtual-scroll') || entry.entryType === 'measure'
      )
      
      if (renderEntries.length > 0) {
        const avgRenderTime = renderEntries.reduce((sum, entry) => 
          sum + entry.duration, 0) / renderEntries.length
        
        setMetrics(prev => ({ ...prev, renderTime: avgRenderTime }))
      }
    })
    
    try {
      observer.observe({ entryTypes: ['measure', 'navigation'] })
    } catch (error) {
      console.warn('Performance monitoring not supported:', error)
    }
    
    return observer
  }
  
  onCleanup(() => {
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
  })
  
  return {
    metrics,
    isScrolling,
    onScroll,
    updateRenderMetrics,
    getOptimizationSuggestions,
    getOptimalConfig,
    startPerformanceMonitoring,
  }
}
