import axios, { Canceler } from "axios"
import {
  appendObjs,
  password,
  ObjStore,
  State,
  getPagination,
  objStore,
  getHistoryKey,
  hasHistory,
  recoverHistory,
  clearHistory,
  me,
} from "~/store"
import {
  fsGet,
  fsList,
  handleRespWithoutNotify,
  log,
  notify,
  pathJoin,
} from "~/utils"
import { useFetch } from "./useFetch"
import { useRouter } from "./useRouter"
import { UserMethods } from "~/types"
import { usePreloader } from "./usePreloader"

let first_fetch = true

let cancelObj: Canceler
let cancelList: Canceler

const IsDirRecord: Record<string, boolean> = {}

// 增强的路径缓存机制，用于提升多基础路径用户的性能
interface CacheItem {
  data: any
  timestamp: number
  ttl: number
  priority: number // 缓存优先级
}

// 缓存配置
const CACHE_CONFIG = {
  virtualStorage: 5 * 60 * 1000, // 虚拟存储空间缓存5分钟
  fileList: 2 * 60 * 1000,       // 文件列表缓存2分钟
  userInfo: 10 * 60 * 1000,      // 用户信息缓存10分钟
  default: 30 * 1000             // 默认缓存30秒
}

const pathCache = new Map<string, CacheItem>()

// 将 pathCache 暴露到全局，以便在用户切换时清除
declare global {
  interface Window {
    pathCache: Map<string, CacheItem>
  }
}
window.pathCache = pathCache

const getCachedData = (path: string, customTtl?: number): any => {
  const cached = pathCache.get(path)
  if (cached) {
    const ttl = customTtl || cached.ttl
    if (Date.now() - cached.timestamp < ttl) {
      // 更新访问时间，提高缓存优先级
      cached.priority = Date.now()
      return cached.data
    }
    pathCache.delete(path)
  }
  return null
}

const setCachedData = (path: string, data: any, customTtl?: number): void => {
  // 智能选择TTL
  let ttl = customTtl
  if (!ttl) {
    if (path.includes('virtual') || path === '/' || path === '') {
      ttl = CACHE_CONFIG.virtualStorage
    } else if (path.includes('file_list')) {
      ttl = CACHE_CONFIG.fileList
    } else if (path.includes('user_info')) {
      ttl = CACHE_CONFIG.userInfo
    } else {
      ttl = CACHE_CONFIG.default
    }
  }

  pathCache.set(path, {
    data,
    timestamp: Date.now(),
    ttl,
    priority: Date.now()
  })

  // 智能清理缓存，优先删除低优先级的过期缓存
  if (pathCache.size > 100) {
    const now = Date.now()
    const entries = Array.from(pathCache.entries())

    // 先删除过期的缓存
    for (const [key, item] of entries) {
      if (now - item.timestamp > item.ttl) {
        pathCache.delete(key)
      }
    }

    // 如果还是太多，删除优先级最低的
    if (pathCache.size > 80) {
      const sortedEntries = Array.from(pathCache.entries())
        .sort((a, b) => a[1].priority - b[1].priority)

      for (let i = 0; i < 20 && i < sortedEntries.length; i++) {
        pathCache.delete(sortedEntries[i][0])
      }
    }
  }
}

// 清除特定类型的缓存
const clearCacheByPattern = (pattern: string) => {
  for (const key of pathCache.keys()) {
    if (key.includes(pattern)) {
      pathCache.delete(key)
    }
  }
}

let globalPage = 1
export const getGlobalPage = () => {
  return globalPage
}
export const setGlobalPage = (page: number) => {
  globalPage = page
  // console.log("setGlobalPage", globalPage)
}
export const resetGlobalPage = () => {
  setGlobalPage(1)
}
export const usePath = () => {
  const { pathname, to, searchParams } = useRouter()
  const { smartPreload } = usePreloader()

  const [, getObj] = useFetch((path: string) =>
    fsGet(
      path,
      password(),
      new axios.CancelToken((c) => {
        cancelObj = c
      }),
    ),
  )
  const pagination = getPagination()
  if (pagination.type === "pagination") {
    setGlobalPage(parseInt(searchParams["page"]) || 1)
  }
  const [, getObjs] = useFetch(
    (arg?: {
      path: string
      index?: number
      size?: number
      force?: boolean
    }) => {
      const page = {
        index: arg?.index,
        size: arg?.size,
      }
      // setSearchParams(page);
      return fsList(
        arg?.path,
        password(),
        page.index,
        page.size,
        arg?.force,
        new axios.CancelToken((c) => {
          cancelList = c
        }),
      )
    },
  )
  // set a path must be a dir
  const setPathAs = (path: string, dir = true, push = false) => {
    if (push) {
      path = pathJoin(pathname(), path)
    }
    if (dir) {
      IsDirRecord[path] = true
    } else {
      delete IsDirRecord[path]
    }
  }

  // record is second time password is wrong
  let retry_pass = false
  // handle pathname change
  // if confirm current path is dir, fetch List directly
  // if not, fetch get then determine if it is dir or file
  const handlePathChange = (
    path: string,
    index?: number,
    rp?: boolean,
    force?: boolean,
  ) => {
    cancelObj?.()
    cancelList?.()
    retry_pass = rp ?? false
    ObjStore.setErr("")
    if (hasHistory(path, index)) {
      log(`handle [${getHistoryKey(path, index)}] from history`)
      return recoverHistory(path, index)
    } else if (IsDirRecord[path]) {
      log(`handle [${getHistoryKey(path, index)}] as folder`)
      return handleFolder(path, index, undefined, undefined, force)
    } else {
      log(`handle [${getHistoryKey(path, index)}] as obj`)
      return handleObj(path, index)
    }
  }

  // handle enter obj that don't know if it is dir or file
  const handleObj = async (path: string, index?: number) => {
    // Check if this is a virtual storage space path
    if (path.startsWith("/virtual/storage/")) {
      // Virtual storage space paths are always directories
      setPathAs(path)
      return handleFolder(path, index)
    }

    // Check if this is root path for multi-base-path users
    const user = me()
    if (
      (path === "/" || path === "") &&
      UserMethods.has_multiple_base_paths(user)
    ) {
      // Root path for multi-base-path users should be treated as directory (virtual storage spaces)
      setPathAs(path)
      return handleFolder(path, index)
    }

    ObjStore.setState(State.FetchingObj)
    const resp = await getObj(path)
    handleRespWithoutNotify(
      resp,
      (data) => {
        ObjStore.setObj(data)
        ObjStore.setProvider(data.provider)
        if (data.is_dir) {
          setPathAs(path)
          handleFolder(path, index)
        } else {
          ObjStore.setReadme(data.readme)
          ObjStore.setHeader(data.header)
          ObjStore.setRelated(data.related ?? [])
          ObjStore.setRawUrl(data.raw_url)
          ObjStore.setState(State.File)
        }
      },
      handleErr,
    )
  }

  // change enter a folder or turn page or load more
  const handleFolder = async (
    path: string,
    index?: number,
    size?: number,
    append = false,
    force?: boolean,
  ) => {
    // 增强的缓存检查，支持更多场景
    if (!force && !append) {
      const cacheKey = `file_list_${path}_${index || 1}_${size || 'all'}`
      const cached = getCachedData(cacheKey)
      if (cached) {
        log(`使用缓存数据加载路径: ${path}`)
        ObjStore.setObjs(cached.content ?? [])
        ObjStore.setTotal(cached.total)
        ObjStore.setReadme(cached.readme)
        ObjStore.setHeader(cached.header)
        ObjStore.setWrite(cached.write)
        ObjStore.setProvider(cached.provider)
        ObjStore.setState(State.Folder)
        setGlobalPage(index ?? 1)
        return
      }
    }

    if (!size) {
      size = pagination.size
    }
    if (size !== undefined && pagination.type === "all") {
      size = undefined
    }
    ObjStore.setState(append ? State.FetchingMore : State.FetchingObjs)
    const resp = await getObjs({ path, index, size, force })
    handleRespWithoutNotify(
      resp,
      (data) => {
        setGlobalPage(index ?? 1)
        if (append) {
          appendObjs(data.content)
        } else {
          ObjStore.setObjs(data.content ?? [])
          ObjStore.setTotal(data.total)

          // 智能缓存文件列表数据
          const cacheKey = `file_list_${path}_${index || 1}_${size || 'all'}`
          setCachedData(cacheKey, data) // 使用智能TTL选择
          log(`缓存文件列表数据: ${path}`)
        }
        ObjStore.setReadme(data.readme)
        ObjStore.setHeader(data.header)
        ObjStore.setWrite(data.write)
        ObjStore.setProvider(data.provider)
        ObjStore.setState(State.Folder)

        // 触发智能预加载
        smartPreload(path)
      },
      handleErr,
    )
  }

  const handleErr = (msg: string, code?: number) => {
    if (code === 403) {
      ObjStore.setState(State.NeedPassword)
      if (retry_pass) {
        notify.error(msg)
      }
    } else {
      const basePath = me().base_path
      if (
        first_fetch &&
        basePath != "/" &&
        pathname().includes(basePath) &&
        msg.endsWith("object not found")
      ) {
        first_fetch = false
        to(pathname().replace(basePath, ""))
        return
      }
      if (code === undefined || code >= 0) {
        ObjStore.setErr(msg)
      }
    }
  }
  const loadMore = () => {
    return handleFolder(pathname(), globalPage + 1, undefined, true)
  }
  return {
    handlePathChange: handlePathChange,
    setPathAs: setPathAs,
    refresh: async (retry_pass?: boolean, force?: boolean) => {
      const path = pathname()
      const scroll = window.scrollY
      clearHistory(path, globalPage)
      if (
        pagination.type === "load_more" ||
        pagination.type === "auto_load_more"
      ) {
        const page = globalPage
        resetGlobalPage()
        await handlePathChange(path, globalPage, retry_pass, force)
        while (globalPage < page) {
          await loadMore()
        }
      } else {
        await handlePathChange(path, globalPage, retry_pass, force)
      }
      window.scroll({ top: scroll, behavior: "smooth" })
    },
    loadMore: loadMore,
    allLoaded: () => globalPage >= Math.ceil(objStore.total / pagination.size),
  }
}
