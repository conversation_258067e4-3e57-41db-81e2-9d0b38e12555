name: Format

on:
  push:
    branches: [main]

jobs:
  format:
    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: pnpm install

      - name: Format
        run: pnpm run format

      - name: Add, Commit and Push
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "style: format code with prettier"
          commit_user_name: <PERSON><PERSON>
          commit_user_email: <EMAIL>
          commit_author: "<PERSON><PERSON> <<EMAIL>>"
