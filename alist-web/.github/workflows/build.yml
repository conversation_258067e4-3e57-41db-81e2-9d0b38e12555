name: build

on:
  push:
    branches:
      - main

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest]
    name: Build
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          registry-url: "https://registry.npmjs.org"

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          version: 9
          run_install: false

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main
          path: alist-web
          persist-credentials: false
          fetch-depth: 0
          submodules: recursive

      - name: Checkout dist repo
        uses: actions/checkout@v4
        with:
          repository: alist-org/web-dist
          ref: dev
          path: web-dist
          persist-credentials: false
          fetch-depth: 0

      - name: Build
        run: |
          cd alist-web
          pnpm install
          pnpm i18n:build
          pnpm build
          cd ..
        env:
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}

      - name: Commit dist
        run: |
          cd web-dist
          rm -rf dist
          cp -r ../alist-web/dist .
          git add .
          git config --local user.email "<EMAIL>"
          git config --local user.name "Noah Hsu"
          git commit --amend -m "upload dev dist"
          cd ..

      - name: Upload dist files
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.MY_TOKEN }}
          branch: dev
          directory: web-dist
          repository: alist-org/web-dist
          force: true
