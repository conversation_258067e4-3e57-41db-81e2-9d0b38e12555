package handles

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/alist-org/alist/v3/internal/capacity"
	"github.com/alist-org/alist/v3/internal/fs"
	"github.com/alist-org/alist/v3/internal/model"
	"github.com/alist-org/alist/v3/internal/op"
	"github.com/alist-org/alist/v3/pkg/generic_sync"
	"github.com/alist-org/alist/v3/server/common"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// 基础路径使用量缓存，用于提升性能
type cacheEntry struct {
	size      int64
	timestamp time.Time
}

var basePathUsageCache = generic_sync.MapOf[string, cacheEntry]{}

func ListUsers(c *gin.Context) {
	var req model.PageReq
	if err := c.ShouldBind(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	req.Validate()
	log.Debugf("%+v", req)
	users, total, err := op.GetUsers(req.Page, req.PerPage)
	if err != nil {
		common.ErrorResp(c, err, 500, true)
		return
	}

	// Enhance users with capacity information for their base paths
	enhanceUsersWithCapacityInfo(convertUsersToPointers(users))

	common.SuccessResp(c, common.PageResp{
		Content: users,
		Total:   total,
	})
}

func CreateUser(c *gin.Context) {
	var req model.User
	if err := c.ShouldBind(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	if req.IsAdmin() {
		common.ErrorStrResp(c, "admin user can not be created", 400, true)
		return
	}
	req.SetPassword(req.Password)
	req.Password = ""
	req.Authn = "[]"

	// Validate base paths
	if err := validateUserBasePaths(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}

	if err := op.CreateUser(&req); err != nil {
		common.ErrorResp(c, err, 500, true)
	} else {
		common.SuccessResp(c)
	}
}

func UpdateUser(c *gin.Context) {
	var req model.User
	if err := c.ShouldBind(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	user, err := op.GetUserById(req.ID)
	if err != nil {
		common.ErrorResp(c, err, 500)
		return
	}
	if user.Role != req.Role {
		common.ErrorStrResp(c, "role can not be changed", 400)
		return
	}
	if req.Password == "" {
		req.PwdHash = user.PwdHash
		req.Salt = user.Salt
	} else {
		req.SetPassword(req.Password)
		req.Password = ""
	}
	if req.OtpSecret == "" {
		req.OtpSecret = user.OtpSecret
	}
	if req.Disabled && req.IsAdmin() {
		common.ErrorStrResp(c, "admin user can not be disabled", 400)
		return
	}

	// Validate base paths
	if err := validateUserBasePaths(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}

	if err := op.UpdateUser(&req); err != nil {
		common.ErrorResp(c, err, 500)
	} else {
		common.SuccessResp(c)
	}
}

func DeleteUser(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	if err := op.DeleteUserById(uint(id)); err != nil {
		common.ErrorResp(c, err, 500)
		return
	}
	common.SuccessResp(c)
}

func GetUser(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	user, err := op.GetUserById(uint(id))
	if err != nil {
		common.ErrorResp(c, err, 500, true)
		return
	}
	common.SuccessResp(c, user)
}

func Cancel2FAById(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	if err := op.Cancel2FAById(uint(id)); err != nil {
		common.ErrorResp(c, err, 500)
		return
	}
	common.SuccessResp(c)
}

func DelUserCache(c *gin.Context) {
	username := c.Query("username")
	err := op.DelUserCache(username)
	if err != nil {
		common.ErrorResp(c, err, 500)
		return
	}
	common.SuccessResp(c)
}

// validateUserBasePaths validates user base paths configuration
func validateUserBasePaths(user *model.User) error {
	// If no BasePaths provided, use BasePath for backward compatibility
	if len(user.BasePaths) == 0 && user.BasePath != "" {
		return nil
	}

	// Validate each base path
	for i, basePath := range user.BasePaths {
		if basePath.Path == "" {
			return fmt.Errorf("base path %d cannot be empty", i+1)
		}

		// Set default alias if not provided
		if basePath.Alias == "" {
			user.BasePaths[i].Alias = fmt.Sprintf("存储空间%d", i+1)
		}

		// Set order if not provided
		if basePath.Order == 0 {
			user.BasePaths[i].Order = i + 1
		}

		// Validate capacity limit
		if basePath.EnableCapacityLimit {
			if basePath.CapacityLimitGB <= 0 {
				return fmt.Errorf("capacity limit for base path %d must be greater than 0", i+1)
			}
			if basePath.CapacityLimitGB > 10000 {
				return fmt.Errorf("capacity limit for base path %d cannot exceed 10000 GB", i+1)
			}
		}
	}

	return nil
}

// TestCapacityInfo 测试容量管理系统的端点
func TestCapacityInfo(c *gin.Context) {
	testPath := c.Query("path")
	if testPath == "" {
		testPath = "/baidu_jaques520/shaper/data/8844"
	}

	result := map[string]interface{}{
		"test_path": testPath,
		"status":    "testing",
	}

	log.Infof("TestCapacityInfo: 测试容量管理系统，路径: %s", testPath)

	// 1. 测试获取容量信息
	capacityInfo, err := capacity.GetCapacity(testPath)
	if err != nil {
		result["get_capacity_error"] = err.Error()
		log.Errorf("TestCapacityInfo: 获取容量失败: %v", err)
	} else {
		result["get_capacity_success"] = true
		if capacityInfo != nil {
			result["capacity_info"] = map[string]interface{}{
				"used_bytes":         capacityInfo.UsedBytes,
				"total_files":        capacityInfo.TotalFiles,
				"last_calculated_at": capacityInfo.LastCalculatedAt,
				"calculation_status": capacityInfo.CalculationStatus,
				"version":            capacityInfo.Version,
			}
			log.Infof("TestCapacityInfo: 容量信息 - 使用: %d 字节, 文件: %d 个, 状态: %s",
				capacityInfo.UsedBytes, capacityInfo.TotalFiles, capacityInfo.CalculationStatus)
		} else {
			result["capacity_info"] = nil
			log.Infof("TestCapacityInfo: 无容量信息")
		}
	}

	// 2. 测试增量更新
	err = capacity.UpdateCapacity(testPath, 1024, 1)
	if err != nil {
		result["update_capacity_error"] = err.Error()
		log.Errorf("TestCapacityInfo: 增量更新失败: %v", err)
	} else {
		result["update_capacity_success"] = true
		log.Infof("TestCapacityInfo: 增量更新成功 (+1024 字节, +1 文件)")
	}

	// 3. 再次获取容量信息
	capacityInfo2, err := capacity.GetCapacity(testPath)
	if err != nil {
		result["get_capacity2_error"] = err.Error()
	} else {
		result["get_capacity2_success"] = true
		if capacityInfo2 != nil {
			result["capacity_info2"] = map[string]interface{}{
				"used_bytes":         capacityInfo2.UsedBytes,
				"total_files":        capacityInfo2.TotalFiles,
				"last_calculated_at": capacityInfo2.LastCalculatedAt,
				"calculation_status": capacityInfo2.CalculationStatus,
				"version":            capacityInfo2.Version,
			}
			log.Infof("TestCapacityInfo: 更新后容量 - 使用: %d 字节, 文件: %d 个",
				capacityInfo2.UsedBytes, capacityInfo2.TotalFiles)
		}
	}

	// 4. 获取统计信息
	stats := capacity.GetStats()
	result["stats"] = stats
	log.Infof("TestCapacityInfo: 获取统计信息成功")

	// 5. 使缓存失效
	capacity.InvalidateCache(testPath)
	result["cache_invalidated"] = true
	log.Infof("TestCapacityInfo: 缓存已失效")

	result["status"] = "completed"
	log.Infof("TestCapacityInfo: 测试完成")
	common.SuccessResp(c, result)
}

// convertUsersToPointers converts []model.User to []*model.User
func convertUsersToPointers(users []model.User) []*model.User {
	result := make([]*model.User, len(users))
	for i := range users {
		result[i] = &users[i]
	}
	return result
}

// enhanceUsersWithCapacityInfo enhances users with capacity information for their base paths
// 使用新的容量管理系统进行实时计算和缓存
func enhanceUsersWithCapacityInfo(users []*model.User) {
	if len(users) == 0 {
		return
	}

	// 收集所有需要计算容量的基础路径，批量处理
	var pathsToCalculate []string
	pathToUserBasePath := make(map[string][]*model.UserBasePath)

	// 为每个用户的基础路径设置容量信息
	for _, user := range users {
		// 处理多基础路径用户
		if len(user.BasePaths) > 0 {
			for i := range user.BasePaths {
				basePath := &user.BasePaths[i]

				// 如果启用了容量限制，设置限制容量和计算已使用容量
				if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
					// 设置总容量为限制容量（GB转换为字节）
					basePath.TotalBytes = int64(basePath.CapacityLimitGB * 1024 * 1024 * 1024)

					// 收集需要计算的路径
					pathsToCalculate = append(pathsToCalculate, basePath.Path)
					pathToUserBasePath[basePath.Path] = append(pathToUserBasePath[basePath.Path], basePath)
				} else {
					// 未启用容量限制的基础路径，不显示容量信息
					basePath.TotalBytes = 0
					basePath.UsedBytes = 0
				}
			}
		} else if user.BasePath != "" {
			// 处理传统的单基础路径用户 - 不显示容量信息
			// 因为传统用户没有容量限制设置
		}
	}

	// 使用新的容量管理系统批量获取容量信息
	if len(pathsToCalculate) > 0 {
		usageResults := batchGetCapacityFromManager(pathsToCalculate)

		// 将计算结果分配给对应的基础路径
		for path, usedBytes := range usageResults {
			if basePaths, exists := pathToUserBasePath[path]; exists {
				for _, basePath := range basePaths {
					basePath.UsedBytes = usedBytes
				}
			}
		}
	}
}

// batchGetCapacityFromManager 使用新的容量管理系统批量获取容量信息
func batchGetCapacityFromManager(basePaths []string) map[string]int64 {
	results := make(map[string]int64)

	// 并发获取容量信息，但限制并发数
	const maxConcurrency = 5
	semaphore := make(chan struct{}, maxConcurrency)
	resultChan := make(chan struct {
		path string
		size int64
	}, len(basePaths))

	// 启动获取任务
	for _, path := range basePaths {
		go func(p string) {
			semaphore <- struct{}{} // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 使用新的容量管理系统获取容量
			capacityInfo, err := capacity.GetCapacity(p)
			var size int64 = 0
			if err != nil {
				log.Debugf("Failed to get capacity for base path %s: %v", p, err)
			} else if capacityInfo != nil {
				size = capacityInfo.UsedBytes
			}

			resultChan <- struct {
				path string
				size int64
			}{p, size}
		}(path)
	}

	// 收集结果
	for i := 0; i < len(basePaths); i++ {
		result := <-resultChan
		results[result.path] = result.size
	}

	return results
}

// enhanceUsersWithCapacityInfoFromCache 从缓存获取用户容量信息，不进行实时计算
// 用于快速响应，避免阻塞用户信息获取
func enhanceUsersWithCapacityInfoFromCache(users []*model.User) {
	if len(users) == 0 {
		return
	}

	// 为每个用户的基础路径设置容量信息
	for _, user := range users {
		// 处理多基础路径用户
		if len(user.BasePaths) > 0 {
			for i := range user.BasePaths {
				basePath := &user.BasePaths[i]

				// 如果启用了容量限制，设置限制容量和尝试从缓存获取已使用容量
				if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
					// 设置总容量为限制容量（GB转换为字节）
					basePath.TotalBytes = int64(basePath.CapacityLimitGB * 1024 * 1024 * 1024)

					// 使用新的容量管理系统从缓存获取容量信息
					capacityInfo, err := capacity.GetCapacity(basePath.Path)
					if err != nil {
						log.Debugf("Failed to get capacity from cache for path %s: %v", basePath.Path, err)
						basePath.UsedBytes = 0
					} else if capacityInfo != nil {
						basePath.UsedBytes = capacityInfo.UsedBytes
					} else {
						// 没有缓存，设置为0，等待异步更新
						basePath.UsedBytes = 0
					}
				} else {
					// 未启用容量限制的基础路径，不显示容量信息
					basePath.TotalBytes = 0
					basePath.UsedBytes = 0
				}
			}
		}
	}
}

// enhanceSpecificBasePathCapacity 为指定的基础路径计算容量信息
// 用于单个基础路径的实时刷新，确保数据准确性
func enhanceSpecificBasePathCapacity(user *model.User, basePathId string) {
	if len(user.BasePaths) == 0 {
		return
	}

	log.Debugf("enhanceSpecificBasePathCapacity: Processing basePathId %s for user %s", basePathId, user.Username)
	log.Debugf("enhanceSpecificBasePathCapacity: User has %d base paths", len(user.BasePaths))

	// 查找指定的基础路径
	var targetBasePath *model.UserBasePath
	for i := range user.BasePaths {
		basePath := &user.BasePaths[i]
		log.Debugf("enhanceSpecificBasePathCapacity: Checking base path - ID: %d, Path: %s, Alias: %s",
			basePath.ID, basePath.Path, basePath.Alias)

		// 支持通过ID或路径匹配
		if (basePath.ID != 0 && fmt.Sprintf("%d", basePath.ID) == basePathId) ||
		   basePath.Path == basePathId {
			targetBasePath = basePath
			log.Debugf("enhanceSpecificBasePathCapacity: Found matching base path - ID: %d, Path: %s",
				basePath.ID, basePath.Path)
			break
		}
	}

	if targetBasePath == nil {
		log.Warnf("enhanceSpecificBasePathCapacity: Base path not found: %s", basePathId)
		log.Warnf("enhanceSpecificBasePathCapacity: Available base paths:")
		for i, bp := range user.BasePaths {
			log.Warnf("  [%d] ID: %d, Path: %s, Alias: %s", i, bp.ID, bp.Path, bp.Alias)
		}
		return
	}

	// 只处理启用容量限制的基础路径
	if !targetBasePath.EnableCapacityLimit || targetBasePath.CapacityLimitGB <= 0 {
		log.Debugf("enhanceSpecificBasePathCapacity: Base path %s does not have capacity limit enabled", targetBasePath.Path)
		return
	}

	// 设置总容量为限制容量（GB转换为字节）
	targetBasePath.TotalBytes = int64(targetBasePath.CapacityLimitGB * 1024 * 1024 * 1024)

	// 使用新的容量管理系统获取容量信息
	capacityInfo, err := capacity.GetCapacity(targetBasePath.Path)
	if err != nil {
		log.Warnf("enhanceSpecificBasePathCapacity: Failed to get capacity for base path %s: %v", targetBasePath.Path, err)
		targetBasePath.UsedBytes = 0
	} else if capacityInfo != nil {
		targetBasePath.UsedBytes = capacityInfo.UsedBytes
		log.Debugf("enhanceSpecificBasePathCapacity: Updated capacity for %s: %d/%d bytes",
			targetBasePath.Path, capacityInfo.UsedBytes, targetBasePath.TotalBytes)
	} else {
		targetBasePath.UsedBytes = 0
	}
	log.Debugf("enhanceSpecificBasePathCapacity: Updated cache for %s", targetBasePath.Path)
}

// InitializeUserCapacity 初始化用户容量数据
func InitializeUserCapacity(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	// 检查用户是否有启用容量限制的基础路径
	hasCapacityLimit := false
	var pathsToInitialize []string

	for _, basePath := range user.BasePaths {
		if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
			hasCapacityLimit = true
			pathsToInitialize = append(pathsToInitialize, basePath.Path)
		}
	}

	if !hasCapacityLimit {
		common.ErrorStrResp(c, "用户没有启用容量限制的基础路径", 400)
		return
	}

	log.Infof("InitializeUserCapacity: Starting capacity initialization for user %s, paths: %v", user.Username, pathsToInitialize)

	// 获取容量管理器
	manager := capacity.GetGlobalManager()
	if manager == nil {
		common.ErrorStrResp(c, "容量管理器未初始化", 500)
		return
	}

	// 启动容量初始化
	ctx := context.Background()
	for _, path := range pathsToInitialize {
		if err := manager.InitializeCapacity(ctx, path); err != nil {
			log.Errorf("Failed to initialize capacity for path %s: %v", path, err)
			common.ErrorResp(c, fmt.Errorf("初始化路径 %s 容量失败: %v", path, err), 500)
			return
		}
	}

	common.SuccessResp(c, gin.H{
		"message": "容量初始化已启动",
		"paths":   pathsToInitialize,
	})
}

// GetCapacityInitializationProgress 获取容量初始化进度
func GetCapacityInitializationProgress(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	// 获取容量管理器
	manager := capacity.GetGlobalManager()
	if manager == nil {
		common.ErrorStrResp(c, "容量管理器未初始化", 500)
		return
	}

	// 收集用户所有基础路径的进度
	progressMap := make(map[string]interface{})
	allCompleted := true

	for _, basePath := range user.BasePaths {
		if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
			if progress, exists := manager.GetInitializationProgress(basePath.Path); exists {
				progressMap[basePath.Path] = progress
				if progress.Status != "completed" {
					allCompleted = false
				}
			} else {
				// 没有进度记录，表示未开始初始化
				progressMap[basePath.Path] = gin.H{
					"base_path": basePath.Path,
					"status":    "not_started",
					"progress":  0.0,
				}
				allCompleted = false
			}
		}
	}

	common.SuccessResp(c, gin.H{
		"progress":      progressMap,
		"all_completed": allCompleted,
	})
}

// GetCapacityStatus 获取容量状态
func GetCapacityStatus(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	// 获取容量管理器
	manager := capacity.GetGlobalManager()
	if manager == nil {
		common.ErrorStrResp(c, "容量管理器未初始化", 500)
		return
	}

	// 收集用户所有基础路径的容量状态
	statusMap := make(map[string]interface{})

	for _, basePath := range user.BasePaths {
		if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
			ctx := context.Background()
			capacityInfo, err := manager.GetCapacity(ctx, basePath.Path)
			if err != nil {
				log.Warnf("Failed to get capacity for path %s: %v", basePath.Path, err)
				statusMap[basePath.Path] = gin.H{
					"error": err.Error(),
				}
			} else {
				statusMap[basePath.Path] = gin.H{
					"base_path":           capacityInfo.BasePath,
					"used_bytes":          capacityInfo.UsedBytes,
					"total_files":         capacityInfo.TotalFiles,
					"last_calculated_at":  capacityInfo.LastCalculatedAt,
					"calculation_status":  capacityInfo.CalculationStatus,
					"limit_gb":           basePath.CapacityLimitGB,
					"limit_bytes":        int64(basePath.CapacityLimitGB * 1024 * 1024 * 1024),
				}
			}
		}
	}

	common.SuccessResp(c, gin.H{
		"status": statusMap,
	})
}

// parseCapacityString parses capacity string like "8.5 GB / 100 GB" to bytes
func parseCapacityString(capacityStr string) (used int64, total int64) {
	if capacityStr == "" {
		return 0, 0
	}

	// Split by " / " to get used and total parts
	parts := strings.Split(capacityStr, " / ")
	if len(parts) != 2 {
		return 0, 0
	}

	used = parseSize(strings.TrimSpace(parts[0]))
	total = parseSize(strings.TrimSpace(parts[1]))
	return used, total
}

// parseSize parses size string like "8.5 GB" to bytes
func parseSize(sizeStr string) int64 {
	if sizeStr == "" {
		return 0
	}

	parts := strings.Fields(sizeStr)
	if len(parts) != 2 {
		return 0
	}

	value, err := strconv.ParseFloat(parts[0], 64)
	if err != nil {
		return 0
	}

	unit := strings.ToUpper(parts[1])
	multiplier := int64(1)

	switch unit {
	case "B":
		multiplier = 1
	case "KB":
		multiplier = 1024
	case "MB":
		multiplier = 1024 * 1024
	case "GB":
		multiplier = 1024 * 1024 * 1024
	case "TB":
		multiplier = 1024 * 1024 * 1024 * 1024
	case "PB":
		multiplier = 1024 * 1024 * 1024 * 1024 * 1024
	default:
		return 0
	}

	return int64(value * float64(multiplier))
}

// batchCalculateBasePathUsage 批量计算多个基础路径的使用量，提升性能
func batchCalculateBasePathUsage(basePaths []string) map[string]int64 {
	results := make(map[string]int64)

	// 并发计算，但限制并发数避免系统负载过高
	const maxConcurrency = 3
	semaphore := make(chan struct{}, maxConcurrency)
	resultChan := make(chan struct {
		path string
		size int64
	}, len(basePaths))

	// 启动计算任务
	for _, path := range basePaths {
		go func(p string) {
			semaphore <- struct{}{} // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			size, err := calculateBasePathUsage(p)
			if err != nil {
				log.Debugf("Failed to calculate usage for base path %s: %v", p, err)
				size = 0
			}

			resultChan <- struct {
				path string
				size int64
			}{p, size}
		}(path)
	}

	// 收集结果
	for i := 0; i < len(basePaths); i++ {
		result := <-resultChan
		results[result.path] = result.size
	}

	return results
}

// calculateBasePathUsage 计算指定基础路径下的所有文件总大小
// 返回已使用的字节数
func calculateBasePathUsage(basePath string) (int64, error) {
	// 创建一个带超时的上下文，避免计算过程过长
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用缓存，避免频繁计算
	cacheKey := fmt.Sprintf("base_path_usage_%s", basePath)
	if cached, ok := basePathUsageCache.Load(cacheKey); ok {
		// 检查缓存是否过期（1小时）
		if time.Since(cached.timestamp) < 1*time.Hour {
			log.Debugf("Using cached usage for base path %s: %d bytes", basePath, cached.size)
			return cached.size, nil
		}
		// 缓存过期，删除旧缓存
		basePathUsageCache.Delete(cacheKey)
		log.Debugf("Cache expired for base path %s, will recalculate", basePath)
	} else {
		log.Debugf("No cache found for base path %s, will calculate", basePath)
	}

	// 快速检查：如果路径不存在或无法访问，直接返回0
	_, err := fs.Get(ctx, basePath, &fs.GetArgs{})
	if err != nil {
		log.Debugf("Base path %s not accessible, returning 0 usage: %v", basePath, err)
		return 0, nil
	}

	// 获取基础路径下的所有文件
	var totalSize int64 = 0

	// 使用fs.List获取基础路径下的文件列表
	objs, err := fs.List(ctx, basePath, &fs.ListArgs{})
	if err != nil {
		return 0, fmt.Errorf("failed to list files in base path %s: %w", basePath, err)
	}

	// 递归计算所有文件的大小
	for _, obj := range objs {
		if obj.IsDir() {
			// 对于目录，递归计算
			dirPath := basePath + "/" + obj.GetName()
			dirSize, err := calculateDirSize(ctx, dirPath)
			if err != nil {
				log.Warnf("Failed to calculate size for directory %s: %v", dirPath, err)
				continue
			}
			totalSize += dirSize
		} else {
			// 对于文件，直接加上文件大小
			totalSize += obj.GetSize()
		}
	}

	// 缓存计算结果，有效期1小时
	basePathUsageCache.Store(cacheKey, cacheEntry{
		size:      totalSize,
		timestamp: time.Now(),
	})

	log.Debugf("Calculated usage for base path %s: %d bytes", basePath, totalSize)
	return totalSize, nil
}

// calculateDirSize 递归计算目录大小
func calculateDirSize(ctx context.Context, dirPath string) (int64, error) {
	var totalSize int64 = 0

	// 检查上下文是否已取消
	if ctx.Err() != nil {
		return 0, ctx.Err()
	}

	// 列出目录内容
	objs, err := fs.List(ctx, dirPath, &fs.ListArgs{})
	if err != nil {
		return 0, err
	}

	// 递归计算
	for _, obj := range objs {
		if obj.IsDir() {
			// 递归计算子目录
			subDirPath := dirPath + "/" + obj.GetName()
			subDirSize, err := calculateDirSize(ctx, subDirPath)
			if err != nil {
				log.Warnf("Failed to calculate size for subdirectory %s: %v", subDirPath, err)
				continue
			}
			totalSize += subDirSize
		} else {
			// 加上文件大小
			totalSize += obj.GetSize()
		}
	}

	return totalSize, nil
}

// clearBasePathUsageCache 清除指定基础路径的容量缓存
func clearBasePathUsageCache(basePath string) {
	cacheKey := fmt.Sprintf("base_path_usage_%s", basePath)

	// 检查缓存是否存在
	if _, exists := basePathUsageCache.Load(cacheKey); exists {
		basePathUsageCache.Delete(cacheKey)
		log.Infof("Successfully cleared capacity cache for base path: %s", basePath)
	} else {
		log.Debugf("No cache found for base path: %s", basePath)
	}
}

// clearUserCapacityCache 清除用户所有基础路径的容量缓存
func clearUserCapacityCache(user *model.User) {
	if user == nil {
		return
	}

	// 清除多基础路径用户的缓存
	if len(user.BasePaths) > 0 {
		for _, basePath := range user.BasePaths {
			if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
				clearBasePathUsageCache(basePath.Path)
			}
		}
	} else if user.BasePath != "" {
		// 清除传统单基础路径用户的缓存
		clearBasePathUsageCache(user.BasePath)
	}

	log.Debugf("Cleared all capacity cache for user: %s", user.Username)
}

// clearUserCapacityCacheNew 清除用户所有基础路径的新容量管理系统缓存
func clearUserCapacityCacheNew(user *model.User) {
	if user == nil {
		return
	}

	// 清除多基础路径用户的缓存
	if len(user.BasePaths) > 0 {
		for _, basePath := range user.BasePaths {
			if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
				capacity.InvalidateCache(basePath.Path)
				log.Debugf("Cleared new capacity cache for base path: %s", basePath.Path)
			}
		}
	} else if user.BasePath != "" {
		// 清除传统单基础路径用户的缓存
		capacity.InvalidateCache(user.BasePath)
		log.Debugf("Cleared new capacity cache for base path: %s", user.BasePath)
	}

	log.Debugf("Cleared all new capacity cache for user: %s", user.Username)
}

// clearSpecificBasePathCacheNew 清除用户指定基础路径的新容量管理系统缓存
func clearSpecificBasePathCacheNew(user *model.User, basePathId string) {
	if user == nil || basePathId == "" {
		return
	}

	// 将字符串ID转换为uint
	id, err := strconv.ParseUint(basePathId, 10, 32)
	if err != nil {
		log.Debugf("Invalid base path ID format: %s", basePathId)
		return
	}

	// 查找指定的基础路径
	for _, basePath := range user.BasePaths {
		if basePath.ID == uint(id) && basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
			capacity.InvalidateCache(basePath.Path)
			log.Debugf("Cleared new capacity cache for specific base path: %s (ID: %s)", basePath.Path, basePathId)
			return
		}
	}

	log.Debugf("No matching base path found for ID: %s", basePathId)
}

// clearSpecificBasePathCache 清除用户指定基础路径的容量缓存
func clearSpecificBasePathCache(user *model.User, basePathId string) {
	if user == nil || basePathId == "" {
		return
	}

	// 清除多基础路径用户的指定缓存
	if len(user.BasePaths) > 0 {
		for _, basePath := range user.BasePaths {
			// 通过ID或路径匹配
			if (basePath.ID != 0 && fmt.Sprintf("%d", basePath.ID) == basePathId) ||
			   basePath.Path == basePathId {
				if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
					clearBasePathUsageCache(basePath.Path)
					log.Debugf("Cleared capacity cache for specific base path: %s (user: %s)", basePath.Path, user.Username)
					return
				}
			}
		}
	} else if user.BasePath != "" && user.BasePath == basePathId {
		// 清除传统单基础路径用户的缓存
		clearBasePathUsageCache(user.BasePath)
		log.Debugf("Cleared capacity cache for traditional base path: %s (user: %s)", user.BasePath, user.Username)
	}
}

// getBasePathConfig 根据目标路径获取用户的基础路径配置
func getBasePathConfig(user *model.User, targetPath string) *model.UserBasePath {
	if user == nil {
		return nil
	}

	// 处理多基础路径用户
	if len(user.BasePaths) > 0 {
		// 找到最匹配的基础路径
		var bestMatch *model.UserBasePath
		maxMatchLen := 0

		for i := range user.BasePaths {
			basePath := &user.BasePaths[i]
			// 检查目标路径是否在此基础路径下
			if strings.HasPrefix(targetPath, basePath.Path) {
				if len(basePath.Path) > maxMatchLen {
					maxMatchLen = len(basePath.Path)
					bestMatch = basePath
				}
			}
		}
		return bestMatch
	}

	// 处理传统单基础路径用户
	if user.BasePath != "" && strings.HasPrefix(targetPath, user.BasePath) {
		// 为传统用户创建虚拟的基础路径配置（不启用容量限制）
		return &model.UserBasePath{
			Path:                user.BasePath,
			EnableCapacityLimit: false,
			CapacityLimitGB:     0,
		}
	}

	return nil
}

// checkUploadCapacity 检查上传文件是否会超出容量限制
func checkUploadCapacity(user *model.User, targetPath string, fileSize int64) error {
	// 获取目标路径所属的基础路径配置
	basePathConfig := getBasePathConfig(user, targetPath)
	if basePathConfig == nil {
		// 无法确定基础路径，允许上传（可能是管理员或特殊路径）
		return nil
	}

	// 检查是否启用容量限制
	if !basePathConfig.EnableCapacityLimit || basePathConfig.CapacityLimitGB <= 0 {
		// 未启用容量限制，允许上传
		return nil
	}

	// 计算容量限制（GB转字节）
	capacityLimit := int64(basePathConfig.CapacityLimitGB * 1024 * 1024 * 1024)

	// 使用新的容量管理系统获取当前使用量
	capacityInfo, err := capacity.GetCapacity(basePathConfig.Path)
	var currentUsed int64 = 0
	if err != nil {
		log.Warnf("Failed to get current capacity for path %s: %v", basePathConfig.Path, err)
		// 获取失败时允许上传，避免阻塞正常操作
		return nil
	} else if capacityInfo != nil {
		currentUsed = capacityInfo.UsedBytes
	}

	// 检查当前是否已经超限
	if currentUsed >= capacityLimit {
		usedGB := float64(currentUsed) / (1024 * 1024 * 1024)
		limitGB := basePathConfig.CapacityLimitGB
		usagePercent := float64(currentUsed) / float64(capacityLimit) * 100
		return fmt.Errorf("存储空间已满，无法上传文件。当前已使用：%.2f GB / %.2f GB (%.1f%%)",
			usedGB, limitGB, usagePercent)
	}

	// 检查上传后是否会超限
	afterUploadUsed := currentUsed + fileSize
	if afterUploadUsed > capacityLimit {
		currentUsedGB := float64(currentUsed) / (1024 * 1024 * 1024)
		fileSizeMB := float64(fileSize) / (1024 * 1024)
		afterUsedGB := float64(afterUploadUsed) / (1024 * 1024 * 1024)
		limitGB := basePathConfig.CapacityLimitGB
		afterUsagePercent := float64(afterUploadUsed) / float64(capacityLimit) * 100

		return fmt.Errorf("上传文件后将超出存储容量限制，无法上传。当前使用：%.2f GB / %.2f GB，文件大小：%.2f MB，上传后：%.2f GB / %.2f GB (%.1f%%)",
			currentUsedGB, limitGB, fileSizeMB, afterUsedGB, limitGB, afterUsagePercent)
	}

	// 允许上传
	return nil
}

// CheckCapacityDataExists 检查容量数据是否存在
func CheckCapacityDataExists(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	var pathsNeedInit []string
	var existingPaths []string

	for _, basePath := range user.BasePaths {
		if basePath.EnableCapacityLimit && basePath.CapacityLimitGB > 0 {
			// 检查数据库中是否有有效的容量数据
			if hasValidCapacityData(basePath.Path) {
				existingPaths = append(existingPaths, basePath.Path)
				log.Debugf("CheckCapacityDataExists: Found existing capacity data for path: %s", basePath.Path)
			} else {
				pathsNeedInit = append(pathsNeedInit, basePath.Path)
				log.Debugf("CheckCapacityDataExists: Path needs initialization: %s", basePath.Path)
			}
		}
	}

	log.Infof("CheckCapacityDataExists: User %s - Existing paths: %v, Need init: %v",
		user.Username, existingPaths, pathsNeedInit)

	common.SuccessResp(c, gin.H{
		"paths_need_initialization": pathsNeedInit,
		"paths_with_existing_data":  existingPaths,
		"total_capacity_paths":      len(existingPaths) + len(pathsNeedInit),
	})
}

// hasValidCapacityData 检查指定基础路径是否在数据库中有有效的容量数据
func hasValidCapacityData(basePath string) bool {
	// 获取容量管理器
	manager := capacity.GetGlobalManager()
	if manager == nil {
		log.Warnf("hasValidCapacityData: Capacity manager not initialized")
		return false
	}

	// 直接从数据库获取容量数据（跳过缓存）
	persister := manager.GetPersister()
	if persister == nil {
		log.Warnf("hasValidCapacityData: Persister not available")
		return false
	}

	capacityData, err := persister.Get(basePath)
	if err != nil {
		log.Debugf("hasValidCapacityData: No capacity data found for path %s: %v", basePath, err)
		return false
	}

	if capacityData == nil {
		log.Debugf("hasValidCapacityData: Capacity data is nil for path %s", basePath)
		return false
	}

	// 检查数据是否有效（不是初始化状态且有实际数据）
	isValid := capacityData.CalculationStatus == "completed" &&
		!capacityData.LastCalculatedAt.IsZero() &&
		(capacityData.UsedBytes > 0 || capacityData.TotalFiles > 0)

	log.Debugf("hasValidCapacityData: Path %s - Status: %s, LastCalc: %v, UsedBytes: %d, TotalFiles: %d, Valid: %v",
		basePath, capacityData.CalculationStatus, capacityData.LastCalculatedAt,
		capacityData.UsedBytes, capacityData.TotalFiles, isValid)

	return isValid
}
